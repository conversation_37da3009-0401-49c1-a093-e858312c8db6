using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 设备部件明细DTO
    /// </summary>
    public class EquipmentComponentDetailDto
    {
        /// <summary>
        /// 设备部件关联ID（用于编辑）
        /// </summary>
        public int? EquipmentComponentId { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号ID
        /// </summary>
        public int EquipmentModelId { get; set; }

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string EquipmentModelName { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号分类
        /// </summary>
        public string EquipmentModelCategory { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号品牌
        /// </summary>
        public string? EquipmentModelBrand { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 位置ID
        /// </summary>
        public int LocationId { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        public string LocationName { get; set; } = string.Empty;

        /// <summary>
        /// 设备状态
        /// </summary>
        public int EquipmentStatus { get; set; }

        /// <summary>
        /// 设备状态名称
        /// </summary>
        public string EquipmentStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string? EquipmentSerialNumber { get; set; }

        /// <summary>
        /// 设备资产编号
        /// </summary>
        public string? EquipmentAssetNumber { get; set; }

        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID
        /// </summary>
        public int ComponentCategoryId { get; set; }

        /// <summary>
        /// 部件分类名称
        /// </summary>
        public string ComponentCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 部件型号
        /// </summary>
        public string? ComponentModel { get; set; }

        /// <summary>
        /// 部件规格
        /// </summary>
        public string? ComponentSpecifications { get; set; }

        /// <summary>
        /// 部件供应商
        /// </summary>
        public string? ComponentSupplier { get; set; }

        /// <summary>
        /// 部件品牌
        /// </summary>
        public string? ComponentBrand { get; set; }

        /// <summary>
        /// 部件单位
        /// </summary>
        public string? ComponentUnit { get; set; }

        /// <summary>
        /// 实际数量（从设备部件关联表）
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 标准数量（兼容性保留）
        /// </summary>
        public int StandardQuantity
        {
            get => Quantity;
            set => Quantity = value;
        }

        /// <summary>
        /// 是否必需部件
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 安装日期
        /// </summary>
        public DateTime? InstallDate { get; set; }

        /// <summary>
        /// 上次更换日期
        /// </summary>
        public DateTime? LastReplacementDate { get; set; }

        /// <summary>
        /// 下次更换日期
        /// </summary>
        public DateTime? NextReplacementDate { get; set; }

        /// <summary>
        /// 部件状态：1-正常, 2-需要维护, 3-需要更换, 4-已损坏
        /// </summary>
        public int ComponentStatus { get; set; } = 1;

        /// <summary>
        /// 部件状态名称
        /// </summary>
        public string ComponentStatusName { get; set; } = "正常";

        /// <summary>
        /// 部件类型名称
        /// </summary>
        public string ComponentTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 更换周期描述
        /// </summary>
        public string ReplacementCycleDescription { get; set; } = string.Empty;

        /// <summary>
        /// 维护说明
        /// </summary>
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 当前库存数量
        /// </summary>
        public int ComponentStockQuantity { get; set; }

        /// <summary>
        /// 最小库存数量
        /// </summary>
        public int ComponentMinStockQuantity { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string ComponentStockStatus { get; set; } = string.Empty;

        /// <summary>
        /// 库存状态颜色
        /// </summary>
        public string ComponentStockStatusColor { get; set; } = string.Empty;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? ComponentUnitPrice { get; set; }

        /// <summary>
        /// 总价值（标准数量 * 单价）
        /// </summary>
        public decimal? TotalValue => StandardQuantity * (ComponentUnitPrice ?? 0);

        /// <summary>
        /// 部件描述
        /// </summary>
        public string? ComponentDescription { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 设备部件明细统计DTO
    /// </summary>
    public class EquipmentComponentDetailStatisticsDto
    {
        /// <summary>
        /// 设备总数
        /// </summary>
        public int TotalEquipmentCount { get; set; }

        /// <summary>
        /// 部件类型总数
        /// </summary>
        public int TotalComponentTypeCount { get; set; }

        /// <summary>
        /// 部件总数量
        /// </summary>
        public int TotalComponentQuantity { get; set; }

        /// <summary>
        /// 必需部件数量
        /// </summary>
        public int RequiredComponentCount { get; set; }

        /// <summary>
        /// 可选部件数量
        /// </summary>
        public int OptionalComponentCount { get; set; }

        /// <summary>
        /// 库存不足的部件数量
        /// </summary>
        public int LowStockComponentCount { get; set; }

        /// <summary>
        /// 需要维护的部件数量
        /// </summary>
        public int MaintenanceDueComponentCount { get; set; }

        /// <summary>
        /// 部件总价值
        /// </summary>
        public decimal TotalComponentValue { get; set; }
    }
}
