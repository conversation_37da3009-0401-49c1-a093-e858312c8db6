# 委外加工进度跟踪功能说明（简洁版）

## 功能概述
委外加工进度跟踪功能用于管理和跟踪设备维修过程中需要委外处理的零部件的加工进度。采用简洁的设计，专注于核心业务功能。

## 已实现的组件

### 1. 数据模型（简洁版）
- **OutsourcedProcessingRecord** (`CoreHub.Shared/Models/Database/OutsourcedProcessingRecord.cs`)
  - 委外加工记录的数据库实体模型
  - 包含15个核心字段：供应商信息、加工要求、费用、时间节点等

- **OutsourcedProcessingStatusLog** (`CoreHub.Shared/Models/Database/OutsourcedProcessingStatusLog.cs`)
  - 委外状态变更日志实体
  - 包含7个字段：状态变更记录和操作人信息

- **OutsourcedProcessingDto** (`CoreHub.Shared/Models/Dto/OutsourcedProcessingDto.cs`)
  - 委外加工进度的数据传输对象
  - 用于前端显示和数据交换

### 2. 服务层
- **OutsourcedProcessingService** (`CoreHub.Shared/Services/OutsourcedProcessingService.cs`)
  - 委外加工业务逻辑服务
  - 提供创建、查询、更新委外记录的方法
  - 支持按零件申请查询委外记录
  - 支持更新委外进度状态

### 3. UI组件
- **PartReplacementRecordInput** (`CoreHub.Shared/Components/PartReplacementRecordInput.razor`)
  - 已集成委外加工进度跟踪功能
  - 在零部件更换记录中可以查看和管理委外进度

## 数据库脚本

### 委外加工简洁版数据库脚本.sql
- **用途**: 简洁版的完整建表脚本
- **特点**: 
  - 精简的字段设计，专注核心业务
  - 关联零件申请表（RepairOrderPartRequests）
  - 5种简洁的状态管理
  - 5个关键索引优化查询性能

### 表结构说明

#### OutsourcedProcessingRecords（委外加工记录表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| Id | int | 主键ID |
| PartRequestId | int | 关联的零件申请ID |
| SupplierName | nvarchar(100) | 供应商名称 |
| SupplierContact | nvarchar(50) | 供应商联系人 |
| SupplierPhone | nvarchar(20) | 供应商电话 |
| ProcessingRequirements | nvarchar(1000) | 加工要求 |
| ProcessingCost | decimal(18,2) | 加工费用 |
| EstimatedCompletionDate | datetime2 | 预计完成时间 |
| ActualCompletionDate | datetime2 | 实际完成时间 |
| ProcessingStatus | int | 加工状态 |
| CreatedBy | int | 创建人ID |
| CreatedAt | datetime2 | 创建时间 |
| UpdatedBy | int | 更新人ID |
| UpdatedAt | datetime2 | 更新时间 |
| Remark | nvarchar(1000) | 备注 |

#### OutsourcedProcessingStatusLogs（状态变更日志表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| Id | int | 主键ID |
| ProcessingRecordId | int | 关联的委外记录ID |
| FromStatus | int | 原状态 |
| ToStatus | int | 新状态 |
| UpdatedBy | int | 更新人ID |
| UpdatedAt | datetime2 | 更新时间 |
| Remark | nvarchar(500) | 更新备注 |

## 服务注册
已在以下项目中注册了 `OutsourcedProcessingService`：
- **CoreHub.Web** (`Program.cs`)
- **CoreHub.Maui** (`MauiProgram.cs`)

## 状态管理（简洁版）
委外加工支持5种状态：
- **1=已下单**: 委外订单已下达
- **2=加工中**: 供应商正在加工
- **3=待验收**: 加工完成，等待验收
- **4=已完成**: 验收通过，委外完成
- **5=已取消**: 委外订单取消

## 编译状态
- ✅ CoreHub.Shared 项目编译成功
- ✅ CoreHub.Web 项目编译成功  
- ✅ CoreHub.Maui Android平台编译成功

## 使用说明
1. **数据库准备**: 执行 `委外加工简洁版数据库脚本.sql`
2. **创建委外记录**: 在零件申请中，可以为需要委外的零部件创建委外记录
3. **进度管理**: 通过 `OutsourcedProcessingService` 的方法管理委外进度
4. **状态跟踪**: 前端组件会自动显示委外进度信息和状态变更历史
5. **实时更新**: 支持实时更新委外状态和进度备注

## 功能特点
1. **简洁设计**: 专注核心业务功能，避免过度复杂化
2. **状态管理**: 5种清晰的状态流转
3. **成本跟踪**: 记录委外费用
4. **时间管理**: 预计和实际完成时间对比
5. **审计日志**: 完整的状态变更记录
6. **关联管理**: 与零件申请紧密关联

## 业务流程
1. **零件申请** → 创建需要委外的零件申请
2. **委外下单** → 为零件申请创建委外记录（状态：已下单）
3. **加工进行** → 更新状态为"加工中"
4. **等待验收** → 加工完成后更新为"待验收"
5. **完成验收** → 验收通过后更新为"已完成"

## 后续扩展
- 可以添加委外厂商管理功能
- 可以添加委外进度提醒功能
- 可以添加委外成本统计分析功能
- 可以添加批量委外管理功能