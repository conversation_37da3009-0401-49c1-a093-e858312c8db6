@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using MudBlazor
@using FluentValidation
@inject IEquipmentModelComponentTemplateService TemplateService
@inject IEquipmentModelService EquipmentModelService
@inject IComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@Template" Validation="@(new TemplateValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Template.EquipmentModelId"
                             Label="设备型号"
                             Required="true"
                             Disabled="@IsEdit"
                             For="@(() => Template.EquipmentModelId)">
                        @if (equipmentModels != null)
                        {
                            @foreach (var model in equipmentModels)
                            {
                                <MudSelectItem T="int" Value="@model.Id">@model.Name (@model.Code)</MudSelectItem>
                            }
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Template.ComponentId"
                             Label="部件"
                             Required="true"
                             Disabled="@IsEdit"
                             For="@(() => Template.ComponentId)">
                        @if (components != null)
                        {
                            @foreach (var component in components)
                            {
                                <MudSelectItem T="int" Value="@component.Id">
                                    @component.Name (@component.Code) - @component.CategoryName
                                </MudSelectItem>
                            }
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField T="int" @bind-Value="Template.StandardQuantity"
                                   Label="标准数量"
                                   Required="true"
                                   Min="1"
                                   For="@(() => Template.StandardQuantity)" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSwitch T="bool" @bind-Value="Template.IsRequired"
                             Label="必需部件"
                             Color="Color.Primary"
                             For="@(() => Template.IsRequired)" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField T="int?" @bind-Value="Template.ReplacementCycleDays"
                                   Label="更换周期（天）"
                                   Min="1"
                                   Placeholder="可选，留空表示无固定周期"
                                   For="@(() => Template.ReplacementCycleDays)" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField T="int" @bind-Value="Template.SortOrder"
                                   Label="排序顺序"
                                   For="@(() => Template.SortOrder)" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Template.MaintenanceNotes"
                                Label="维护说明"
                                Lines="3"
                                Placeholder="可选，描述该部件的维护要求和注意事项"
                                For="@(() => Template.MaintenanceNotes)" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Template.Remark"
                                Label="备注"
                                Lines="2"
                                Placeholder="可选，其他说明信息"
                                For="@(() => Template.Remark)" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSwitch T="bool" @bind-Value="Template.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary"
                             For="@(() => Template.IsEnabled)" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" Color="Color.Secondary">
            取消
        </MudButton>
        <MudButton OnClick="Submit" Color="Color.Primary" Variant="Variant.Filled" Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            @(IsEdit ? "更新" : "创建")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public EquipmentModelComponentTemplate Template { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;
    private List<EquipmentModel> equipmentModels = new();
    private List<ComponentDto> components = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        
        if (!IsEdit)
        {
            Template.IsEnabled = true;
            Template.IsRequired = true;
            Template.StandardQuantity = 1;
            Template.SortOrder = 0;
        }
    }

    private async Task LoadData()
    {
        try
        {
            // 加载设备型号
            var equipmentModelList = await EquipmentModelService.GetAllEquipmentModelsAsync();
            equipmentModels = equipmentModelList.Where(em => em.IsEnabled).ToList();

            // 加载部件
            components = await ComponentService.GetAllComponentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result;
            
            if (IsEdit)
            {
                result = await TemplateService.UpdateTemplateAsync(Template);
            }
            else
            {
                result = await TemplateService.CreateTemplateAsync(Template);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add($"模板{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    // 验证器
    public class TemplateValidator : AbstractValidator<EquipmentModelComponentTemplate>
    {
        public TemplateValidator()
        {
            RuleFor(x => x.EquipmentModelId)
                .GreaterThan(0)
                .WithMessage("请选择设备型号");

            RuleFor(x => x.ComponentId)
                .GreaterThan(0)
                .WithMessage("请选择部件");

            RuleFor(x => x.StandardQuantity)
                .GreaterThan(0)
                .WithMessage("标准数量必须大于0");

            RuleFor(x => x.ReplacementCycleDays)
                .GreaterThan(0)
                .When(x => x.ReplacementCycleDays.HasValue)
                .WithMessage("更换周期必须大于0天");

            RuleFor(x => x.MaintenanceNotes)
                .MaximumLength(500)
                .WithMessage("维护说明不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(200)
                .WithMessage("备注不能超过200个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<EquipmentModelComponentTemplate>.CreateWithOptions((EquipmentModelComponentTemplate)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
