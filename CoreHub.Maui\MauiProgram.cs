﻿using Microsoft.Extensions.Logging;
using CoreHub.Shared.Services;
using CoreHub.Shared.Data;
using CoreHub.Services;
using ZXing.Net.Maui;
using ZXing.Net.Maui.Controls;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using CoreHub.Shared.Configuration;
using MudBlazor.Services;
using Microsoft.Extensions.Http;



namespace CoreHub
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseBarcodeReader()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddMudServices();

            // 注册HttpClient服务
            builder.Services.AddHttpClient();

            // 配置HttpClient处理器
            builder.Services.ConfigureAll<HttpClientFactoryOptions>(options =>
            {
                options.HttpMessageHandlerBuilderActions.Add(builder =>
                {
#if DEBUG
                    // 开发环境下忽略SSL证书验证
                    builder.PrimaryHandler = new HttpClientHandler()
                    {
                        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
                    };
#endif
                });
            });

            // 环境配置验证（Android平台）
#if ANDROID
            // 根据编译配置显示当前环境
#if DEBUG
            System.Diagnostics.Debug.WriteLine("=== Android环境：开发环境 ===");
#else
            System.Diagnostics.Debug.WriteLine("=== Android环境：生产环境 ===");
#endif

            System.Diagnostics.Debug.WriteLine($"环境: {EnvironmentConfig.CurrentEnvironment}");
            System.Diagnostics.Debug.WriteLine($"API: {EnvironmentConfig.GetApiBaseUrl()}");
            System.Diagnostics.Debug.WriteLine($"数据库: {EnvironmentConfig.GetConnectionString()}");
#endif

            // 使用统一的配置管理（环境配置已设置完成）
            builder.Configuration.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = EnvironmentConfig.GetConnectionString(),
                ["AuthenticationSettings:UseStoredProcedure"] = Environment.GetEnvironmentVariable("USE_STORED_PROCEDURE") ?? "true"
            });

            // 添加Authorization服务
            builder.Services.AddAuthorizationCore();

            // 注册FormFactor服务
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            // 注册应用程序日志服务
            builder.Services.AddSingleton<IApplicationLogger, MauiApplicationLogger>();

            // 根据不同平台注册不同的服务实现
#if ANDROID
            builder.Services.AddSingleton<INotificationService, Platforms.Android.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Android.QrCodeScannerService>();
            // 注册Android平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Android.AndroidAuthenticationStateStorage>();
            // 注册Android平台的更新服务
            builder.Services.AddSingleton<IClientUpdateService, Platforms.Android.AndroidUpdateService>();
            // 注册更新页面
            builder.Services.AddTransient<Platforms.Android.UpdatePage>();
            // 注册更新UI服务
            builder.Services.AddSingleton<IUpdateUIService, Platforms.Android.AndroidUpdateUIService>();
#elif IOS
            builder.Services.AddSingleton<INotificationService, Platforms.iOS.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.iOS.QrCodeScannerService>();
            // 注册iOS平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.iOS.iOSAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<IUpdateUIService, DefaultUpdateUIService>();
#elif MACCATALYST
            builder.Services.AddSingleton<INotificationService, Platforms.MacCatalyst.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.MacCatalyst.QrCodeScannerService>();
            // 注册MacCatalyst平台的认证状态存储（创建MacCatalyst版本）
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.MacCatalyst.MacCatalystAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<IUpdateUIService, DefaultUpdateUIService>();
#elif WINDOWS
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 注册Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<IUpdateUIService, DefaultUpdateUIService>();
#else
            builder.Services.AddSingleton<INotificationService, Platforms.Windows.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 默认使用Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
            // 注册默认更新UI服务
            builder.Services.AddSingleton<IUpdateUIService, DefaultUpdateUIService>();
#endif

            // 注册数据库上下文
            builder.Services.AddScoped<DatabaseContext>();

            // 根据配置选择认证服务实现（与Web项目完全一致）
            var useStoredProcedure = builder.Configuration.GetValue<bool>("AuthenticationSettings:UseStoredProcedure");
            if (useStoredProcedure)
            {
                builder.Services.AddScoped<IUserAuthenticationService, StoredProcedureAuthenticationService>();
            }
            else
            {
                builder.Services.AddScoped<IUserAuthenticationService, DatabaseAuthenticationService>();
            }

            // 注册用户管理服务
            builder.Services.AddScoped<IUserManagementService, UserManagementService>();

            // 注册菜单服务
            builder.Services.AddScoped<IMenuService, MenuService>();

            // 注册设备管理服务
            builder.Services.AddScoped<IDepartmentService, DepartmentService>();
            builder.Services.AddScoped<IEquipmentModelService, EquipmentModelService>();
            builder.Services.AddScoped<ILocationService, LocationService>();
            builder.Services.AddScoped<IEquipmentService, EquipmentService>();
            builder.Services.AddScoped<IRepairOrderPartRequestService, RepairOrderPartRequestService>();
            builder.Services.AddScoped<IRepairOrderService, RepairOrderService>();
            builder.Services.AddScoped<OutsourcedProcessingService>();

            // 注册设备部件管理服务
            builder.Services.AddScoped<IComponentCategoryService, ComponentCategoryService>();
            builder.Services.AddScoped<IComponentService, ComponentService>();
            builder.Services.AddScoped<IEquipmentModelComponentTemplateService, EquipmentModelComponentTemplateService>();
            builder.Services.AddScoped<IComponentReplacementRecordService, ComponentReplacementRecordService>();
            builder.Services.AddScoped<IEquipmentComponentDetailService, EquipmentComponentDetailService>();
            builder.Services.AddScoped<IEquipmentComponentBatchService, EquipmentComponentBatchService>();

            // 注册权限服务
            builder.Services.AddScoped<IRoleDepartmentPermissionService, RoleDepartmentPermissionService>();
            builder.Services.AddScoped<IRoleDepartmentAssignmentServiceV2, RoleDepartmentAssignmentServiceV2>();
            builder.Services.AddScoped<IMaintenanceDepartmentPermissionService, MaintenanceDepartmentPermissionService>();
            builder.Services.AddScoped<IPermissionValidationService, PermissionValidationService>();

            // 注册部门类型和工种类型服务
            builder.Services.AddScoped<IDepartmentTypeService, DepartmentTypeService>();
            builder.Services.AddScoped<IJobTypeService, JobTypeService>();

            // 注册维修工作流服务
            builder.Services.AddScoped<IMaintenanceDashboardService, MaintenanceDashboardService>();
            builder.Services.AddScoped<IRepairWorkflowService, RepairWorkflowService>();

            // 注册应用更新服务
            builder.Services.AddScoped<IAppUpdateService, AppUpdateService>();

            // 注册持久化认证状态提供器
            builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

#if DEBUG
    		builder.Services.AddBlazorWebViewDeveloperTools();
    		builder.Logging.AddDebug();
#endif

            var app = builder.Build();

            return app;
        }
    }
}
