using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件更换记录服务实现
    /// </summary>
    public class ComponentReplacementRecordService : IComponentReplacementRecordService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<ComponentReplacementRecordService> _logger;

        public ComponentReplacementRecordService(
            DatabaseContext dbContext,
            ILogger<ComponentReplacementRecordService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<ComponentReplacementRecord>> GetAllRecordsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<RepairOrder>((r, ro) => r.RepairOrderId == ro.Id)
                    .LeftJoin<Equipment>((r, ro, e) => r.EquipmentId == e.Id)
                    .LeftJoin<Component>((r, ro, e, c) => r.ComponentId == c.Id)
                    .LeftJoin<User>((r, ro, e, c, u) => r.ReplacedBy == u.Id)
                    .Select((r, ro, e, c, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        RepairOrder = ro,
                        Equipment = e,
                        Component = c,
                        ReplacedByUser = u
                    })
                    .OrderByDescending(r => r.ReplacedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有更换记录失败");
                throw;
            }
        }

        public async Task<ComponentReplacementRecord?> GetRecordByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<RepairOrder>((r, ro) => r.RepairOrderId == ro.Id)
                    .LeftJoin<Equipment>((r, ro, e) => r.EquipmentId == e.Id)
                    .LeftJoin<Component>((r, ro, e, c) => r.ComponentId == c.Id)
                    .LeftJoin<User>((r, ro, e, c, u) => r.ReplacedBy == u.Id)
                    .Where(r => r.Id == id)
                    .Select((r, ro, e, c, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        RepairOrder = ro,
                        Equipment = e,
                        Component = c,
                        ReplacedByUser = u
                    })
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取更换记录失败: {id}", id);
                throw;
            }
        }

        public async Task<List<ComponentReplacementRecord>> GetRecordsByRepairOrderAsync(int repairOrderId)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<Equipment>((r, e) => r.EquipmentId == e.Id)
                    .LeftJoin<Component>((r, e, c) => r.ComponentId == c.Id)
                    .LeftJoin<User>((r, e, c, u) => r.ReplacedBy == u.Id)
                    .Where(r => r.RepairOrderId == repairOrderId)
                    .Select((r, e, c, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        Equipment = e,
                        Component = c,
                        ReplacedByUser = u
                    })
                    .OrderBy(r => r.ReplacedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据维修工单获取更换记录失败: {repairOrderId}", repairOrderId);
                throw;
            }
        }

        public async Task<List<ComponentReplacementRecord>> GetRecordsByEquipmentAsync(int equipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<RepairOrder>((r, ro) => r.RepairOrderId == ro.Id)
                    .LeftJoin<Component>((r, ro, c) => r.ComponentId == c.Id)
                    .LeftJoin<User>((r, ro, c, u) => r.ReplacedBy == u.Id)
                    .Where(r => r.EquipmentId == equipmentId)
                    .Select((r, ro, c, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        RepairOrder = ro,
                        Component = c,
                        ReplacedByUser = u
                    })
                    .OrderByDescending(r => r.ReplacedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备获取更换记录失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<ComponentReplacementRecord>> GetRecordsByComponentAsync(int componentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<RepairOrder>((r, ro) => r.RepairOrderId == ro.Id)
                    .LeftJoin<Equipment>((r, ro, e) => r.EquipmentId == e.Id)
                    .LeftJoin<User>((r, ro, e, u) => r.ReplacedBy == u.Id)
                    .Where(r => r.ComponentId == componentId)
                    .Select((r, ro, e, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        RepairOrder = ro,
                        Equipment = e,
                        ReplacedByUser = u
                    })
                    .OrderByDescending(r => r.ReplacedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据部件获取更换记录失败: {componentId}", componentId);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateRecordAsync(ComponentReplacementRecord record)
        {
            try
            {
                // 验证维修工单是否存在
                var repairOrder = await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.Id == record.RepairOrderId)
                    .FirstAsync();
                if (repairOrder == null)
                {
                    return (false, "维修工单不存在");
                }

                // 验证设备是否存在
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == record.EquipmentId)
                    .FirstAsync();
                if (equipment == null)
                {
                    return (false, "设备不存在");
                }

                // 验证部件是否存在
                var component = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == record.ComponentId && c.IsEnabled)
                    .FirstAsync();
                if (component == null)
                {
                    return (false, "部件不存在或已禁用");
                }

                // 检查库存是否充足
                if (component.StockQuantity < record.Quantity)
                {
                    return (false, $"部件库存不足，当前库存：{component.StockQuantity}，需要：{record.Quantity}");
                }

                // 计算总价
                if (record.UnitPrice.HasValue)
                {
                    record.TotalPrice = record.UnitPrice.Value * record.Quantity;
                }

                record.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(record).ExecuteReturnIdentityAsync();

                // 更新部件库存
                component.StockQuantity -= record.Quantity;
                await _dbContext.Db.Updateable(component)
                    .UpdateColumns(c => c.StockQuantity)
                    .ExecuteCommandAsync();

                _logger.LogInformation("创建部件更换记录成功: 维修工单{repairOrderId} - 部件{componentId}", record.RepairOrderId, record.ComponentId);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建部件更换记录失败: 维修工单{repairOrderId} - 部件{componentId}", record.RepairOrderId, record.ComponentId);
                return (false, $"创建更换记录失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRecordAsync(ComponentReplacementRecord record)
        {
            try
            {
                // 获取原记录
                var originalRecord = await GetRecordByIdAsync(record.Id);
                if (originalRecord == null)
                {
                    return (false, "更换记录不存在");
                }

                // 如果数量发生变化，需要调整库存
                if (originalRecord.Quantity != record.Quantity || originalRecord.ComponentId != record.ComponentId)
                {
                    // 恢复原部件库存
                    var originalComponent = await _dbContext.Db.Queryable<Component>()
                        .Where(c => c.Id == originalRecord.ComponentId)
                        .FirstAsync();
                    if (originalComponent != null)
                    {
                        originalComponent.StockQuantity += originalRecord.Quantity;
                        await _dbContext.Db.Updateable(originalComponent)
                            .UpdateColumns(c => c.StockQuantity)
                            .ExecuteCommandAsync();
                    }

                    // 扣减新部件库存
                    var newComponent = await _dbContext.Db.Queryable<Component>()
                        .Where(c => c.Id == record.ComponentId && c.IsEnabled)
                        .FirstAsync();
                    if (newComponent == null)
                    {
                        return (false, "部件不存在或已禁用");
                    }

                    if (newComponent.StockQuantity < record.Quantity)
                    {
                        return (false, $"部件库存不足，当前库存：{newComponent.StockQuantity}，需要：{record.Quantity}");
                    }

                    newComponent.StockQuantity -= record.Quantity;
                    await _dbContext.Db.Updateable(newComponent)
                        .UpdateColumns(c => c.StockQuantity)
                        .ExecuteCommandAsync();
                }

                // 重新计算总价
                if (record.UnitPrice.HasValue)
                {
                    record.TotalPrice = record.UnitPrice.Value * record.Quantity;
                }

                record.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(record).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新部件更换记录成功: {id}", record.Id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "更换记录不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新部件更换记录失败: {id}", record.Id);
                return (false, $"更新更换记录失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteRecordAsync(int id)
        {
            try
            {
                // 获取记录信息
                var record = await GetRecordByIdAsync(id);
                if (record == null)
                {
                    return (false, "更换记录不存在");
                }

                // 恢复部件库存
                var component = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == record.ComponentId)
                    .FirstAsync();
                if (component != null)
                {
                    component.StockQuantity += record.Quantity;
                    await _dbContext.Db.Updateable(component)
                        .UpdateColumns(c => c.StockQuantity)
                        .ExecuteCommandAsync();
                }

                var result = await _dbContext.Db.Deleteable<ComponentReplacementRecord>()
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除部件更换记录成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "更换记录不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除部件更换记录失败: {id}", id);
                return (false, $"删除更换记录失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int CreatedCount)> BatchCreateRecordsAsync(List<ComponentReplacementRecord> records)
        {
            try
            {
                var createdCount = 0;
                var errors = new List<string>();

                foreach (var record in records)
                {
                    var result = await CreateRecordAsync(record);
                    if (result.IsSuccess)
                    {
                        createdCount++;
                    }
                    else
                    {
                        errors.Add($"部件{record.ComponentId}: {result.ErrorMessage}");
                    }
                }

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                _logger.LogInformation("批量创建部件更换记录完成: 成功{created}个, 失败{failed}个", createdCount, errors.Count);

                return (true, errorMessage, createdCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量创建部件更换记录失败");
                return (false, $"批量创建更换记录失败: {ex.Message}", 0);
            }
        }

        public async Task<Dictionary<int, int>> GetEquipmentComponentReplacementStatisticsAsync(int equipmentId)
        {
            try
            {
                var statistics = await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .Where(r => r.EquipmentId == equipmentId)
                    .GroupBy(r => r.ComponentId)
                    .Select(g => new { ComponentId = g.ComponentId, Count = SqlFunc.AggregateSum(g.Quantity) })
                    .ToListAsync();

                return statistics.ToDictionary(s => s.ComponentId, s => s.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备部件更换统计失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<Dictionary<int, int>> GetComponentReplacementFrequencyAsync()
        {
            try
            {
                var statistics = await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .GroupBy(r => r.ComponentId)
                    .Select(g => new { ComponentId = g.ComponentId, Count = SqlFunc.AggregateSum(g.Quantity) })
                    .ToListAsync();

                return statistics.ToDictionary(s => s.ComponentId, s => s.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部件更换频率统计失败");
                throw;
            }
        }

        public async Task<List<ComponentReplacementRecord>> GetRecordsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .LeftJoin<RepairOrder>((r, ro) => r.RepairOrderId == ro.Id)
                    .LeftJoin<Equipment>((r, ro, e) => r.EquipmentId == e.Id)
                    .LeftJoin<Component>((r, ro, e, c) => r.ComponentId == c.Id)
                    .LeftJoin<User>((r, ro, e, c, u) => r.ReplacedBy == u.Id)
                    .Where(r => r.ReplacedAt >= startDate && r.ReplacedAt <= endDate)
                    .Select((r, ro, e, c, u) => new ComponentReplacementRecord
                    {
                        Id = r.Id,
                        RepairOrderId = r.RepairOrderId,
                        EquipmentId = r.EquipmentId,
                        ComponentId = r.ComponentId,
                        Quantity = r.Quantity,
                        Reason = r.Reason,
                        OldComponentCondition = r.OldComponentCondition,
                        ReplacedBy = r.ReplacedBy,
                        ReplacedAt = r.ReplacedAt,
                        UnitPrice = r.UnitPrice,
                        TotalPrice = r.TotalPrice,
                        CreatedAt = r.CreatedAt,
                        Remark = r.Remark,
                        RepairOrder = ro,
                        Equipment = e,
                        Component = c,
                        ReplacedByUser = u
                    })
                    .OrderByDescending(r => r.ReplacedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据时间范围获取更换记录失败: {startDate} - {endDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<decimal> GetMaintenanceCostByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var totalCost = await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .Where(r => r.ReplacedAt >= startDate && r.ReplacedAt <= endDate && r.TotalPrice.HasValue)
                    .SumAsync(r => r.TotalPrice.Value);

                return totalCost;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修成本统计失败: {startDate} - {endDate}", startDate, endDate);
                throw;
            }
        }

        public async Task<List<EquipmentModelComponentTemplate>> GetStandardComponentsByEquipmentAsync(int equipmentId)
        {
            try
            {
                // 获取设备信息
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == equipmentId)
                    .FirstAsync();

                if (equipment == null)
                {
                    return new List<EquipmentModelComponentTemplate>();
                }

                // 获取设备型号的标准部件模板
                return await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .LeftJoin<Component>((t, c) => t.ComponentId == c.Id)
                    .LeftJoin<ComponentCategory>((t, c, cc) => c.CategoryId == cc.Id)
                    .Where(t => t.EquipmentModelId == equipment.ModelId && t.IsEnabled)
                    .Select((t, c, cc) => new EquipmentModelComponentTemplate
                    {
                        Id = t.Id,
                        EquipmentModelId = t.EquipmentModelId,
                        ComponentId = t.ComponentId,
                        StandardQuantity = t.StandardQuantity,
                        IsRequired = t.IsRequired,
                        ReplacementCycleDays = t.ReplacementCycleDays,
                        MaintenanceNotes = t.MaintenanceNotes,
                        SortOrder = t.SortOrder,
                        IsEnabled = t.IsEnabled,
                        Component = c
                    })
                    .OrderBy(t => t.SortOrder)
                    .OrderBy(t => t.Component.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备标准部件清单失败: {equipmentId}", equipmentId);
                throw;
            }
        }
    }
}
