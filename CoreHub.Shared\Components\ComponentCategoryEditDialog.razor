@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IComponentCategoryService ComponentCategoryService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@category" Validation="@(new ComponentCategoryValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="category.Code"
                                For="@(() => category.Code)"
                                Label="分类编码"
                                Placeholder="请输入分类编码"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="category.Name"
                                For="@(() => category.Name)"
                                Label="分类名称"
                                Placeholder="请输入分类名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int?" @bind-Value="category.ParentId"
                             Label="父级分类"
                             Placeholder="选择父级分类（可选）"
                             Clearable="true">
                        @foreach (var cat in GetAvailableParentCategories())
                        {
                            <MudSelectItem T="int?" Value="@cat.Id">@cat.FullName</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="category.SortOrder"
                                   For="@(() => category.SortOrder)"
                                   Label="排序顺序"
                                   Min="0"
                                   Max="9999" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="category.Description"
                                For="@(() => category.Description)"
                                Label="分类描述"
                                Placeholder="请输入分类描述（可选）"
                                Lines="3" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch @bind-Value="category.IsEnabled"
                             For="@(() => category.IsEnabled)"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="category.Remark"
                                For="@(() => category.Remark)"
                                Label="备注"
                                Placeholder="请输入备注（可选）"
                                Lines="2" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@isSubmitting">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public ComponentCategory Category { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public List<ComponentCategory> AllCategories { get; set; } = new();

    private ComponentCategory category = new();
    private MudForm form = null!;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        if (IsEdit)
        {
            // 编辑模式：复制现有数据
            category = new ComponentCategory
            {
                Id = Category.Id,
                Code = Category.Code,
                Name = Category.Name,
                ParentId = Category.ParentId,
                Level = Category.Level,
                SortOrder = Category.SortOrder,
                Description = Category.Description,
                IsEnabled = Category.IsEnabled,
                Remark = Category.Remark
            };
        }
        else
        {
            // 新建模式：初始化默认值
            category = new ComponentCategory
            {
                IsEnabled = true,
                SortOrder = 0
            };
        }
    }

    private List<ComponentCategory> GetAvailableParentCategories()
    {
        var result = new List<ComponentCategory>();
        
        if (IsEdit)
        {
            // 编辑模式：排除自己和自己的子分类
            result = GetFlattenedCategories(AllCategories)
                .Where(c => c.Id != category.Id && !IsDescendantOf(c, category.Id))
                .ToList();
        }
        else
        {
            // 新建模式：所有分类都可选
            result = GetFlattenedCategories(AllCategories);
        }

        return result;
    }

    private List<ComponentCategory> GetFlattenedCategories(List<ComponentCategory> tree)
    {
        var result = new List<ComponentCategory>();
        foreach (var cat in tree)
        {
            result.Add(cat);
            if (cat.Children.Any())
            {
                result.AddRange(GetFlattenedCategories(cat.Children));
            }
        }
        return result;
    }

    private bool IsDescendantOf(ComponentCategory category, int ancestorId)
    {
        var current = category;
        while (current.ParentId.HasValue)
        {
            if (current.ParentId.Value == ancestorId)
                return true;
            
            current = AllCategories.FirstOrDefault(c => c.Id == current.ParentId.Value);
            if (current == null) break;
        }
        return false;
    }

    private async Task Submit()
    {
        try
        {
            await form.Validate();
            if (!form.IsValid) return;

            isSubmitting = true;
            StateHasChanged();

            var result = IsEdit 
                ? await ComponentCategoryService.UpdateCategoryAsync(category)
                : await ComponentCategoryService.CreateCategoryAsync(category);

            if (result.IsSuccess)
            {
                Snackbar.Add($"分类{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add(result.ErrorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    public class ComponentCategoryValidator : AbstractValidator<ComponentCategory>
    {
        public ComponentCategoryValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("分类编码不能为空")
                .MaximumLength(50).WithMessage("分类编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("分类名称不能为空")
                .MaximumLength(100).WithMessage("分类名称长度不能超过100个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("分类描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");

            RuleFor(x => x.SortOrder)
                .GreaterThanOrEqualTo(0).WithMessage("排序顺序不能小于0");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<ComponentCategory>.CreateWithOptions((ComponentCategory)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
