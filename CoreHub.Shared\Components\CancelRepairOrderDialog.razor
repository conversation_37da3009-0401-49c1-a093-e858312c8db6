@using CoreHub.Shared.Services
@using FluentValidation
@inject IRepairOrderService RepairOrderService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="Icons.Material.Filled.Cancel" Class="mr-2" />
                    作废报修单 - @OrderNumber
                </MudText>
            </MudItem>

            <MudItem xs="12">
                <MudAlert Severity="Severity.Warning" Class="mb-4">
                    <MudText><strong>注意：</strong>作废后的报修单无法恢复，请确认是否继续操作。</MudText>
                </MudAlert>
            </MudItem>

            <MudItem xs="12">
                <MudForm @ref="form" Model="@cancelModel" Validation="@(new CancelModelValidator())">
                    <MudTextField @bind-Value="cancelModel.CancelReason"
                                For="@(() => cancelModel.CancelReason)"
                                Label="作废原因"
                                Required="true"
                                Lines="4"
                                Placeholder="请说明作废原因..."
                                HelperText="请详细说明作废此报修单的原因"
                                Immediate="true" />
                </MudForm>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Warning" 
                 Variant="Variant.Filled" 
                 StartIcon="Icons.Material.Filled.Cancel"
                 OnClick="ConfirmCancel"
                 Disabled="@cancelling">
            @if (cancelling)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">作废中...</MudText>
            }
            else
            {
                <MudText>确认作废</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public int RepairOrderId { get; set; }
    [Parameter] public string OrderNumber { get; set; } = string.Empty;

    private MudForm form = null!;
    private CancelModel cancelModel = new();
    private bool cancelling = false;

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task ConfirmCancel()
    {
        await form.Validate();
        if (!form.IsValid) return;

        cancelling = true;
        try
        {
            var result = await RepairOrderService.CancelRepairOrderAsync(RepairOrderId, cancelModel.CancelReason);
            if (result.IsSuccess)
            {
                Snackbar.Add("报修单已作废", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"作废失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"作废失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            cancelling = false;
        }
    }

    public class CancelModel
    {
        public string CancelReason { get; set; } = string.Empty;
    }

    public class CancelModelValidator : AbstractValidator<CancelModel>
    {
        public CancelModelValidator()
        {
            RuleFor(x => x.CancelReason)
                .NotEmpty().WithMessage("请填写作废原因")
                .MinimumLength(5).WithMessage("作废原因至少需要5个字符")
                .MaximumLength(500).WithMessage("作废原因不能超过500个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<CancelModel>.CreateWithOptions((CancelModel)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
