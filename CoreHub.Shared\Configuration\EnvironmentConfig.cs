namespace CoreHub.Shared.Configuration
{
    /// <summary>
    /// 环境配置管理 - 提供统一的只读配置接口
    /// 注意：此类只负责读取配置，设置配置请使用 IPlatformEnvironmentManager
    /// </summary>
    public static class EnvironmentConfig
    {
        #region 常量定义

        /// <summary>
        /// 环境名称常量
        /// </summary>
        public static class Environments
        {
            public const string Development = "Development";
            public const string Production = "Production";
            public const string Staging = "Staging";
        }

        /// <summary>
        /// 环境变量名称常量
        /// </summary>
        public static class EnvironmentVariables
        {
            public const string Environment = "COREHUB_ENVIRONMENT";
            public const string ApiBaseUrl = "COREHUB_API_BASE_URL";
            public const string ConnectionString = "COREHUB_CONNECTION_STRING";
            public const string UseHttpsRedirection = "COREHUB_USE_HTTPS_REDIRECTION";
            public const string EnableVerboseLogging = "COREHUB_ENABLE_VERBOSE_LOGGING";
        }

        /// <summary>
        /// API地址常量
        /// </summary>
        public static class ApiUrls
        {
            public const string Development = "http://************:8080";
            public const string Staging = "https://api-staging.saintyeartex.com:8081";
            public const string Production = "https://api.saintyeartex.com:8081";
        }

        /// <summary>
        /// 数据库连接字符串常量
        /// </summary>
        public static class ConnectionStrings
        {
            public const string Development = "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True";
            public const string Staging = "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True";
            public const string Production = "Server=*************;Database=CoreHub;User Id=sa;Password=*********;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True";
        }

        #endregion
        /// <summary>
        /// 当前环境名称
        /// </summary>
        public static string CurrentEnvironment => GetCurrentEnvironment();

        /// <summary>
        /// 是否为生产环境
        /// </summary>
        public static bool IsProduction => CurrentEnvironment.Equals(Environments.Production, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// 是否为开发环境
        /// </summary>
        public static bool IsDevelopment => CurrentEnvironment.Equals(Environments.Development, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// 是否为测试环境
        /// </summary>
        public static bool IsStaging => CurrentEnvironment.Equals(Environments.Staging, StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// 获取API基础URL
        /// </summary>
        public static string GetApiBaseUrl()
        {
            // 1. 优先从环境变量获取
            var envApiUrl = Environment.GetEnvironmentVariable(EnvironmentVariables.ApiBaseUrl);
            if (!string.IsNullOrEmpty(envApiUrl))
            {
                return envApiUrl;
            }

            // 2. 根据环境返回默认URL
            return CurrentEnvironment switch
            {
                Environments.Development => ApiUrls.Development,
                Environments.Staging => ApiUrls.Staging,
                Environments.Production => ApiUrls.Production,
                _ => ApiUrls.Development
            };
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        public static string GetConnectionString()
        {
            // 1. 优先从环境变量获取
            var envConnectionString = Environment.GetEnvironmentVariable(EnvironmentVariables.ConnectionString);
            if (!string.IsNullOrEmpty(envConnectionString))
            {
                return envConnectionString;
            }

            // 2. 根据环境返回默认连接字符串
            return CurrentEnvironment switch
            {
                Environments.Development => ConnectionStrings.Development,
                Environments.Staging => ConnectionStrings.Staging,
                Environments.Production => ConnectionStrings.Production,
                _ => ConnectionStrings.Development
            };
        }

        /// <summary>
        /// 是否启用HTTPS重定向
        /// </summary>
        public static bool UseHttpsRedirection()
        {
            var envValue = Environment.GetEnvironmentVariable(EnvironmentVariables.UseHttpsRedirection);
            if (bool.TryParse(envValue, out var result))
            {
                return result;
            }
            return IsProduction; // 生产环境默认启用HTTPS
        }

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public static bool EnableVerboseLogging()
        {
            var envValue = Environment.GetEnvironmentVariable(EnvironmentVariables.EnableVerboseLogging);
            if (bool.TryParse(envValue, out var result))
            {
                return result;
            }
            return IsDevelopment; // 开发环境默认启用详细日志
        }

        /// <summary>
        /// 获取当前环境
        /// </summary>
        private static string GetCurrentEnvironment()
        {
            // 1. 优先从环境变量获取
            var envVar = Environment.GetEnvironmentVariable(EnvironmentVariables.Environment);
            if (!string.IsNullOrEmpty(envVar))
            {
                return envVar;
            }

            // 2. 默认值
#if DEBUG
            return Environments.Development;
#else
            return Environments.Production;
#endif
        }

    }
}
