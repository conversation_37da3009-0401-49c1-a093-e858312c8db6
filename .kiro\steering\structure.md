---
inclusion: always
---

# 项目结构与架构规范

## 解决方案架构

CoreHub 采用共享库架构，三层项目结构：

- **CoreHub.Shared** - 共享业务逻辑、组件和模型
- **CoreHub.Web** - Blazor Server Web 应用
- **CoreHub.Maui** - 跨平台移动/桌面应用

## 关键架构原则

### 依赖注入模式
- 所有服务必须通过 DI 容器注册
- 接口定义在 CoreHub.Shared，实现可在各项目中
- 服务注册位置：`Program.cs` (Web) 或 `MauiProgram.cs` (MAUI)

### 服务层架构
- 业务逻辑封装在服务类中，位于 `CoreHub.Shared/Services/`
- 接口命名：`I{ServiceName}Service`
- 实现命名：`{ServiceName}Service`
- 平台特定服务：`{Platform}{ServiceName}Service`

### 数据访问层
- 使用 SqlSugar ORM 进行数据库操作
- 数据库上下文：`CoreHub.Shared/Data/DatabaseContext`
- 实体模型：`CoreHub.Shared/Models/`
- 所有数据库操作必须使用异步方法

### 组件开发规范
- 共享组件：`CoreHub.Shared/Components/`
- 平台特定组件：各项目的 `Components/` 文件夹
- 组件参数使用 `[Parameter]` 特性
- 事件回调使用 `EventCallback<T>`

## 文件命名约定

### 服务文件
```
I{Name}Service.cs          # 接口定义
{Name}Service.cs           # 标准实现
{Platform}{Name}Service.cs # 平台特定实现
```

### 组件文件
```
{Name}.razor               # Blazor 组件
{Name}.razor.cs           # 代码后置文件
{Name}Layout.razor        # 布局组件
```

### 模型文件
```
{EntityName}.cs           # 实体模型
{Name}Dto.cs             # 数据传输对象
{Name}ViewModel.cs       # 视图模型
```

## 代码组织规则

### 共享代码放置
- 业务逻辑 → `CoreHub.Shared/Services/`
- 数据模型 → `CoreHub.Shared/Models/`
- UI 组件 → `CoreHub.Shared/Components/`
- 工具类 → `CoreHub.Shared/Utils/`

### 平台特定代码
- Web 特定 → `CoreHub.Web/Services/`、`CoreHub.Web/Components/`
- MAUI 特定 → `CoreHub.Maui/Services/`、`CoreHub.Maui/Platforms/`

### 配置管理
- Web：分层 appsettings.json 文件
- MAUI：`DatabaseConfig.GetConfigurationData()` 方法
- 敏感信息使用环境变量或用户机密

## 开发约定

### 异步编程
- 所有 I/O 操作必须使用 async/await
- 数据库操作方法以 `Async` 结尾
- 避免同步调用异步方法

### 错误处理
- 使用 Serilog 进行结构化日志记录
- 关键操作必须包含 try-catch 块
- 异常信息使用中文描述

### 权限控制
- 基于角色的访问控制 (RBAC)
- 权限检查在服务层实现
- 菜单和功能基于权限动态渲染

## 构建和部署规范

### 排除文件
- `bin/`、`obj/` 文件夹
- `logs/` 目录内容
- `wwwroot/updates/` 运行时生成目录

### 平台构建
```bash
# Web 应用
dotnet run --project CoreHub.Web

# MAUI 应用（指定平台）
dotnet build CoreHub.Maui -f net8.0-android
```