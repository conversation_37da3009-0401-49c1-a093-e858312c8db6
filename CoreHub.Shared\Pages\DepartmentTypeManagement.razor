@page "/department-type-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IDepartmentTypeService DepartmentTypeService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>部门类型管理</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Category" Class="mr-2" />
                    部门类型管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索部门类型名称或编码..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadDepartmentTypes">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增部门类型
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="DepartmentType" 
                           Items="@filteredDepartmentTypes" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.Code" Title="类型编码" />
                        <PropertyColumn Property="x => x.Name" Title="类型名称" />
                        <PropertyColumn Property="x => x.Description" Title="描述" />
                        <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteDepartmentType(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private List<DepartmentType> departmentTypes = new();
    private List<DepartmentType> filteredDepartmentTypes = new();
    private bool loading = false;
    private string searchText = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartmentTypes();
    }

    private async Task LoadDepartmentTypes()
    {
        loading = true;
        try
        {
            departmentTypes = await DepartmentTypeService.GetAllDepartmentTypesAsync();
            FilterDepartmentTypes();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门类型数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private void FilterDepartmentTypes()
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            filteredDepartmentTypes = departmentTypes.ToList();
        }
        else
        {
            filteredDepartmentTypes = departmentTypes.Where(dt => 
                dt.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                dt.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (dt.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterDepartmentTypes();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<DepartmentTypeEditDialog>
        {
            { x => x.DepartmentType, new DepartmentType() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<DepartmentTypeEditDialog>("新增部门类型", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadDepartmentTypes();
        }
    }

    private async Task OpenEditDialog(DepartmentType departmentType)
    {
        var parameters = new DialogParameters<DepartmentTypeEditDialog>
        {
            { x => x.DepartmentType, departmentType },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<DepartmentTypeEditDialog>("编辑部门类型", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadDepartmentTypes();
        }
    }

    private async Task ToggleStatus(DepartmentType departmentType)
    {
        try
        {
            // 切换状态
            departmentType.IsEnabled = !departmentType.IsEnabled;
            departmentType.UpdatedAt = DateTime.Now;
            
            var result = await DepartmentTypeService.UpdateDepartmentTypeAsync(departmentType);
            if (result)
            {
                Snackbar.Add($"部门类型状态已{(departmentType.IsEnabled ? "启用" : "禁用")}", Severity.Success);
                await LoadDepartmentTypes();
            }
            else
            {
                // 恢复状态
                departmentType.IsEnabled = !departmentType.IsEnabled;
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            // 恢复状态
            departmentType.IsEnabled = !departmentType.IsEnabled;
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteDepartmentType(DepartmentType departmentType)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除部门类型 '{departmentType.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await DepartmentTypeService.DeleteDepartmentTypeAsync(departmentType.Id);
                if (result)
                {
                    Snackbar.Add("部门类型删除成功", Severity.Success);
                    await LoadDepartmentTypes();
                }
                else
                {
                    Snackbar.Add("删除失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
