{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:10973", "sslPort": 44359}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "http://localhost:8080", "applicationUrl": "http://localhost:8080", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "https://localhost:8081", "applicationUrl": "https://localhost:8081;http://localhost:8080", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}