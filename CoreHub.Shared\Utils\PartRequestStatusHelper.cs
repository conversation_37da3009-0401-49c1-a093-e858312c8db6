using MudBlazor;

namespace CoreHub.Shared.Utils
{
    /// <summary>
    /// 零件申请状态帮助类
    /// 统一管理零件申请的状态定义、名称、颜色等
    /// </summary>
    public static class PartRequestStatusHelper
    {
        #region 状态常量定义

        /// <summary>
        /// 申请中 - 初始状态，可编辑删除
        /// </summary>
        public const int Pending = 1;

        /// <summary>
        /// 已领用 - 已从仓库领取，等待安装
        /// </summary>
        public const int Issued = 2;

        /// <summary>
        /// 已安装 - 安装完成，流程结束
        /// </summary>
        public const int Installed = 3;

        /// <summary>
        /// 无需申请 - 维修人员已有零件，直接使用
        /// </summary>
        public const int NotRequired = 4;

        /// <summary>
        /// 委外加工 - 零件需要外厂加工
        /// </summary>
        public const int OutsourcedProcessing = 5;

        #endregion

        #region 状态名称映射

        /// <summary>
        /// 获取状态对应的中文名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态名称</returns>
        public static string GetStatusName(int status)
        {
            return status switch
            {
                Pending => "申请中",
                Issued => "已领用",
                Installed => "已安装",
                NotRequired => "无需申请",
                OutsourcedProcessing => "委外加工",
                _ => "未知"
            };
        }

        #endregion

        #region 状态颜色映射

        /// <summary>
        /// 获取状态对应的颜色
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>MudBlazor颜色</returns>
        public static Color GetStatusColor(int status)
        {
            return status switch
            {
                Pending => Color.Warning,           // 申请中 - 橙色
                Issued => Color.Primary,            // 已领用 - 蓝色
                Installed => Color.Success,         // 已安装 - 绿色
                NotRequired => Color.Info,          // 无需申请 - 蓝绿色
                OutsourcedProcessing => Color.Secondary, // 委外加工 - 紫色
                _ => Color.Default
            };
        }

        #endregion

        #region 状态验证

        /// <summary>
        /// 检查状态值是否有效
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否有效</returns>
        public static bool IsValidStatus(int status)
        {
            return status is Pending or Issued or Installed or NotRequired or OutsourcedProcessing;
        }

        /// <summary>
        /// 检查状态流转是否有效
        /// </summary>
        /// <param name="currentStatus">当前状态</param>
        /// <param name="newStatus">新状态</param>
        /// <returns>是否可以流转</returns>
        public static bool IsValidStatusTransition(int currentStatus, int newStatus)
        {
            var validTransitions = new Dictionary<int, List<int>>
            {
                { Pending, [Issued, NotRequired, Installed, OutsourcedProcessing] }, // 申请中 → 已领用、无需申请、已安装、委外加工
                { Issued, [Installed] },                                             // 已领用 → 已安装
                { NotRequired, [Installed] },                                        // 无需申请 → 已安装
                { OutsourcedProcessing, [Installed] },                               // 委外加工 → 已安装
                { Installed, [] }                                                    // 已安装 → 无法变更
            };

            return validTransitions.TryGetValue(currentStatus, out var transitions) &&
                   transitions.Contains(newStatus);
        }

        #endregion

        #region 状态权限控制

        /// <summary>
        /// 检查是否可以编辑
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以编辑</returns>
        public static bool CanEdit(int status)
        {
            return status == Pending; // 只有申请中状态可以编辑
        }

        /// <summary>
        /// 检查是否可以删除
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以删除</returns>
        public static bool CanDelete(int status)
        {
            return status is Pending or NotRequired; // 申请中和无需申请状态可以删除
        }

        /// <summary>
        /// 检查是否可以标记为无需申请
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以标记为无需申请</returns>
        public static bool CanMarkAsNotRequired(int status)
        {
            return status == Pending; // 只有申请中状态可以标记为无需申请
        }

        /// <summary>
        /// 检查是否可以直接完成（跳过申请流程）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以直接完成</returns>
        public static bool CanDirectComplete(int status)
        {
            return status is Pending or NotRequired; // 申请中和无需申请状态可以直接完成
        }

        /// <summary>
        /// 检查是否可以安装
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以安装</returns>
        public static bool CanInstall(int status)
        {
            return status is Issued or OutsourcedProcessing; // 已领用和委外加工状态可以安装
        }

        /// <summary>
        /// 检查是否可以委外加工
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以委外加工</returns>
        public static bool CanOutsourceProcessing(int status)
        {
            return status == Pending; // 只有申请中状态可以委外加工
        }

        /// <summary>
        /// 检查是否可以管理委外加工进度
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以管理委外加工进度</returns>
        public static bool CanManageOutsourcedProgress(int status)
        {
            return status == OutsourcedProcessing; // 只有委外加工状态可以管理进度
        }

        /// <summary>
        /// 检查是否可以查看委外加工记录（用于已完成的零件回查）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否可以查看委外加工记录</returns>
        public static bool CanViewOutsourcedRecord(int status)
        {
            return status == Installed; // 已安装状态可以查看委外加工记录
        }

        #endregion

        #region 状态分组

        /// <summary>
        /// 检查是否为活跃状态（未完成的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为活跃状态</returns>
        public static bool IsActiveStatus(int status)
        {
            return status is Pending or Issued or NotRequired or OutsourcedProcessing;
        }

        /// <summary>
        /// 检查是否为完成状态
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为完成状态</returns>
        public static bool IsCompletedStatus(int status)
        {
            return status == Installed;
        }

        /// <summary>
        /// 检查是否为终止状态（无法再变更的状态）
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>是否为终止状态</returns>
        public static bool IsFinalStatus(int status)
        {
            return status == Installed;
        }

        #endregion

        #region 状态列表

        /// <summary>
        /// 获取所有有效状态列表
        /// </summary>
        /// <returns>状态列表</returns>
        public static List<(int Value, string Name)> GetAllStatuses()
        {
            return
            [
                (Pending, GetStatusName(Pending)),
                (Issued, GetStatusName(Issued)),
                (Installed, GetStatusName(Installed)),
                (NotRequired, GetStatusName(NotRequired)),
                (OutsourcedProcessing, GetStatusName(OutsourcedProcessing))
            ];
        }

        /// <summary>
        /// 获取可以流转到的状态列表
        /// </summary>
        /// <param name="currentStatus">当前状态</param>
        /// <returns>可流转状态列表</returns>
        public static List<(int Value, string Name)> GetAvailableTransitions(int currentStatus)
        {
            var validTransitions = new Dictionary<int, List<int>>
            {
                { Pending, [Issued, NotRequired, Installed, OutsourcedProcessing] },
                { Issued, [Installed] },
                { NotRequired, [Installed] },
                { OutsourcedProcessing, [Installed] },
                { Installed, [] }
            };

            if (!validTransitions.TryGetValue(currentStatus, out var transitions))
                return [];

            return transitions
                .Select(status => (status, GetStatusName(status)))
                .ToList();
        }

        #endregion

        #region 统计相关

        /// <summary>
        /// 计算完成率
        /// </summary>
        /// <param name="totalCount">总数量</param>
        /// <param name="installedCount">已安装数量</param>
        /// <returns>完成率百分比</returns>
        public static decimal CalculateCompletionRate(int totalCount, int installedCount)
        {
            if (totalCount == 0) return 100m;
            return Math.Round((decimal)installedCount / totalCount * 100, 2);
        }

        /// <summary>
        /// 获取状态统计描述
        /// </summary>
        /// <param name="pendingCount">申请中数量</param>
        /// <param name="issuedCount">已领用数量</param>
        /// <param name="installedCount">已安装数量</param>
        /// <param name="notRequiredCount">无需申请数量</param>
        /// <returns>统计描述</returns>
        public static string GetStatusSummary(int pendingCount, int issuedCount, int installedCount, int notRequiredCount = 0, int outsourcedProcessingCount = 0)
        {
            var total = pendingCount + issuedCount + installedCount + notRequiredCount + outsourcedProcessingCount;
            if (total == 0) return "暂无零件更换记录";

            var parts = new List<string>();
            if (pendingCount > 0) parts.Add($"申请中{pendingCount}个");
            if (issuedCount > 0) parts.Add($"已领用{issuedCount}个");
            if (installedCount > 0) parts.Add($"已安装{installedCount}个");
            if (notRequiredCount > 0) parts.Add($"无需申请{notRequiredCount}个");
            if (outsourcedProcessingCount > 0) parts.Add($"委外加工{outsourcedProcessingCount}个");

            return $"共{total}个零件更换记录：{string.Join("，", parts)}";
        }

        #endregion
    }
}
