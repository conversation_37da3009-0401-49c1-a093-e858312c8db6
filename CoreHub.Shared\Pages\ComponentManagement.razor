@page "/component-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@inject IComponentService ComponentService
@inject IComponentCategoryService ComponentCategoryService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>部件管理</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" />
                    部件管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索部件名称、编码、型号..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudSelect T="int?" @bind-Value="selectedCategoryId"
                                 Label="部件分类"
                                 Clearable="true"
                                 OnSelectionChanged="OnCategoryChanged"
                                 Class="mr-4">
                            @foreach (var category in categories)
                            {
                                <MudSelectItem T="int?" Value="@category.Id">@category.FullName</MudSelectItem>
                            }
                        </MudSelect>
                        <MudSelect T="string" @bind-Value="stockFilter"
                                 Label="库存状态"
                                 Clearable="true"
                                 OnSelectionChanged="OnStockFilterChanged"
                                 Class="mr-4">
                            <MudSelectItem T="string" Value="@("low")">库存不足</MudSelectItem>
                            <MudSelectItem T="string" Value="@("out")">缺货</MudSelectItem>
                            <MudSelectItem T="string" Value="@("normal")">库存充足</MudSelectItem>
                        </MudSelect>
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadComponents">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudStack Row Spacing="2">
                        <MudButton Variant="Variant.Outlined" 
                                 Color="Color.Warning" 
                                 StartIcon="@Icons.Material.Filled.Warning"
                                 OnClick="ShowLowStockComponents">
                            库存预警
                        </MudButton>
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Success" 
                                 StartIcon="@Icons.Material.Filled.Add"
                                 OnClick="OpenCreateDialog">
                            新增部件
                        </MudButton>
                    </MudStack>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="ComponentDto" 
                           Items="@filteredComponents" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px"
                           SortMode="SortMode.Single"
                           Groupable="true"
                           GroupExpanded="true">
                    <Columns>
                        <PropertyColumn Property="x => x.CategoryName" Title="分类" Grouping />
                        <PropertyColumn Property="x => x.Code" Title="部件编码" />
                        <PropertyColumn Property="x => x.Name" Title="部件名称" />
                        <PropertyColumn Property="x => x.Model" Title="型号规格" />
                        <PropertyColumn Property="x => x.Brand" Title="品牌" />
                        <PropertyColumn Property="x => x.Supplier" Title="供应商" />
                        <TemplateColumn Title="库存" Sortable="true" SortBy="@(x => x.StockQuantity)">
                            <CellTemplate>
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                    <MudText>@context.Item.StockQuantity @context.Item.Unit</MudText>
                                    <MudChip Color="@GetStockStatusColor(context.Item.StockStatusColor)" 
                                           Size="Size.Small">
                                        @context.Item.StockStatusName
                                    </MudChip>
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="单价" Sortable="true" SortBy="@(x => x.UnitPrice)">
                            <CellTemplate>
                                @if (context.Item.UnitPrice.HasValue)
                                {
                                    <MudText>¥@context.Item.UnitPrice.Value.ToString("F2")</MudText>
                                }
                                else
                                {
                                    <MudText Color="Color.Secondary">-</MudText>
                                }
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="操作" Sortable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudTooltip Text="编辑">
                                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                     Color="Color.Primary"
                                                     Size="Size.Small"
                                                     OnClick="() => OpenEditDialog(context.Item)" />
                                    </MudTooltip>
                                    <MudTooltip Text="库存管理">
                                        <MudIconButton Icon="@Icons.Material.Filled.Inventory"
                                                     Color="Color.Info"
                                                     Size="Size.Small"
                                                     OnClick="() => OpenStockDialog(context.Item)" />
                                    </MudTooltip>
                                    <MudTooltip Text="@(context.Item.IsEnabled ? "禁用" : "启用")">
                                        <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                                                     Color="Color.Warning"
                                                     Size="Size.Small"
                                                     OnClick="() => ToggleStatus(context.Item)" />
                                    </MudTooltip>
                                    <MudTooltip Text="删除">
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                     Color="Color.Error"
                                                     Size="Size.Small"
                                                     OnClick="() => DeleteComponent(context.Item)" />
                                    </MudTooltip>
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private List<ComponentDto> components = new();
    private List<ComponentDto> filteredComponents = new();
    private List<ComponentCategory> categories = new();
    private string searchText = string.Empty;
    private int? selectedCategoryId;
    private string? stockFilter;
    private bool loading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
        await LoadComponents();
    }

    private async Task LoadCategories()
    {
        try
        {
            var allCategories = await ComponentCategoryService.GetEnabledCategoriesAsync();
            categories = FlattenCategoryTree(allCategories);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载分类失败: {ex.Message}", Severity.Error);
        }
    }

    private List<ComponentCategory> FlattenCategoryTree(List<ComponentCategory> tree)
    {
        var result = new List<ComponentCategory>();
        foreach (var category in tree)
        {
            result.Add(category);
            if (category.Children.Any())
            {
                result.AddRange(FlattenCategoryTree(category.Children));
            }
        }
        return result;
    }

    private async Task LoadComponents()
    {
        try
        {
            loading = true;
            StateHasChanged();

            components = await ComponentService.GetAllComponentsAsync();
            FilterComponents();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部件失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private void FilterComponents()
    {
        var query = components.AsEnumerable();

        // 搜索过滤
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            query = query.Where(c =>
                c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(c.Model) && c.Model.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(c.Brand) && c.Brand.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(c.Supplier) && c.Supplier.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            );
        }

        // 分类过滤
        if (selectedCategoryId.HasValue)
        {
            query = query.Where(c => c.CategoryId == selectedCategoryId.Value);
        }

        // 库存状态过滤
        if (!string.IsNullOrEmpty(stockFilter))
        {
            query = stockFilter switch
            {
                "low" => query.Where(c => c.StockQuantity <= c.MinStockQuantity && c.StockQuantity > 0),
                "out" => query.Where(c => c.StockQuantity <= 0),
                "normal" => query.Where(c => c.StockQuantity > c.MinStockQuantity),
                _ => query
            };
        }

        filteredComponents = query.ToList();
        StateHasChanged();
    }

    private Color GetStockStatusColor(string statusColor)
    {
        return statusColor switch
        {
            "error" => Color.Error,
            "warning" => Color.Warning,
            "success" => Color.Success,
            _ => Color.Default
        };
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterComponents();
    }

    private void OnCategoryChanged(int? categoryId)
    {
        selectedCategoryId = categoryId;
        FilterComponents();
    }

    private void OnStockFilterChanged(string? filter)
    {
        stockFilter = filter;
        FilterComponents();
    }

    private async Task ShowLowStockComponents()
    {
        try
        {
            var lowStockComponents = await ComponentService.GetLowStockComponentsAsync();
            if (lowStockComponents.Any())
            {
                // 显示库存预警对话框
                var parameters = new DialogParameters();
                parameters.Add("LowStockComponents", lowStockComponents);

                // await DialogService.ShowAsync<LowStockWarningDialog>("库存预警", parameters);
                Snackbar.Add($"发现 {lowStockComponents.Count} 个库存不足的部件", Severity.Warning);
            }
            else
            {
                Snackbar.Add("当前没有库存不足的部件", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取库存预警失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters();
        parameters.Add("Component", new Component());
        parameters.Add("IsEdit", false);
        parameters.Add("Categories", categories);

        var dialog = await DialogService.ShowAsync<ComponentEditDialog>("新增部件", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadComponents();
        }
    }

    private async Task OpenEditDialog(ComponentDto componentDto)
    {
        var component = await ComponentService.GetComponentByIdAsync(componentDto.Id);
        if (component == null)
        {
            Snackbar.Add("部件不存在", Severity.Error);
            return;
        }

        var parameters = new DialogParameters();
        parameters.Add("Component", component);
        parameters.Add("IsEdit", true);
        parameters.Add("Categories", categories);

        var dialog = await DialogService.ShowAsync<ComponentEditDialog>("编辑部件", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadComponents();
        }
    }

    private async Task OpenStockDialog(ComponentDto componentDto)
    {
        var parameters = new DialogParameters();
        parameters.Add("ComponentId", componentDto.Id);
        parameters.Add("ComponentName", componentDto.Name);
        parameters.Add("CurrentStock", componentDto.StockQuantity);
        parameters.Add("Unit", componentDto.Unit);

        var dialog = await DialogService.ShowAsync<ComponentStockDialog>("库存管理", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadComponents();
        }
    }

    private async Task ToggleStatus(ComponentDto componentDto)
    {
        try
        {
            var result = await ComponentService.ToggleStatusAsync(componentDto.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"部件状态已{(componentDto.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadComponents();
            }
            else
            {
                Snackbar.Add(result.ErrorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteComponent(ComponentDto componentDto)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除部件 '{componentDto.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await ComponentService.DeleteComponentAsync(componentDto.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("部件删除成功", Severity.Success);
                    await LoadComponents();
                }
                else
                {
                    Snackbar.Add(result.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
