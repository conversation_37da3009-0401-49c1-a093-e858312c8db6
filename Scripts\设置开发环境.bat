@echo off
echo ========================================
echo    CoreHub - Setting Development Environment
echo ========================================
echo.

echo Setting CoreHub-specific environment variables...
set "ENV_NAME=Development"
setx COREHUB_ENVIRONMENT "%ENV_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENVIRONMENT = %ENV_NAME%
) else (
    echo [ERROR] Failed to set COREHUB_ENVIRONMENT
)

set "API_URL=http://************:8080"
setx COREHUB_API_BASE_URL "%API_URL%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_API_BASE_URL = %API_URL%
) else (
    echo [ERROR] Failed to set COREHUB_API_BASE_URL
)

set "DB_CONN_STR=Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
setx COREHUB_DB_CONNECTION_STRING "%DB_CONN_STR%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_DB_CONNECTION_STRING = %DB_CONN_STR%
) else (
    echo [ERROR] Failed to set COREHUB_DB_CONNECTION_STRING
)

set "USE_HTTPS=false"
setx COREHUB_USE_HTTPS_REDIRECTION "%USE_HTTPS%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_USE_HTTPS_REDIRECTION = %USE_HTTPS%
) else (
    echo [ERROR] Failed to set COREHUB_USE_HTTPS_REDIRECTION
)

set "VERBOSE_LOG=true"
setx COREHUB_ENABLE_VERBOSE_LOGGING "%VERBOSE_LOG%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENABLE_VERBOSE_LOGGING = %VERBOSE_LOG%
) else (
    echo [ERROR] Failed to set COREHUB_ENABLE_VERBOSE_LOGGING
)

echo.
echo ========================================
echo CoreHub Development environment configured!
echo ========================================
echo.
echo Configuration Summary:
echo - Environment: Development (CoreHub-specific)
echo - API URL: http://************:8080
echo - HTTPS Redirect: Disabled
echo - Verbose Logging: Enabled
echo - Database: CoreHub (Development)
echo.
echo ADVANTAGE: These settings only affect CoreHub project!
echo Other projects using different environment variables will not be affected.
echo.
echo IMPORTANT: Please restart your application
echo for the changes to take effect:
echo - Visual Studio: Restart Visual Studio
echo - Command Line: Open new command window
echo - IIS: Restart application pool
echo.
pause
