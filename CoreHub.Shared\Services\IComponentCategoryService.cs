using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件分类服务接口
    /// </summary>
    public interface IComponentCategoryService
    {
        /// <summary>
        /// 获取所有部件分类
        /// </summary>
        Task<List<ComponentCategory>> GetAllCategoriesAsync();

        /// <summary>
        /// 根据ID获取部件分类
        /// </summary>
        Task<ComponentCategory?> GetCategoryByIdAsync(int id);

        /// <summary>
        /// 获取启用的部件分类
        /// </summary>
        Task<List<ComponentCategory>> GetEnabledCategoriesAsync();

        /// <summary>
        /// 获取根级分类（一级分类）
        /// </summary>
        Task<List<ComponentCategory>> GetRootCategoriesAsync();

        /// <summary>
        /// 获取指定分类的子分类
        /// </summary>
        Task<List<ComponentCategory>> GetChildCategoriesByParentIdAsync(int parentId);

        /// <summary>
        /// 获取分类树结构（仅启用的）
        /// </summary>
        Task<List<ComponentCategory>> GetCategoryTreeAsync();

        /// <summary>
        /// 获取所有分类树结构（包括禁用的）
        /// </summary>
        Task<List<ComponentCategory>> GetAllCategoryTreeAsync();

        /// <summary>
        /// 创建部件分类
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateCategoryAsync(ComponentCategory category);

        /// <summary>
        /// 更新部件分类
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateCategoryAsync(ComponentCategory category);

        /// <summary>
        /// 删除部件分类
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteCategoryAsync(int id);

        /// <summary>
        /// 切换分类状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 检查分类编码是否唯一
        /// </summary>
        Task<bool> IsCategoryCodeUniqueAsync(string code, int? excludeId = null);

        /// <summary>
        /// 检查是否可以删除分类（没有子分类且没有关联的部件）
        /// </summary>
        Task<bool> CanDeleteCategoryAsync(int id);

        /// <summary>
        /// 获取分类的完整路径
        /// </summary>
        Task<string> GetCategoryFullPathAsync(int id);

        /// <summary>
        /// 移动分类到新的父级
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> MoveCategoryAsync(int categoryId, int? newParentId);
    }
}
