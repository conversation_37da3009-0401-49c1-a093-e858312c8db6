using System.Data;
using Microsoft.Extensions.Logging;
using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using SqlSugar;


namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 基于存储过程的用户认证服务 (SQL Server 2012 兼容版)
    /// </summary>
    public class StoredProcedureAuthenticationService : IUserAuthenticationService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<StoredProcedureAuthenticationService> _logger;

        public StoredProcedureAuthenticationService(DatabaseContext dbContext, ILogger<StoredProcedureAuthenticationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 验证用户登录（使用存储过程）
        /// </summary>
        public async Task<UserLoginResult> ValidateUserAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("使用存储过程验证用户：{username}", username);

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "用户名和密码不能为空"
                    };
                }

                // 调用数据库脚本中的存储过程验证用户
                var parameters = new SugarParameter[]
                {
                    new SugarParameter("@Username", username),
                    new SugarParameter("@Password", password),
                    new SugarParameter("@IsValid", null, true) { Direction = ParameterDirection.Output },
                    new SugarParameter("@UserId", null, true) { Direction = ParameterDirection.Output },
                    new SugarParameter("@DisplayName", null, true) { Direction = ParameterDirection.Output, Size = 100 },
                    new SugarParameter("@IsLocked", null, true) { Direction = ParameterDirection.Output },
                    new SugarParameter("@FailureReason", null, true) { Direction = ParameterDirection.Output, Size = 500 }
                };

                // 执行存储过程
                await _dbContext.Db.Ado.UseStoredProcedure().ExecuteCommandAsync("sp_ValidateUser", parameters);

                // 获取输出参数
                var isValidValue = parameters.First(p => p.ParameterName == "@IsValid").Value;
                var isValid = SafeConvertToBoolean(isValidValue);
                
                var userId = parameters.First(p => p.ParameterName == "@UserId").Value;
                var displayName = parameters.First(p => p.ParameterName == "@DisplayName").Value?.ToString();
                
                var isLockedValue = parameters.First(p => p.ParameterName == "@IsLocked").Value;
                var isLocked = SafeConvertToBoolean(isLockedValue);
                
                var failureReason = parameters.First(p => p.ParameterName == "@FailureReason").Value?.ToString();

                if (!isValid)
                {
                    _logger.LogWarning("存储过程验证失败：{username}，错误：{error}", username, failureReason);
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = failureReason ?? "用户名或密码错误"
                    };
                }

                if (isLocked)
                {
                    _logger.LogWarning("用户账户已锁定：{username}", username);
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "用户账户已被锁定"
                    };
                }

                // 获取用户权限
                var userPermissions = await GetUserPermissionsByStoredProcedureAsync(Convert.ToInt32(userId));

                // 获取用户详细信息
                var userInfo = await GetUserInfoByIdAsync(Convert.ToInt32(userId));

                if (userInfo == null)
                {
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "获取用户信息失败"
                    };
                }

                // 构建UserInfo
                userInfo.Permissions = userPermissions;

                _logger.LogInformation("存储过程验证成功：{username}", username);

                return new UserLoginResult
                {
                    IsSuccess = true,
                    User = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "存储过程验证用户时发生异常：{username}", username);
                return new UserLoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = "验证过程中发生错误，请稍后重试"
                };
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public UserInfo? GetUserInfo(string username)
        {
            try
            {
                // 使用SqlSugar通过用户名获取用户ID
                var user = _dbContext.Db.Queryable<User>()
                    .Where(u => u.Username == username)
                    .Select(u => new { u.Id })
                    .First();

                if (user == null) return null;

                // 使用存储过程获取用户信息
                return GetUserInfoByIdAsync(user.Id).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息失败：{username}", username);
                return null;
            }
        }

        /// <summary>
        /// 根据用户ID获取用户信息
        /// </summary>
        private async Task<UserInfo?> GetUserInfoByIdAsync(int userId)
        {
            try
            {
                var parameters = new SugarParameter[]
                {
                    new SugarParameter("@UserId", userId)
                };

                // 使用存储过程获取用户信息
                var userInfos = await _dbContext.Db.Ado.UseStoredProcedure()
                    .SqlQueryAsync<UserInfoDto>("sp_GetUserInfo", parameters);

                var userInfo = userInfos.FirstOrDefault();
                if (userInfo == null) return null;

                // 获取用户权限
                var permissions = await GetUserPermissionsByStoredProcedureAsync(userId);

                // 使用SqlSugar获取用户角色代码和名称
                var userRoleInfo = await _dbContext.Db.Queryable<UserRole>()
                    .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                    .Where((ur, r) => ur.UserId == userId && ur.IsEnabled)
                    .Select((ur, r) => new { r.Code, r.Name })
                    .ToListAsync();

                var roleCodes = userRoleInfo.Select(r => r.Code).ToList();
                var roleNames = userRoleInfo.Select(r => r.Name).ToList();

                // 调试日志
                _logger.LogInformation("用户 {UserId} 角色信息: 代码=[{RoleCodes}], 名称=[{RoleNames}]",
                    userId, string.Join(", ", roleCodes), string.Join(", ", roleNames));

                return new UserInfo
                {
                    UserId = userId,
                    Username = userInfo.Username,
                    DisplayName = userInfo.DisplayName,
                    Email = userInfo.Email ?? "",
                    Role = string.Join(", ", roleCodes),
                    RoleName = string.Join(", ", roleNames),
                    Permissions = permissions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取用户信息失败：{userId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 获取所有用户
        /// </summary>
        public List<UserInfo> GetAllUsers()
        {
            try
            {
                // 使用存储过程获取所有用户，支持分页参数
                var parameters = new SugarParameter[]
                {
                    new SugarParameter("@PageIndex", 1),
                    new SugarParameter("@PageSize", 1000), // 获取前1000个用户
                    new SugarParameter("@SearchKeyword", null),
                    new SugarParameter("@IsEnabled", null),
                    new SugarParameter("@IsLocked", null)
                };

                var users = _dbContext.Db.Ado.UseStoredProcedure()
                    .SqlQuery<UserInfoDto>("sp_GetAllUsers", parameters)
                    .ToList();

                var userInfos = new List<UserInfo>();

                foreach (var user in users)
                {
                    var permissions = GetUserPermissionsByStoredProcedureAsync(user.Id).GetAwaiter().GetResult();

                    // 使用SqlSugar获取用户角色代码和名称
                    var userRoleInfo = _dbContext.Db.Queryable<UserRole>()
                        .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                        .Where((ur, r) => ur.UserId == user.Id && ur.IsEnabled)
                        .Select((ur, r) => new { r.Code, r.Name })
                        .ToList();

                    var roleCodes = userRoleInfo.Select(r => r.Code).ToList();
                    var roleNames = userRoleInfo.Select(r => r.Name).ToList();

                    userInfos.Add(new UserInfo
                    {
                        UserId = user.Id,
                        Username = user.Username,
                        DisplayName = user.DisplayName,
                        Email = user.Email ?? "",
                        Role = string.Join(", ", roleCodes),
                        RoleName = string.Join(", ", roleNames),
                        Permissions = permissions
                    });
                }

                return userInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户失败");
                return new List<UserInfo>();
            }
        }

        #region 私有方法

        /// <summary>
        /// 安全转换存储过程返回的布尔值
        /// </summary>
        private static bool SafeConvertToBoolean(object? value)
        {
            if (value == null) return false;
            
            var stringValue = value.ToString();
            if (string.IsNullOrEmpty(stringValue)) return false;
            
            // 处理数字格式
            if (stringValue == "1" || stringValue == "True" || stringValue.ToLower() == "true")
                return true;
            
            if (stringValue == "0" || stringValue == "False" || stringValue.ToLower() == "false")
                return false;
            
            // 尝试标准布尔转换
            if (bool.TryParse(stringValue, out bool result))
                return result;
            
            // 尝试数字转换
            if (int.TryParse(stringValue, out int intResult))
                return intResult != 0;
            
            return false;
        }

        /// <summary>
        /// 使用存储过程获取用户权限
        /// </summary>
        private async Task<List<string>> GetUserPermissionsByStoredProcedureAsync(int userId)
        {
            try
            {
                var parameters = new SugarParameter[]
                {
                    new SugarParameter("@UserId", userId)
                };

                // 使用存储过程获取用户权限，返回权限详细信息
                var permissions = await _dbContext.Db.Ado.UseStoredProcedure()
                    .SqlQueryAsync<PermissionDto>("sp_GetUserPermissions", parameters);

                // 提取权限代码
                return permissions.Where(p => !string.IsNullOrEmpty(p.PermissionCode))
                    .Select(p => p.PermissionCode)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户权限失败：{userId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private string GetClientIP()
        {
            // 在实际应用中，这里应该从HttpContext获取真实的客户端IP
            // 当前返回本地IP作为示例
            try
            {
                return System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                    .AddressList
                    .FirstOrDefault(addr => addr.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    ?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// 获取数据库系统统计信息
        /// </summary>
        public async Task<object> GetSystemStatsAsync()
        {
            try
            {
                var stats = await _dbContext.Db.Ado.UseStoredProcedure()
                    .SqlQueryAsync<dynamic>("sp_PermissionSystemStats");

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统统计信息失败");
                return new { Error = "获取统计信息失败" };
            }
        }

        /// <summary>
        /// 执行数据库维护操作
        /// </summary>
        public async Task<bool> MaintenanceDatabaseAsync(string operationType = "CLEANUP_LOGS")
        {
            try
            {
                var parameters = new SugarParameter[]
                {
                    new SugarParameter("@Action", operationType)
                };

                await _dbContext.Db.Ado.UseStoredProcedure()
                    .ExecuteCommandAsync("sp_MaintenancePermissionSystem", parameters);

                _logger.LogInformation("数据库维护操作完成：{operationType}", operationType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库维护操作失败：{operationType}", operationType);
                return false;
            }
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 用户信息DTO
        /// </summary>
        private class UserInfoDto
        {
            public int Id { get; set; }
            public string Username { get; set; } = "";
            public string DisplayName { get; set; } = "";
            public string? Email { get; set; }
            public string? Phone { get; set; }
            public bool IsEnabled { get; set; }
            public bool IsLocked { get; set; }
            public int LoginFailureCount { get; set; }
            public DateTime? LastLoginTime { get; set; }
            public DateTime CreatedAt { get; set; }
            public string? Remark { get; set; }
        }

        /// <summary>
        /// 权限信息DTO
        /// </summary>
        private class PermissionDto
        {
            public string PermissionCode { get; set; } = "";
            public string PermissionName { get; set; } = "";
            public string Module { get; set; } = "";
            public string Action { get; set; } = "";
            public int Level { get; set; }
            public string? RouteUrl { get; set; }
        }

        #endregion
    }
} 