@page "/equipment-component-batch-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components
@using MudBlazor
@inject IEquipmentComponentBatchService BatchService
@inject IEquipmentService EquipmentService
@inject IEquipmentModelService EquipmentModelService
@inject IComponentService ComponentService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备部件批量管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <!-- 页面标题 -->
    <MudBreadcrumbs Items="breadcrumbItems" Class="mb-4"></MudBreadcrumbs>
    
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h4" Class="mb-4">
            <MudIcon Icon="@Icons.Material.Filled.BatchPrediction" Class="mr-2" />
            设备部件批量管理
        </MudText>
        <MudText Typo="Typo.body1" Color="Color.Secondary">
            快速为多台设备批量添加、复制或应用部件配置模板，提高设备配置效率
        </MudText>
    </MudPaper>

    <!-- 操作选择卡片 -->
    <MudGrid>
        <MudItem xs="12" md="4">
            <MudCard Class="pa-4 cursor-pointer hover-card" @onclick="() => SelectOperation(BatchOperationType.ApplyTemplate)">
                <MudCardContent>
                    <MudStack AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.ContentCopy" Size="Size.Large" Color="Color.Primary" />
                        <MudText Typo="Typo.h6" Align="Align.Center">应用型号模板</MudText>
                        <MudText Typo="Typo.body2" Align="Align.Center" Color="Color.Secondary">
                            从设备型号的标准配置模板批量应用部件到多台设备
                        </MudText>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Class="pa-4 cursor-pointer hover-card" @onclick="() => SelectOperation(BatchOperationType.CopyFromEquipment)">
                <MudCardContent>
                    <MudStack AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.FileCopy" Size="Size.Large" Color="Color.Secondary" />
                        <MudText Typo="Typo.h6" Align="Align.Center">复制设备配置</MudText>
                        <MudText Typo="Typo.body2" Align="Align.Center" Color="Color.Secondary">
                            从一台已配置的设备复制部件配置到其他设备
                        </MudText>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="4">
            <MudCard Class="pa-4 cursor-pointer hover-card" @onclick="() => SelectOperation(BatchOperationType.ManualAdd)">
                <MudCardContent>
                    <MudStack AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.Add" Size="Size.Large" Color="Color.Success" />
                        <MudText Typo="Typo.h6" Align="Align.Center">手动批量添加</MudText>
                        <MudText Typo="Typo.body2" Align="Align.Center" Color="Color.Secondary">
                            手动选择部件并批量添加到多台设备
                        </MudText>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 操作配置区域 -->
    @if (selectedOperation != BatchOperationType.None)
    {
        <MudPaper Class="pa-4 mt-4">
            <MudText Typo="Typo.h5" Class="mb-4">
                @GetOperationTitle()
            </MudText>

            <!-- 设备选择 -->
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudText Typo="Typo.h6" Class="mb-2">选择目标设备</MudText>
                    <MudSelect T="int" @bind-SelectedValues="selectedEquipmentIds"
                             Label="目标设备"
                             MultiSelection="true"
                             Clearable="true"
                             AdornmentIcon="@Icons.Material.Filled.Computer"
                             AdornmentColor="Color.Primary"
                             ToStringFunc="@GetEquipmentDisplayText">
                        @if (equipments != null)
                        {
                            @foreach (var equipment in equipments)
                            {
                                <MudSelectItem T="int" Value="@equipment.Id">
                                    @equipment.Name - @equipment.Model?.Name
                                </MudSelectItem>
                            }
                        }
                    </MudSelect>
                    <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-1">
                        已选择 @selectedEquipmentIds.Count() 台设备
                    </MudText>
                </MudItem>

                <!-- 操作特定配置 -->
                <MudItem xs="12" md="6">
                    @if (selectedOperation == BatchOperationType.ApplyTemplate)
                    {
                        <MudText Typo="Typo.h6" Class="mb-2">选择设备型号模板</MudText>
                        <MudSelect T="int?" @bind-Value="selectedTemplateModelId"
                                 Label="设备型号模板"
                                 Clearable="true"
                                 AdornmentIcon="@Icons.Material.Filled.Memory"
                                 AdornmentColor="Color.Primary">
                            @if (availableTemplates != null)
                            {
                                @foreach (var template in availableTemplates)
                                {
                                    <MudSelectItem T="int?" Value="@((int?)template.EquipmentModelId)">
                                        @template.EquipmentModelName (@template.ComponentCount 个部件)
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>
                    }
                    else if (selectedOperation == BatchOperationType.CopyFromEquipment)
                    {
                        <MudText Typo="Typo.h6" Class="mb-2">选择源设备</MudText>
                        <MudSelect T="int?" @bind-Value="selectedSourceEquipmentId"
                                 Label="源设备"
                                 Clearable="true"
                                 AdornmentIcon="@Icons.Material.Filled.Source"
                                 AdornmentColor="Color.Secondary">
                            @if (copyableEquipments != null)
                            {
                                @foreach (var equipment in copyableEquipments)
                                {
                                    <MudSelectItem T="int?" Value="@((int?)equipment.EquipmentId)">
                                        @equipment.EquipmentName (@equipment.EquipmentCode) - @equipment.ComponentCount 个部件
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>
                    }
                    else if (selectedOperation == BatchOperationType.ManualAdd)
                    {
                        <MudText Typo="Typo.h6" Class="mb-2">选择要添加的部件</MudText>
                        <MudSelect T="int" @bind-SelectedValues="selectedComponentIds"
                                 Label="部件"
                                 MultiSelection="true"
                                 Clearable="true"
                                 AdornmentIcon="@Icons.Material.Filled.Inventory"
                                 AdornmentColor="Color.Success">
                            @if (components != null)
                            {
                                @foreach (var component in components)
                                {
                                    <MudSelectItem T="int" Value="@component.Id">
                                        @component.Name (@component.Code) - @component.CategoryName
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>
                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-1">
                            已选择 @selectedComponentIds.Count() 个部件
                        </MudText>
                    }
                </MudItem>
            </MudGrid>

            <!-- 操作选项 -->
            <MudGrid Class="mt-4">
                <MudItem xs="12" md="6">
                    <MudCheckBox T="bool" @bind-Checked="overrideExisting" Label="覆盖已存在的配置" Color="Color.Warning" />
                    @if (selectedOperation == BatchOperationType.ApplyTemplate)
                    {
                        <MudCheckBox T="bool" @bind-Checked="onlyRequiredComponents" Label="仅应用必需部件" Color="Color.Info" Class="mt-2" />
                    }
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="operationRemark" 
                                Label="操作备注" 
                                Variant="Variant.Outlined" 
                                Lines="2" 
                                Placeholder="可选：描述此次批量操作的目的或注意事项" />
                </MudItem>
            </MudGrid>

            <!-- 操作按钮 -->
            <MudStack Direction="Row" Spacing="2" Class="mt-4">
                <MudButton Variant="Variant.Filled" 
                         Color="Color.Primary" 
                         StartIcon="@Icons.Material.Filled.Preview"
                         OnClick="PreviewOperation"
                         Disabled="!CanExecuteOperation()">
                    预览影响
                </MudButton>
                <MudButton Variant="Variant.Filled" 
                         Color="Color.Success" 
                         StartIcon="@Icons.Material.Filled.PlayArrow"
                         OnClick="ExecuteOperation"
                         Disabled="!CanExecuteOperation() || isExecuting">
                    @if (isExecuting)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                        <span>执行中...</span>
                    }
                    else
                    {
                        <span>执行操作</span>
                    }
                </MudButton>
                <MudButton Variant="Variant.Outlined" 
                         Color="Color.Secondary" 
                         StartIcon="@Icons.Material.Filled.Clear"
                         OnClick="ClearSelection">
                    清空选择
                </MudButton>
            </MudStack>
        </MudPaper>
    }

    <!-- 操作结果显示 -->
    @if (lastOperationResult != null)
    {
        <MudPaper Class="pa-4 mt-4">
            <MudText Typo="Typo.h6" Class="mb-3">
                <MudIcon Icon="@(lastOperationResult.IsSuccess ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" 
                       Color="@(lastOperationResult.IsSuccess ? Color.Success : Color.Error)" Class="mr-2" />
                操作结果
            </MudText>
            
            <MudAlert Severity="@(lastOperationResult.IsSuccess ? Severity.Success : Severity.Error)" Class="mb-3">
                @lastOperationResult.Summary
                @if (!string.IsNullOrEmpty(lastOperationResult.ErrorMessage))
                {
                    <br />@lastOperationResult.ErrorMessage
                }
            </MudAlert>

            @if (lastOperationResult.Details.Any())
            {
                <MudDataGrid Items="@lastOperationResult.Details" 
                           Filterable="false" 
                           SortMode="SortMode.None"
                           Groupable="false">
                    <Columns>
                        <PropertyColumn Property="x => x.ItemName" Title="项目名称" />
                        <PropertyColumn Property="x => x.OperationType" Title="操作类型" />
                        <TemplateColumn Title="状态">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsSuccess ? Color.Success : Color.Error)" 
                                       Size="Size.Small">
                                    @(context.Item.IsSuccess ? "成功" : "失败")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.ErrorMessage" Title="错误信息" />
                    </Columns>
                </MudDataGrid>
            }
        </MudPaper>
    }
</MudContainer>

<style>
    .hover-card {
        transition: all 0.3s ease;
    }
    .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.12);
    }
    .cursor-pointer {
        cursor: pointer;
    }
</style>

@code {
    // 枚举定义
    public enum BatchOperationType
    {
        None,
        ApplyTemplate,
        CopyFromEquipment,
        ManualAdd
    }

    // 页面状态
    private BatchOperationType selectedOperation = BatchOperationType.None;
    private bool isExecuting = false;
    private bool isLoading = false;

    // 面包屑导航
    private List<BreadcrumbItem> breadcrumbItems = new()
    {
        new BreadcrumbItem("首页", href: "/", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("设备管理", href: null, disabled: true),
        new BreadcrumbItem("设备部件批量管理", href: "/equipment-component-batch-management", disabled: true)
    };

    // 数据集合
    private List<Equipment>? equipments;
    private List<TemplateOptionDto>? availableTemplates;
    private List<EquipmentOptionDto>? copyableEquipments;
    private List<ComponentDto>? components;

    // 选择状态
    private IEnumerable<int> selectedEquipmentIds = new List<int>();
    private int? selectedTemplateModelId;
    private int? selectedSourceEquipmentId;
    private IEnumerable<int> selectedComponentIds = new List<int>();

    // 操作选项
    private bool overrideExisting = false;
    private bool onlyRequiredComponents = false;
    private string operationRemark = string.Empty;

    // 操作结果
    private BatchOperationResultDto? lastOperationResult;

    protected override async Task OnInitializedAsync()
    {
        await LoadInitialData();
    }

    private async Task LoadInitialData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // 加载设备列表
            equipments = await EquipmentService.GetAllEquipmentAsync();

            // 加载部件列表
            components = await ComponentService.GetAllComponentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SelectOperation(BatchOperationType operationType)
    {
        selectedOperation = operationType;

        // 清空之前的选择
        ClearSelection();

        // 根据操作类型加载相应数据
        try
        {
            switch (operationType)
            {
                case BatchOperationType.ApplyTemplate:
                    await LoadAvailableTemplates();
                    break;
                case BatchOperationType.CopyFromEquipment:
                    await LoadCopyableEquipments();
                    break;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载操作数据失败: {ex.Message}", Severity.Error);
        }

        StateHasChanged();
    }

    private async Task LoadAvailableTemplates()
    {
        // 获取所有有模板的设备型号
        availableTemplates = await BatchService.GetAllAvailableTemplatesAsync();
    }

    private async Task LoadCopyableEquipments()
    {
        if (selectedEquipmentIds.Any())
        {
            copyableEquipments = await BatchService.GetCopyableEquipmentsAsync(selectedEquipmentIds.ToList());
        }
    }

    private string GetOperationTitle()
    {
        return selectedOperation switch
        {
            BatchOperationType.ApplyTemplate => "应用设备型号模板",
            BatchOperationType.CopyFromEquipment => "复制设备部件配置",
            BatchOperationType.ManualAdd => "手动批量添加部件",
            _ => "未知操作"
        };
    }

    private bool CanExecuteOperation()
    {
        if (!selectedEquipmentIds.Any()) return false;

        return selectedOperation switch
        {
            BatchOperationType.ApplyTemplate => selectedTemplateModelId.HasValue,
            BatchOperationType.CopyFromEquipment => selectedSourceEquipmentId.HasValue,
            BatchOperationType.ManualAdd => selectedComponentIds.Any(),
            _ => false
        };
    }

    private async Task PreviewOperation()
    {
        try
        {
            var request = CreateOperationRequest();
            if (request == null) return;

            var impact = await BatchService.GetBatchOperationImpactAsync(request);

            var parameters = new DialogParameters
            {
                ["Impact"] = impact
            };

            var dialog = await DialogService.ShowAsync<BatchOperationPreviewDialog>("操作影响预览", parameters);
            var result = await dialog.Result;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"预览操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ExecuteOperation()
    {
        try
        {
            isExecuting = true;
            StateHasChanged();

            var request = CreateOperationRequest();
            if (request == null) return;

            // 验证操作
            var validation = await BatchService.ValidateBatchOperationAsync(request);
            if (!validation.IsValid)
            {
                var errorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                Snackbar.Add($"操作验证失败: {errorMessage}", Severity.Error);
                return;
            }

            // 执行操作
            lastOperationResult = selectedOperation switch
            {
                BatchOperationType.ApplyTemplate => await BatchService.ApplyTemplateToEquipmentsAsync((ApplyTemplateToEquipmentsRequestDto)request),
                BatchOperationType.CopyFromEquipment => await BatchService.CopyComponentsFromEquipmentAsync((CopyComponentsFromEquipmentRequestDto)request),
                BatchOperationType.ManualAdd => await BatchService.BatchAddComponentsToEquipmentsAsync((BatchAddComponentsRequestDto)request),
                _ => new BatchOperationResultDto { IsSuccess = false, ErrorMessage = "不支持的操作类型" }
            };

            if (lastOperationResult.IsSuccess)
            {
                Snackbar.Add("批量操作执行成功！", Severity.Success);
            }
            else
            {
                Snackbar.Add($"批量操作执行失败: {lastOperationResult.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"执行操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isExecuting = false;
            StateHasChanged();
        }
    }

    private object? CreateOperationRequest()
    {
        return selectedOperation switch
        {
            BatchOperationType.ApplyTemplate => new ApplyTemplateToEquipmentsRequestDto
            {
                EquipmentModelId = selectedTemplateModelId!.Value,
                EquipmentIds = selectedEquipmentIds.ToList(),
                OverrideExisting = overrideExisting,
                OnlyRequiredComponents = onlyRequiredComponents,
                Remark = operationRemark
            },
            BatchOperationType.CopyFromEquipment => new CopyComponentsFromEquipmentRequestDto
            {
                SourceEquipmentId = selectedSourceEquipmentId!.Value,
                TargetEquipmentIds = selectedEquipmentIds.ToList(),
                OverrideExisting = overrideExisting,
                Remark = operationRemark
            },
            BatchOperationType.ManualAdd => new BatchAddComponentsRequestDto
            {
                EquipmentIds = selectedEquipmentIds.ToList(),
                ComponentConfigurations = selectedComponentIds.Select(id => new ComponentConfigurationDto
                {
                    ComponentId = id,
                    StandardQuantity = 1,
                    IsRequired = false
                }).ToList(),
                OverrideExisting = overrideExisting,
                Remark = operationRemark
            },
            _ => null
        };
    }

    private void ClearSelection()
    {
        selectedEquipmentIds = new List<int>();
        selectedTemplateModelId = null;
        selectedSourceEquipmentId = null;
        selectedComponentIds = new List<int>();
        overrideExisting = false;
        onlyRequiredComponents = false;
        operationRemark = string.Empty;
        lastOperationResult = null;
        availableTemplates = null;
        copyableEquipments = null;
    }

    private string GetEquipmentDisplayText(int equipmentId)
    {
        var equipment = equipments?.FirstOrDefault(e => e.Id == equipmentId);
        if (equipment == null)
            return equipmentId.ToString();

        return equipment.Name;
    }
}
