using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件分类服务实现
    /// </summary>
    public class ComponentCategoryService : IComponentCategoryService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<ComponentCategoryService> _logger;

        public ComponentCategoryService(
            DatabaseContext dbContext,
            ILogger<ComponentCategoryService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<ComponentCategory>> GetAllCategoriesAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentCategory>()
                    .Includes(cc => cc.Parent)
                    .OrderBy(cc => new { cc.Level, cc.SortOrder, cc.Name })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有部件分类失败");
                throw;
            }
        }

        public async Task<ComponentCategory?> GetCategoryByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取部件分类失败: {id}", id);
                throw;
            }
        }

        public async Task<List<ComponentCategory>> GetEnabledCategoriesAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.IsEnabled)
                    .Includes(cc => cc.Parent)
                    .OrderBy(cc => new { cc.Level, cc.SortOrder, cc.Name })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的部件分类失败");
                throw;
            }
        }

        public async Task<List<ComponentCategory>> GetRootCategoriesAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.ParentId == null && cc.IsEnabled)
                    .OrderBy(cc => new { cc.SortOrder, cc.Name })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取根级部件分类失败");
                throw;
            }
        }

        public async Task<List<ComponentCategory>> GetChildCategoriesByParentIdAsync(int parentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.ParentId == parentId && cc.IsEnabled)
                    .OrderBy(cc => new { cc.SortOrder, cc.Name })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取子部件分类失败: {parentId}", parentId);
                throw;
            }
        }

        public async Task<List<ComponentCategory>> GetCategoryTreeAsync()
        {
            try
            {
                var allCategories = await GetEnabledCategoriesAsync();
                return BuildCategoryTree(allCategories, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部件分类树失败");
                throw;
            }
        }

        public async Task<List<ComponentCategory>> GetAllCategoryTreeAsync()
        {
            try
            {
                var allCategories = await GetAllCategoriesAsync();
                return BuildCategoryTree(allCategories, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有部件分类树失败");
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateCategoryAsync(ComponentCategory category)
        {
            try
            {
                // 检查编码是否唯一
                if (!await IsCategoryCodeUniqueAsync(category.Code))
                {
                    return (false, "分类编码已存在");
                }

                // 设置层级
                if (category.ParentId.HasValue)
                {
                    var parent = await GetCategoryByIdAsync(category.ParentId.Value);
                    if (parent == null)
                    {
                        return (false, "父级分类不存在");
                    }
                    category.Level = parent.Level + 1;
                }
                else
                {
                    category.Level = 1;
                }

                category.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(category).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建部件分类成功: {name} ({code})", category.Name, category.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建部件分类失败: {name}", category.Name);
                return (false, $"创建部件分类失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateCategoryAsync(ComponentCategory category)
        {
            try
            {
                // 检查编码是否唯一（排除自己）
                if (!await IsCategoryCodeUniqueAsync(category.Code, category.Id))
                {
                    return (false, "分类编码已存在");
                }

                // 检查是否形成循环引用
                if (category.ParentId.HasValue && await WouldCreateCircularReference(category.Id, category.ParentId.Value))
                {
                    return (false, "不能将分类移动到其子分类下");
                }

                // 更新层级
                if (category.ParentId.HasValue)
                {
                    var parent = await GetCategoryByIdAsync(category.ParentId.Value);
                    if (parent == null)
                    {
                        return (false, "父级分类不存在");
                    }
                    category.Level = parent.Level + 1;
                }
                else
                {
                    category.Level = 1;
                }

                category.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(category).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新部件分类成功: {name} ({code})", category.Name, category.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部件分类不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新部件分类失败: {id}", category.Id);
                return (false, $"更新部件分类失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteCategoryAsync(int id)
        {
            try
            {
                if (!await CanDeleteCategoryAsync(id))
                {
                    return (false, "该分类下还有子分类或部件，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<ComponentCategory>()
                    .Where(cc => cc.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除部件分类成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部件分类不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除部件分类失败: {id}", id);
                return (false, $"删除部件分类失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var category = await GetCategoryByIdAsync(id);
                if (category == null)
                {
                    return (false, "部件分类不存在");
                }

                category.IsEnabled = !category.IsEnabled;
                category.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(category)
                    .UpdateColumns(cc => new { cc.IsEnabled, cc.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换部件分类状态成功: {id} -> {status}", id, category.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换部件分类状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<bool> IsCategoryCodeUniqueAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(cc => cc.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部件分类编码是否唯一失败: {code}", code);
                throw;
            }
        }

        public async Task<bool> CanDeleteCategoryAsync(int id)
        {
            try
            {
                // 检查是否有子分类
                var childCount = await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.ParentId == id)
                    .CountAsync();

                if (childCount > 0)
                {
                    return false;
                }

                // 检查是否有关联的部件
                var componentCount = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.CategoryId == id)
                    .CountAsync();

                return componentCount == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部件分类是否可删除失败: {id}", id);
                throw;
            }
        }

        public async Task<string> GetCategoryFullPathAsync(int id)
        {
            try
            {
                var category = await GetCategoryByIdAsync(id);
                if (category == null)
                {
                    return string.Empty;
                }

                var path = new List<string> { category.Name };
                var currentParentId = category.ParentId;

                while (currentParentId.HasValue)
                {
                    var parent = await GetCategoryByIdAsync(currentParentId.Value);
                    if (parent == null) break;
                    
                    path.Insert(0, parent.Name);
                    currentParentId = parent.ParentId;
                }

                return string.Join(" > ", path);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部件分类完整路径失败: {id}", id);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> MoveCategoryAsync(int categoryId, int? newParentId)
        {
            try
            {
                var category = await GetCategoryByIdAsync(categoryId);
                if (category == null)
                {
                    return (false, "部件分类不存在");
                }

                // 检查是否形成循环引用
                if (newParentId.HasValue && await WouldCreateCircularReference(categoryId, newParentId.Value))
                {
                    return (false, "不能将分类移动到其子分类下");
                }

                category.ParentId = newParentId;
                
                // 更新层级
                if (newParentId.HasValue)
                {
                    var parent = await GetCategoryByIdAsync(newParentId.Value);
                    if (parent == null)
                    {
                        return (false, "目标父级分类不存在");
                    }
                    category.Level = parent.Level + 1;
                }
                else
                {
                    category.Level = 1;
                }

                category.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(category)
                    .UpdateColumns(cc => new { cc.ParentId, cc.Level, cc.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("移动部件分类成功: {categoryId} -> {newParentId}", categoryId, newParentId);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动部件分类失败: {categoryId} -> {newParentId}", categoryId, newParentId);
                return (false, $"移动部件分类失败: {ex.Message}");
            }
        }

        #region 私有方法

        private List<ComponentCategory> BuildCategoryTree(List<ComponentCategory> allCategories, int? parentId)
        {
            var result = new List<ComponentCategory>();
            var children = allCategories.Where(c => c.ParentId == parentId).ToList();

            foreach (var child in children)
            {
                child.Children = BuildCategoryTree(allCategories, child.Id);
                result.Add(child);
            }

            return result;
        }

        private async Task<bool> WouldCreateCircularReference(int categoryId, int newParentId)
        {
            int? currentId = newParentId;
            while (currentId.HasValue)
            {
                if (currentId.Value == categoryId)
                {
                    return true;
                }

                var parent = await GetCategoryByIdAsync(currentId.Value);
                currentId = parent?.ParentId;
            }

            return false;
        }

        #endregion
    }
}
