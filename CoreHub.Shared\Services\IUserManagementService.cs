using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 用户管理服务接口
    /// </summary>
    public interface IUserManagementService
    {
        #region 用户管理

        /// <summary>
        /// 创建新用户
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, int? UserId)> CreateUserAsync(User user, string plainPassword);

        /// <summary>
        /// 创建新用户（简化版本）
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateUserAsync(User user);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateUserAsync(User user);

        /// <summary>
        /// 修改用户密码
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ChangePasswordAsync(int userId, string newPassword, int? operatorId = null);

        /// <summary>
        /// 启用/禁用用户
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> SetUserEnabledAsync(int userId, bool isEnabled, int? operatorId = null);

        /// <summary>
        /// 锁定/解锁用户
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> SetUserLockedAsync(int userId, bool isLocked, string? reason = null, int? operatorId = null);

        /// <summary>
        /// 锁定用户
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> LockUserAsync(int userId, string? reason = null);

        /// <summary>
        /// 解锁用户
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UnlockUserAsync(int userId);

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteUserAsync(int userId, int? operatorId = null);

        /// <summary>
        /// 重置用户登录失败次数
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ResetLoginFailureCountAsync(int userId, int? operatorId = null);

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        Task<User?> GetUserByIdAsync(int userId);

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// 获取用户列表（分页）
        /// </summary>
        Task<(List<User> Users, int TotalCount)> GetUsersAsync(int page = 1, int pageSize = 20, string? searchKeyword = null, bool? isEnabled = null, bool? isLocked = null);

        /// <summary>
        /// 获取所有用户列表
        /// </summary>
        Task<List<User>> GetAllUsersAsync();

        #endregion

        #region 角色管理

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> AssignUserRoleAsync(int userId, int roleId, int? operatorId = null, DateTime? expiresAt = null, string? remark = null);

        /// <summary>
        /// 移除用户角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> RemoveUserRoleAsync(int userId, int roleId, int? operatorId = null);

        /// <summary>
        /// 获取用户的所有角色
        /// </summary>
        Task<List<Role>> GetUserRolesAsync(int userId);

        /// <summary>
        /// 获取所有可用角色
        /// </summary>
        Task<List<Role>> GetAllRolesAsync();

        /// <summary>
        /// 创建角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateRoleAsync(Role role);

        /// <summary>
        /// 更新角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRoleAsync(Role role);

        /// <summary>
        /// 删除角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteRoleAsync(int roleId);

        /// <summary>
        /// 获取角色权限
        /// </summary>
        Task<List<Permission>> GetRolePermissionsAsync(int roleId);

        /// <summary>
        /// 更新角色权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRolePermissionsAsync(int roleId, List<int> permissionIds);

        /// <summary>
        /// 更新用户角色
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateUserRolesAsync(int userId, List<int> roleIds);

        #endregion

        #region 权限管理

        /// <summary>
        /// 为用户直接分配权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> AssignUserPermissionAsync(int userId, int permissionId, bool isGranted = true, int? operatorId = null, DateTime? expiresAt = null, string? remark = null);

        /// <summary>
        /// 移除用户直接权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> RemoveUserPermissionAsync(int userId, int permissionId, int? operatorId = null);

        /// <summary>
        /// 获取用户的所有权限（包括角色权限和直接权限）
        /// </summary>
        Task<List<Permission>> GetUserAllPermissionsAsync(int userId);

        /// <summary>
        /// 获取用户的直接权限
        /// </summary>
        Task<List<Permission>> GetUserDirectPermissionsAsync(int userId);

        /// <summary>
        /// 获取所有可用权限
        /// </summary>
        Task<List<Permission>> GetAllPermissionsAsync();

        /// <summary>
        /// 检查用户是否有指定权限
        /// </summary>
        Task<bool> CheckUserPermissionAsync(int userId, string permissionCode);

        /// <summary>
        /// 检查用户是否有指定角色
        /// </summary>
        Task<bool> CheckUserRoleAsync(int userId, string roleCode);

        /// <summary>
        /// 创建权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreatePermissionAsync(Permission permission);

        /// <summary>
        /// 更新权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdatePermissionAsync(Permission permission);

        /// <summary>
        /// 删除权限
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeletePermissionAsync(int permissionId);

        #endregion

        #region 登录日志

        /// <summary>
        /// 获取用户登录日志
        /// </summary>
        Task<List<LoginLog>> GetUserLoginLogsAsync(int userId, int page = 1, int pageSize = 20);

        /// <summary>
        /// 获取系统登录日志
        /// </summary>
        Task<(List<LoginLog> Logs, int TotalCount)> GetSystemLoginLogsAsync(int page = 1, int pageSize = 20, string? username = null, bool? isSuccess = null, DateTime? startTime = null, DateTime? endTime = null);

        #endregion
    }

    /// <summary>
    /// 登录日志实体
    /// </summary>
    public class LoginLog
    {
        public int Id { get; set; }
        public string Username { get; set; } = "";
        public string? ClientIP { get; set; }
        public bool IsSuccess { get; set; }
        public string? FailureReason { get; set; }
        public DateTime LoginTime { get; set; }
    }
} 