using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 设备型号标准部件模板DTO
    /// </summary>
    public class EquipmentModelComponentTemplateDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 设备型号ID
        /// </summary>
        public int EquipmentModelId { get; set; }

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string EquipmentModelName { get; set; } = string.Empty;

        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类名称
        /// </summary>
        public string ComponentCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 标准数量
        /// </summary>
        public int StandardQuantity { get; set; }

        /// <summary>
        /// 是否必需部件
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 更换周期描述
        /// </summary>
        public string ReplacementCycleDescription { get; set; } = string.Empty;

        /// <summary>
        /// 维护说明
        /// </summary>
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 部件类型名称
        /// </summary>
        public string ComponentTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 部件单位
        /// </summary>
        public string ComponentUnit { get; set; } = string.Empty;

        /// <summary>
        /// 部件库存数量
        /// </summary>
        public int ComponentStockQuantity { get; set; }

        /// <summary>
        /// 部件库存状态
        /// </summary>
        public string ComponentStockStatus { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 从实体转换为DTO
        /// </summary>
        public static EquipmentModelComponentTemplateDto FromEntity(
            EquipmentModelComponentTemplate entity, 
            EquipmentModel? equipmentModel = null, 
            Component? component = null)
        {
            return new EquipmentModelComponentTemplateDto
            {
                Id = entity.Id,
                EquipmentModelId = entity.EquipmentModelId,
                EquipmentModelName = equipmentModel?.Name ?? entity.EquipmentModel?.Name ?? "",
                ComponentId = entity.ComponentId,
                ComponentCode = component?.Code ?? entity.Component?.Code ?? "",
                ComponentName = component?.Name ?? entity.Component?.Name ?? "",
                ComponentCategoryName = component?.Category?.Name ?? "",
                StandardQuantity = entity.StandardQuantity,
                IsRequired = entity.IsRequired,
                ReplacementCycleDays = entity.ReplacementCycleDays,
                ReplacementCycleDescription = entity.ReplacementCycleDescription,
                MaintenanceNotes = entity.MaintenanceNotes,
                SortOrder = entity.SortOrder,
                IsEnabled = entity.IsEnabled,
                ComponentTypeName = entity.ComponentTypeName,
                ComponentUnit = component?.Unit ?? entity.Component?.Unit ?? "",
                ComponentStockQuantity = component?.StockQuantity ?? entity.Component?.StockQuantity ?? 0,
                ComponentStockStatus = component?.StockStatusName ?? entity.Component?.StockStatusName ?? "",
                CreatedAt = entity.CreatedAt,
                Remark = entity.Remark
            };
        }

        /// <summary>
        /// 转换为实体
        /// </summary>
        public EquipmentModelComponentTemplate ToEntity()
        {
            return new EquipmentModelComponentTemplate
            {
                Id = Id,
                EquipmentModelId = EquipmentModelId,
                ComponentId = ComponentId,
                StandardQuantity = StandardQuantity,
                IsRequired = IsRequired,
                ReplacementCycleDays = ReplacementCycleDays,
                MaintenanceNotes = MaintenanceNotes,
                SortOrder = SortOrder,
                IsEnabled = IsEnabled,
                Remark = Remark
            };
        }
    }
}
