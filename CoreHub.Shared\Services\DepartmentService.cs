using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部门服务实现
    /// </summary>
    public class DepartmentService : IDepartmentService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<DepartmentService> _logger;

        public DepartmentService(DatabaseContext dbContext, ILogger<DepartmentService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<Department>> GetAllDepartmentsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Department>()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有部门失败");
                throw;
            }
        }

        public async Task<Department?> GetDepartmentByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取部门失败: {id}", id);
                throw;
            }
        }

        public async Task<Department?> GetDepartmentByCodeAsync(string code)
        {
            try
            {
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Code == code)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取部门失败: {code}", code);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateDepartmentAsync(Department department)
        {
            try
            {
                // 检查编码是否存在
                if (await IsCodeExistsAsync(department.Code))
                {
                    return (false, "部门编码已存在");
                }

                // 检查名称是否存在
                if (await IsNameExistsAsync(department.Name))
                {
                    return (false, "部门名称已存在");
                }

                department.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(department).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建部门成功: {name} ({code})", department.Name, department.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建部门失败: {name}", department.Name);
                return (false, $"创建部门失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateDepartmentAsync(Department department)
        {
            try
            {
                // 检查编码是否存在（排除自己）
                if (await IsCodeExistsAsync(department.Code, department.Id))
                {
                    return (false, "部门编码已存在");
                }

                // 检查名称是否存在（排除自己）
                if (await IsNameExistsAsync(department.Name, department.Id))
                {
                    return (false, "部门名称已存在");
                }

                department.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(department).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新部门成功: {name} ({code})", department.Name, department.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部门不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新部门失败: {id}", department.Id);
                return (false, $"更新部门失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteDepartmentAsync(int id)
        {
            try
            {
                // 检查是否有子部门
                var hasChildren = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.ParentId == id)
                    .AnyAsync();

                if (hasChildren)
                {
                    return (false, "该部门下还有子部门，无法删除");
                }

                // 检查是否有关联的设备
                var hasEquipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.DepartmentId == id)
                    .AnyAsync();

                if (hasEquipment)
                {
                    return (false, "该部门下还有设备，无法删除");
                }

                // 检查是否有关联的位置
                var hasLocations = await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.DepartmentId == id)
                    .AnyAsync();

                if (hasLocations)
                {
                    return (false, "该部门下还有位置，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<Department>()
                    .Where(d => d.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除部门成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部门不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除部门失败: {id}", id);
                return (false, $"删除部门失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var department = await GetDepartmentByIdAsync(id);
                if (department == null)
                {
                    return (false, "部门不存在");
                }

                department.IsEnabled = !department.IsEnabled;
                department.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(department)
                    .UpdateColumns(d => new { d.IsEnabled, d.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换部门状态成功: {id} -> {status}", id, department.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换部门状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<List<Department>> GetEnabledDepartmentsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的部门列表失败");
                throw;
            }
        }

        public async Task<List<Department>> GetDepartmentTreeAsync()
        {
            try
            {
                var allDepartments = await GetAllDepartmentsAsync();
                return BuildDepartmentTree(allDepartments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门树形结构失败");
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(d => d.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部门编码是否存在失败: {code}", code);
                throw;
            }
        }

        public async Task<bool> IsNameExistsAsync(string name, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Name == name);

                if (excludeId.HasValue)
                {
                    query = query.Where(d => d.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部门名称是否存在失败: {name}", name);
                throw;
            }
        }

        private List<Department> BuildDepartmentTree(List<Department> departments)
        {
            var departmentDict = departments.ToDictionary(d => d.Id);
            var rootDepartments = new List<Department>();

            foreach (var department in departments)
            {
                if (department.ParentId.HasValue && departmentDict.ContainsKey(department.ParentId.Value))
                {
                    var parent = departmentDict[department.ParentId.Value];
                    parent.Children.Add(department);
                    department.Parent = parent;
                }
                else
                {
                    rootDepartments.Add(department);
                }
            }

            return rootDepartments;
        }
    }
}
