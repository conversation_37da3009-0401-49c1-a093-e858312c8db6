@using CoreHub.Shared.Models.Dto
@using MudBlazor
@namespace CoreHub.Shared.Components

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="Icons.Material.Filled.Preview" Class="mr-2" />
                批量操作影响预览
            </MudText>

            @if (Impact != null)
            {
                <!-- 操作摘要 -->
                <MudPaper Class="pa-4 mb-4" Elevation="2">
                    <MudGrid>
                        <MudItem xs="12" md="6">
                            <MudStack Spacing="2">
                                <MudText Typo="Typo.subtitle1" Color="Color.Primary">
                                    <MudIcon Icon="Icons.Material.Filled.Info" Size="Size.Small" Class="mr-1" />
                                    操作信息
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    <strong>操作类型：</strong>@Impact.OperationType
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    <strong>影响设备：</strong>@Impact.AffectedEquipmentCount 台
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    <strong>涉及部件：</strong>@Impact.AffectedComponentCount 种
                                </MudText>
                            </MudStack>
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudStack Spacing="2">
                                <MudText Typo="Typo.subtitle1" Color="Color.Secondary">
                                    <MudIcon Icon="Icons.Material.Filled.Assessment" Size="Size.Small" Class="mr-1" />
                                    预期变更
                                </MudText>
                                @if (Impact.NewConfigurationCount > 0)
                                {
                                    <MudText Typo="Typo.body2" Color="Color.Success">
                                        <MudIcon Icon="Icons.Material.Filled.Add" Size="Size.Small" Class="mr-1" />
                                        新增配置：@Impact.NewConfigurationCount 个
                                    </MudText>
                                }
                                @if (Impact.UpdatedConfigurationCount > 0)
                                {
                                    <MudText Typo="Typo.body2" Color="Color.Warning">
                                        <MudIcon Icon="Icons.Material.Filled.Edit" Size="Size.Small" Class="mr-1" />
                                        更新配置：@Impact.UpdatedConfigurationCount 个
                                    </MudText>
                                }
                                @if (Impact.DeletedConfigurationCount > 0)
                                {
                                    <MudText Typo="Typo.body2" Color="Color.Error">
                                        <MudIcon Icon="Icons.Material.Filled.Delete" Size="Size.Small" Class="mr-1" />
                                        删除配置：@Impact.DeletedConfigurationCount 个
                                    </MudText>
                                }
                                <MudText Typo="Typo.body2">
                                    <MudIcon Icon="Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-1" />
                                    预计耗时：@Impact.EstimatedExecutionTimeSeconds 秒
                                </MudText>
                            </MudStack>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

                <!-- 操作摘要 -->
                @if (!string.IsNullOrEmpty(Impact.Summary))
                {
                    <MudAlert Severity="Severity.Info" Class="mb-4">
                        @Impact.Summary
                    </MudAlert>
                }

                <!-- 详细影响列表 -->
                @if (Impact.ImpactDetails.Any())
                {
                    <MudText Typo="Typo.h6" Class="mb-3">详细影响</MudText>
                    
                    <MudDataGrid Items="@Impact.ImpactDetails" 
                               Filterable="true" 
                               FilterMode="DataGridFilterMode.Simple"
                               SortMode="SortMode.Multiple"
                               Groupable="false"
                               Height="400px"
                               FixedHeader="true">
                        <Columns>
                            <PropertyColumn Property="x => x.EquipmentName" Title="设备名称" />
                            <PropertyColumn Property="x => x.OperationType" Title="操作类型" />
                            <TemplateColumn Title="影响部件">
                                <CellTemplate>
                                    <MudChip Size="Size.Small" Color="Color.Info">
                                        @context.Item.ComponentImpacts.Count 个部件
                                    </MudChip>
                                </CellTemplate>
                            </TemplateColumn>
                            <TemplateColumn Title="状态">
                                <CellTemplate>
                                    <MudChip Size="Size.Small" 
                                           Color="@GetStatusColor(context.Item.Status)">
                                        @context.Item.Status
                                    </MudChip>
                                </CellTemplate>
                            </TemplateColumn>
                            <PropertyColumn Property="x => x.Remark" Title="备注" />
                        </Columns>
                    </MudDataGrid>
                }
                else
                {
                    <MudAlert Severity="Severity.Warning">
                        没有找到具体的影响详情。
                    </MudAlert>
                }

                <!-- 风险提示 -->
                @if (HasRisks())
                {
                    <MudAlert Severity="Severity.Warning" Class="mt-4">
                        <MudText Typo="Typo.subtitle2" Class="mb-2">
                            <MudIcon Icon="Icons.Material.Filled.Warning" Class="mr-1" />
                            操作风险提示
                        </MudText>
                        <MudList T="string" Dense="true">
                            @if (Impact.UpdatedConfigurationCount > 0 || Impact.DeletedConfigurationCount > 0)
                            {
                                <MudListItem T="string" Icon="Icons.Material.Filled.Info">
                                    此操作将修改或删除现有配置，请确认操作的正确性
                                </MudListItem>
                            }
                            @if (Impact.AffectedEquipmentCount > 10)
                            {
                                <MudListItem T="string" Icon="Icons.Material.Filled.Info">
                                    此操作将影响大量设备（@Impact.AffectedEquipmentCount 台），建议先在少量设备上测试
                                </MudListItem>
                            }
                            @if (Impact.EstimatedExecutionTimeSeconds > 30)
                            {
                                <MudListItem T="string" Icon="Icons.Material.Filled.Info">
                                    此操作预计耗时较长（@Impact.EstimatedExecutionTimeSeconds 秒），请耐心等待
                                </MudListItem>
                            }
                        </MudList>
                    </MudAlert>
                }
            }
            else
            {
                <MudAlert Severity="Severity.Error">
                    无法获取操作影响预览信息。
                </MudAlert>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" Color="Color.Secondary">
            取消
        </MudButton>
        <MudButton OnClick="Confirm" Color="Color.Primary" Variant="Variant.Filled">
            确认执行
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public BatchImpactPreviewDto? Impact { get; set; }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private void Confirm()
    {
        MudDialog.Close(DialogResult.Ok(true));
    }

    private Color GetStatusColor(string status)
    {
        return status.ToLower() switch
        {
            "ready" or "准备就绪" => Color.Success,
            "warning" or "警告" => Color.Warning,
            "error" or "错误" => Color.Error,
            _ => Color.Default
        };
    }

    private bool HasRisks()
    {
        if (Impact == null) return false;

        return Impact.UpdatedConfigurationCount > 0 ||
               Impact.DeletedConfigurationCount > 0 ||
               Impact.AffectedEquipmentCount > 10 ||
               Impact.EstimatedExecutionTimeSeconds > 30;
    }
}
