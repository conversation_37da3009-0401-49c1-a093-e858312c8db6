using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备部件关联表
    /// 存储实际的设备-部件关联关系
    /// </summary>
    [SugarTable("EquipmentComponents")]
    public class EquipmentComponent
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [Required]
        public int EquipmentId { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        [Required]
        public int ComponentId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 是否必需部件
        /// </summary>
        [Required]
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 安装日期
        /// </summary>
        public DateTime? InstallDate { get; set; }

        /// <summary>
        /// 上次更换日期
        /// </summary>
        public DateTime? LastReplacementDate { get; set; }

        /// <summary>
        /// 下次更换日期
        /// </summary>
        public DateTime? NextReplacementDate { get; set; }

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 维护说明
        /// </summary>
        [MaxLength(500)]
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 状态：1-正常, 2-需要维护, 3-需要更换, 4-已损坏
        /// </summary>
        [Required]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [Required]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(200)]
        public string? Remark { get; set; }

        /// <summary>
        /// 状态名称（计算属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "正常",
            2 => "需要维护",
            3 => "需要更换",
            4 => "已损坏",
            _ => "未知"
        };

        /// <summary>
        /// 是否需要维护（计算属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool NeedsMaintenance => Status == 2 || Status == 3;

        /// <summary>
        /// 是否过期需要更换（计算属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsOverdue => NextReplacementDate.HasValue && NextReplacementDate.Value < DateTime.Now;

        /// <summary>
        /// 距离下次更换的天数（计算属性）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? DaysUntilReplacement => NextReplacementDate.HasValue 
            ? (int)(NextReplacementDate.Value - DateTime.Now).TotalDays 
            : null;
    }
}
