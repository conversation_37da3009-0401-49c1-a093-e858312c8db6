# 🔄 多项目环境管理指南

## 🚨 问题描述

如果您有多个类似的项目（如 CoreHub、ProjectA、ProjectB），使用相同的环境变量名会导致冲突：

```
所有项目都使用相同的环境变量：
- ASPNETCORE_ENVIRONMENT
- API_BASE_URL
- DB_CONNECTION_STRING
- USE_HTTPS_REDIRECTION
```

**结果：所有项目使用相同的配置！**

## 🎯 解决方案

### 方案一：项目特定环境变量（推荐）

#### CoreHub 项目使用：
```
COREHUB_ENVIRONMENT=Production
COREHUB_API_BASE_URL=https://api.saintyeartex.com:8081
COREHUB_DB_CONNECTION_STRING=Server=...;Database=CoreHub;...
COREHUB_USE_HTTPS_REDIRECTION=true
```

#### ProjectA 项目使用：
```
PROJECTA_ENVIRONMENT=Development
PROJECTA_API_BASE_URL=http://localhost:5000
PROJECTA_DB_CONNECTION_STRING=Server=...;Database=ProjectA;...
PROJECTA_USE_HTTPS_REDIRECTION=false
```

#### ProjectB 项目使用：
```
PROJECTB_ENVIRONMENT=Staging
PROJECTB_API_BASE_URL=https://api-staging.projectb.com
PROJECTB_DB_CONNECTION_STRING=Server=...;Database=ProjectB;...
PROJECTB_USE_HTTPS_REDIRECTION=true
```

### 方案二：目录隔离

每个项目在不同目录下运行，使用不同的配置文件：

```
D:\Projects\
├── CoreHub\
│   ├── appsettings.json (CoreHub配置)
│   └── Scripts\
├── ProjectA\
│   ├── appsettings.json (ProjectA配置)
│   └── Scripts\
└── ProjectB\
    ├── appsettings.json (ProjectB配置)
    └── Scripts\
```

### 方案三：Docker容器隔离

每个项目使用独立的Docker容器，环境变量在容器内隔离：

```yaml
# CoreHub容器
services:
  corehub:
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - API_BASE_URL=https://api.saintyeartex.com:8081

# ProjectA容器  
services:
  projecta:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - API_BASE_URL=http://localhost:5000
```

## 🛠️ CoreHub 实现

### 已更新的代码
CoreHub 的 `EnvironmentConfig.cs` 已支持项目特定环境变量：

```csharp
// 优先使用项目特定的环境变量
public static string CurrentEnvironment => 
    Environment.GetEnvironmentVariable("COREHUB_ENVIRONMENT") ??     // 项目特定
    Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ??  // 通用
    "Development";

public static string GetApiBaseUrl()
{
    var envUrl = Environment.GetEnvironmentVariable("COREHUB_API_BASE_URL") ??  // 项目特定
                 Environment.GetEnvironmentVariable("API_BASE_URL");            // 通用
    // ...
}
```

### 新增的脚本
- `设置CoreHub开发环境.bat` - 设置项目特定的开发环境
- `设置CoreHub生产环境.bat` - 设置项目特定的生产环境

## 🎯 使用建议

### 单项目场景
如果您只有一个项目，继续使用通用环境变量：
```cmd
Scripts\设置开发环境.bat
Scripts\设置生产环境.bat
```

### 多项目场景
如果您有多个项目，使用项目特定环境变量：
```cmd
Scripts\设置CoreHub开发环境.bat
Scripts\设置CoreHub生产环境.bat
```

## 📋 环境变量对照表

| 通用环境变量 | CoreHub特定环境变量 | 说明 |
|-------------|-------------------|------|
| `ASPNETCORE_ENVIRONMENT` | `COREHUB_ENVIRONMENT` | 环境名称 |
| `API_BASE_URL` | `COREHUB_API_BASE_URL` | API基础地址 |
| `DB_CONNECTION_STRING` | `COREHUB_DB_CONNECTION_STRING` | 数据库连接字符串 |
| `USE_HTTPS_REDIRECTION` | `COREHUB_USE_HTTPS_REDIRECTION` | HTTPS重定向 |
| `ENABLE_VERBOSE_LOGGING` | `COREHUB_ENABLE_VERBOSE_LOGGING` | 详细日志 |

## 🔍 验证配置

### 检查当前环境变量
```cmd
# 查看所有CoreHub相关环境变量
set | findstr COREHUB

# 查看通用环境变量
set | findstr ASPNETCORE
set | findstr API_BASE_URL
```

### 在代码中验证
```csharp
// 显示当前使用的配置源
Console.WriteLine($"Environment: {EnvironmentConfig.CurrentEnvironment}");
Console.WriteLine($"API URL: {EnvironmentConfig.GetApiBaseUrl()}");

// 显示环境变量来源
var coreHubEnv = Environment.GetEnvironmentVariable("COREHUB_ENVIRONMENT");
var genericEnv = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
Console.WriteLine($"CoreHub Env: {coreHubEnv ?? "Not Set"}");
Console.WriteLine($"Generic Env: {genericEnv ?? "Not Set"}");
```

## 💡 最佳实践

### 1. 命名规范
- 项目特定：`{PROJECT_NAME}_{VARIABLE_NAME}`
- 示例：`COREHUB_API_BASE_URL`、`PROJECTA_DB_CONNECTION`

### 2. 优先级设计
```
项目特定环境变量 > 通用环境变量 > 默认配置
```

### 3. 文档管理
为每个项目维护独立的环境变量文档：
- `CoreHub_环境变量.md`
- `ProjectA_环境变量.md`
- `ProjectB_环境变量.md`

### 4. 部署策略
- **开发环境**：使用项目特定环境变量
- **测试环境**：使用Docker容器隔离
- **生产环境**：使用项目特定环境变量 + 配置管理工具

## 🚀 迁移步骤

### 从通用环境变量迁移到项目特定环境变量

1. **备份当前配置**
   ```cmd
   Scripts\查看当前环境.bat > 当前配置备份.txt
   ```

2. **清除通用环境变量**
   ```cmd
   Scripts\清除环境变量.bat
   ```

3. **设置项目特定环境变量**
   ```cmd
   Scripts\设置CoreHub开发环境.bat
   ```

4. **验证配置**
   ```cmd
   Scripts\查看当前环境.bat
   ```

5. **重启应用程序**

## ⚠️ 注意事项

1. **向后兼容**：代码同时支持新旧环境变量，迁移过程中不会中断
2. **优先级**：项目特定环境变量优先级更高
3. **团队协作**：确保团队成员了解新的环境变量命名规范
4. **CI/CD**：更新部署脚本以使用新的环境变量名

---

通过这种方式，您可以在同一台机器上运行多个项目，每个项目使用独立的配置，互不干扰！
