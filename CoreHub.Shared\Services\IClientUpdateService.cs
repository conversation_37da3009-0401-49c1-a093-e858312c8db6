using CoreHub.Shared.Models.AppUpdate;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 客户端更新服务接口
    /// </summary>
    public interface IClientUpdateService
    {
        /// <summary>
        /// 检查更新
        /// </summary>
        /// <param name="silent">是否静默检查</param>
        /// <returns>更新检查结果</returns>
        Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = false);

        /// <summary>
        /// 下载更新
        /// </summary>
        /// <param name="versionInfo">版本信息</param>
        /// <param name="progress">下载进度回调</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>下载结果</returns>
        Task<(bool IsSuccess, string? FilePath, string? ErrorMessage)> DownloadUpdateAsync(
            VersionInfo versionInfo, 
            IProgress<DownloadProgress>? progress = null, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 安装更新
        /// </summary>
        /// <param name="filePath">更新文件路径</param>
        /// <returns>安装结果</returns>
        Task<(bool IsSuccess, string? ErrorMessage)> InstallUpdateAsync(string filePath);

        /// <summary>
        /// 验证更新文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedMd5">期望的MD5值</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateUpdateFileAsync(string filePath, string expectedMd5);

        /// <summary>
        /// 获取当前应用版本信息
        /// </summary>
        /// <returns>当前版本信息</returns>
        Task<(string VersionNumber, int VersionCode)> GetCurrentVersionAsync();

        /// <summary>
        /// 获取设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        Task<DeviceInfo> GetDeviceInfoAsync();

        /// <summary>
        /// 清理旧的更新文件
        /// </summary>
        /// <returns>清理结果</returns>
        Task<bool> CleanupOldUpdatesAsync();

        /// <summary>
        /// 设置更新检查间隔
        /// </summary>
        /// <param name="intervalHours">检查间隔（小时）</param>
        void SetUpdateCheckInterval(int intervalHours);

        /// <summary>
        /// 启动自动更新检查
        /// </summary>
        void StartAutoUpdateCheck();

        /// <summary>
        /// 停止自动更新检查
        /// </summary>
        void StopAutoUpdateCheck();

        /// <summary>
        /// 更新检查事件
        /// </summary>
        event EventHandler<UpdateCheckResponse>? UpdateAvailable;

        /// <summary>
        /// 下载进度事件
        /// </summary>
        event EventHandler<DownloadProgress>? DownloadProgressChanged;

        /// <summary>
        /// 下载完成事件
        /// </summary>
        event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;
    }

    /// <summary>
    /// 下载进度信息
    /// </summary>
    public class DownloadProgress
    {
        /// <summary>
        /// 已下载字节数
        /// </summary>
        public long BytesReceived { get; set; }

        /// <summary>
        /// 总字节数
        /// </summary>
        public long TotalBytesToReceive { get; set; }

        /// <summary>
        /// 下载进度百分比 (0-100)
        /// </summary>
        public double ProgressPercentage => TotalBytesToReceive > 0 ? 
            (double)BytesReceived / TotalBytesToReceive * 100 : 0;

        /// <summary>
        /// 下载速度 (字节/秒)
        /// </summary>
        public long BytesPerSecond { get; set; }

        /// <summary>
        /// 剩余时间估计
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }

        /// <summary>
        /// 格式化的已下载大小
        /// </summary>
        public string FormattedBytesReceived => FormatFileSize(BytesReceived);

        /// <summary>
        /// 格式化的总大小
        /// </summary>
        public string FormattedTotalBytes => FormatFileSize(TotalBytesToReceive);

        /// <summary>
        /// 格式化的下载速度
        /// </summary>
        public string FormattedSpeed => $"{FormatFileSize(BytesPerSecond)}/s";

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// 下载完成事件参数
    /// </summary>
    public class DownloadCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 下载的文件路径
        /// </summary>
        public string? FilePath { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 版本信息
        /// </summary>
        public VersionInfo? VersionInfo { get; set; }

        /// <summary>
        /// 是否已取消
        /// </summary>
        public bool IsCancelled { get; set; }
    }
}
