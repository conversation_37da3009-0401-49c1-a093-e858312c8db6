using CoreHub.Shared.Services;

namespace CoreHub.Services
{
    /// <summary>
    /// 默认的更新UI服务实现（用于非Android平台）
    /// </summary>
    public class DefaultUpdateUIService : IUpdateUIService
    {
        /// <summary>
        /// 手动检查更新并显示更新页面
        /// </summary>
        public async Task CheckForUpdateAsync()
        {
            await MainThread.InvokeOnMainThreadAsync(async () =>
            {
                await Application.Current?.MainPage?.DisplayAlert("提示", "当前平台不支持自动更新功能", "确定")!;
            });
        }

        /// <summary>
        /// 显示更新页面
        /// </summary>
        public async Task ShowUpdatePageAsync()
        {
            await MainThread.InvokeOnMainThreadAsync(async () =>
            {
                await Application.Current?.MainPage?.DisplayAlert("提示", "当前平台不支持自动更新功能", "确定")!;
            });
        }
    }
}
