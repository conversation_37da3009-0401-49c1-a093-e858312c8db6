@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using MudBlazor
@inject IEquipmentModelComponentTemplateService TemplateService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="Icons.Material.Filled.FileCopy" Class="mr-2" />
                复制设备型号模板
            </MudText>
            
            <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-4">
                将一个设备型号的部件模板复制到另一个设备型号，可以快速建立相似设备的标准配置。
            </MudText>

            <MudForm @ref="form">
                <MudGrid>
                    <MudItem xs="12">
                        <MudSelect T="int?" @bind-Value="sourceModelId"
                                 Label="源设备型号"
                                 Required="true"
                                 Placeholder="选择要复制模板的源设备型号"
                                 OnSelectionChanged="OnSourceModelChanged">
                            @if (EquipmentModels != null)
                            {
                                @foreach (var model in EquipmentModels)
                                {
                                    <MudSelectItem T="int?" Value="@((int?)model.Id)">
                                        @model.Name (@model.Code)
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>
                        @if (sourceTemplateCount > 0)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Info" Class="mt-1">
                                该设备型号有 @sourceTemplateCount 个部件模板
                            </MudText>
                        }
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudSelect T="int?" @bind-Value="targetModelId"
                                 Label="目标设备型号"
                                 Required="true"
                                 Placeholder="选择要接收模板的目标设备型号">
                            @if (EquipmentModels != null)
                            {
                                @foreach (var model in EquipmentModels.Where(m => m.Id != sourceModelId))
                                {
                                    <MudSelectItem T="int?" Value="@((int?)model.Id)">
                                        @model.Name (@model.Code)
                                    </MudSelectItem>
                                }
                            }
                        </MudSelect>
                        @if (targetTemplateCount > 0)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                该设备型号已有 @targetTemplateCount 个部件模板，复制时会跳过重复的部件
                            </MudText>
                        }
                    </MudItem>

                    <MudItem xs="12">
                        <MudCheckBox T="bool" @bind-Value="copyOnlyRequired"
                                   Label="仅复制必需部件"
                                   Color="Color.Primary" />
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            勾选此选项将只复制标记为"必需"的部件模板
                        </MudText>
                    </MudItem>

                    <MudItem xs="12">
                        <MudTextField @bind-Value="remark"
                                    Label="复制备注"
                                    Lines="2"
                                    Placeholder="可选，描述此次复制操作的目的" />
                    </MudItem>
                </MudGrid>
            </MudForm>

            @if (previewResult != null)
            {
                <MudDivider Class="my-4" />
                <MudText Typo="Typo.h6" Class="mb-3">
                    <MudIcon Icon="Icons.Material.Filled.Preview" Class="mr-2" />
                    复制预览
                </MudText>
                
                <MudAlert Severity="Severity.Info" Class="mb-3">
                    将从 <strong>@GetModelName(sourceModelId)</strong> 复制 <strong>@previewResult.WillCopyCount</strong> 个部件模板到 <strong>@GetModelName(targetModelId)</strong>
                    @if (previewResult.SkipCount > 0)
                    {
                        <text><br />跳过 @previewResult.SkipCount 个已存在的部件模板</text>
                    }
                </MudAlert>

                @if (previewResult.ComponentNames.Any())
                {
                    <MudText Typo="Typo.subtitle2" Class="mb-2">将复制的部件：</MudText>
                    <MudChipSet T="string">
                        @foreach (var componentName in previewResult.ComponentNames)
                        {
                            <MudChip T="string" Size="Size.Small" Color="Color.Primary">@componentName</MudChip>
                        }
                    </MudChipSet>
                }
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" Color="Color.Secondary">
            取消
        </MudButton>
        <MudButton OnClick="Preview" 
                 Color="Color.Info" 
                 Variant="Variant.Outlined"
                 Disabled="@(!CanPreview() || isLoading)">
            @if (isLoading)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            预览
        </MudButton>
        <MudButton OnClick="Submit" 
                 Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 Disabled="@(!CanExecute() || isExecuting)">
            @if (isExecuting)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
            }
            执行复制
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public List<EquipmentModel> EquipmentModels { get; set; } = new();

    private MudForm form = null!;
    private bool isLoading = false;
    private bool isExecuting = false;
    
    private int? sourceModelId;
    private int? targetModelId;
    private bool copyOnlyRequired = false;
    private string remark = string.Empty;
    
    private int sourceTemplateCount = 0;
    private int targetTemplateCount = 0;
    private CopyPreviewResult? previewResult;

    private async Task OnSourceModelChanged(int? modelId)
    {
        sourceModelId = modelId;
        previewResult = null;
        
        if (modelId.HasValue)
        {
            try
            {
                var templates = await TemplateService.GetTemplatesByEquipmentModelAsync(modelId.Value);
                sourceTemplateCount = templates.Count;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"获取源模板数量失败: {ex.Message}", Severity.Error);
            }
        }
        else
        {
            sourceTemplateCount = 0;
        }
    }

    private async Task OnTargetModelChanged(int? modelId)
    {
        targetModelId = modelId;
        previewResult = null;
        
        if (modelId.HasValue)
        {
            try
            {
                var templates = await TemplateService.GetTemplatesByEquipmentModelAsync(modelId.Value);
                targetTemplateCount = templates.Count;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"获取目标模板数量失败: {ex.Message}", Severity.Error);
            }
        }
        else
        {
            targetTemplateCount = 0;
        }
    }

    private async Task Preview()
    {
        if (!CanPreview()) return;

        try
        {
            isLoading = true;
            StateHasChanged();

            // 获取源模板
            var sourceTemplates = await TemplateService.GetTemplatesByEquipmentModelAsync(sourceModelId!.Value);
            if (copyOnlyRequired)
            {
                sourceTemplates = sourceTemplates.Where(t => t.IsRequired).ToList();
            }

            // 获取目标模板
            var targetTemplates = await TemplateService.GetTemplatesByEquipmentModelAsync(targetModelId!.Value);
            var existingComponentIds = targetTemplates.Select(t => t.ComponentId).ToHashSet();

            // 计算将要复制的模板
            var willCopyTemplates = sourceTemplates.Where(t => !existingComponentIds.Contains(t.ComponentId)).ToList();

            previewResult = new CopyPreviewResult
            {
                WillCopyCount = willCopyTemplates.Count,
                SkipCount = sourceTemplates.Count - willCopyTemplates.Count,
                ComponentNames = willCopyTemplates.Select(t => t.ComponentName).ToList()
            };
        }
        catch (Exception ex)
        {
            Snackbar.Add($"预览失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task Submit()
    {
        if (!CanExecute()) return;

        try
        {
            isExecuting = true;
            StateHasChanged();

            var result = await TemplateService.CopyTemplatesToEquipmentModelAsync(sourceModelId!.Value, targetModelId!.Value);
            
            if (result.IsSuccess)
            {
                Snackbar.Add("模板复制成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"复制失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"复制失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isExecuting = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private bool CanPreview()
    {
        return sourceModelId.HasValue && targetModelId.HasValue && sourceModelId != targetModelId;
    }

    private bool CanExecute()
    {
        return CanPreview() && previewResult != null && previewResult.WillCopyCount > 0;
    }

    private string GetModelName(int? modelId)
    {
        if (!modelId.HasValue) return "";
        var model = EquipmentModels.FirstOrDefault(m => m.Id == modelId.Value);
        return model?.Name ?? "";
    }

    private class CopyPreviewResult
    {
        public int WillCopyCount { get; set; }
        public int SkipCount { get; set; }
        public List<string> ComponentNames { get; set; } = new();
    }
}
