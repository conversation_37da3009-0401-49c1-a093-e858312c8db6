@page "/equipment-component-details"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@inject IEquipmentComponentDetailService EquipmentComponentDetailService
@inject IEquipmentService EquipmentService
@inject IEquipmentModelService EquipmentModelService
@inject IDepartmentService DepartmentService
@inject IComponentCategoryService ComponentCategoryService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备部件明细</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
    <!-- 页面标题和统计信息 -->
    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
        <MudText Typo="Typo.h4" Color="Color.Primary">
            <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" />
            设备部件明细
        </MudText>
        
        @if (statistics != null)
        {
            <MudStack Row Spacing="4">
                <MudPaper Class="pa-2" Elevation="1">
                    <MudStack AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.caption">设备总数</MudText>
                        <MudText Typo="Typo.h6" Color="Color.Primary">@statistics.TotalEquipmentCount</MudText>
                    </MudStack>
                </MudPaper>
                <MudPaper Class="pa-2" Elevation="1">
                    <MudStack AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.caption">部件类型</MudText>
                        <MudText Typo="Typo.h6" Color="Color.Info">@statistics.TotalComponentTypeCount</MudText>
                    </MudStack>
                </MudPaper>
                <MudPaper Class="pa-2" Elevation="1">
                    <MudStack AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.caption">部件总数</MudText>
                        <MudText Typo="Typo.h6" Color="Color.Success">@statistics.TotalComponentQuantity</MudText>
                    </MudStack>
                </MudPaper>
                <MudPaper Class="pa-2" Elevation="1">
                    <MudStack AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.caption">库存不足</MudText>
                        <MudText Typo="Typo.h6" Color="Color.Warning">@statistics.LowStockComponentCount</MudText>
                    </MudStack>
                </MudPaper>
            </MudStack>
        }
    </MudStack>

    <!-- 搜索和过滤区域 -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid>
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="searchKeyword"
                            Label="搜索关键词"
                            Placeholder="设备名称、部件名称、编码等"
                            Adornment="Adornment.Start"
                            AdornmentIcon="@Icons.Material.Filled.Search"
                            OnKeyUp="OnSearchKeyUp"
                            Clearable="true" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="int?" @bind-Value="selectedEquipmentId"
                         Label="选择设备"
                         Clearable="true"
                         OnClearButtonClick="() => { selectedEquipmentId = null; FilterData(); }">
                    @if (equipment != null)
                    {
                        @foreach (var eq in equipment)
                        {
                            <MudSelectItem T="int?" Value="@((int?)eq.Id)">@eq.Name (@eq.Code)</MudSelectItem>
                        }
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="int?" @bind-Value="selectedModelId"
                         Label="设备型号"
                         Clearable="true"
                         OnClearButtonClick="() => { selectedModelId = null; FilterData(); }">
                    @if (equipmentModels != null)
                    {
                        @foreach (var model in equipmentModels)
                        {
                            <MudSelectItem T="int?" Value="@((int?)model.Id)">@model.Name</MudSelectItem>
                        }
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="int?" @bind-Value="selectedDepartmentId"
                         Label="所属部门"
                         Clearable="true"
                         OnClearButtonClick="() => { selectedDepartmentId = null; FilterData(); }">
                    @if (departments != null)
                    {
                        @foreach (var dept in departments)
                        {
                            <MudSelectItem T="int?" Value="@((int?)dept.Id)">@dept.Name</MudSelectItem>
                        }
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="int?" @bind-Value="selectedComponentCategoryId"
                         Label="部件分类"
                         Clearable="true"
                         OnClearButtonClick="() => { selectedComponentCategoryId = null; FilterData(); }">
                    @if (componentCategories != null)
                    {
                        @foreach (var category in componentCategories)
                        {
                            <MudSelectItem T="int?" Value="@((int?)category.Id)">@category.Name</MudSelectItem>
                        }
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="1">
                <MudStack Row Spacing="2">
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Primary"
                             StartIcon="@Icons.Material.Filled.Refresh"
                             OnClick="LoadData">
                        刷新
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 操作按钮区域 -->
    <MudStack Row Spacing="2" Class="mb-4">
        <MudButton Variant="Variant.Outlined"
                 Color="Color.Info"
                 StartIcon="@Icons.Material.Filled.Download"
                 OnClick="ExportData">
            导出数据
        </MudButton>
        <MudButton Variant="Variant.Outlined"
                 Color="Color.Warning"
                 StartIcon="@Icons.Material.Filled.Warning"
                 OnClick="ShowLowStockComponents">
            库存预警
        </MudButton>
        <MudButton Variant="Variant.Outlined"
                 Color="Color.Secondary"
                 StartIcon="@Icons.Material.Filled.Schedule"
                 OnClick="ShowMaintenanceDueComponents">
            维护提醒
        </MudButton>

        <MudButton Variant="Variant.Outlined"
                 Color="Color.Error"
                 StartIcon="@Icons.Material.Filled.DeleteSweep"
                 OnClick="BatchDeleteSelected"
                 Disabled="@(!selectedItems.Any())">
            批量删除 (@selectedItems.Count)
        </MudButton>
    </MudStack>

    <!-- 数据表格 -->
    <MudDataGrid T="EquipmentComponentDetailDto"
                Items="@filteredDetails"
                Loading="@loading"
                Hover="true"
                Striped="true"
                Dense="true"
                FixedHeader="true"
                Height="600px"
                Groupable="true"
                GroupExpanded="true"
                MultiSelection="true"
                SelectedItems="selectedItems"
                SelectedItemsChanged="OnSelectedItemsChanged">
        <Columns>
            <PropertyColumn Property="x => x.DepartmentName" Title="部门" Grouping />
            <PropertyColumn Property="x => x.EquipmentName" Title="设备名称" />
            <PropertyColumn Property="x => x.EquipmentCode" Title="设备编码" />
            <PropertyColumn Property="x => x.EquipmentModelName" Title="设备型号" />
            <PropertyColumn Property="x => x.LocationName" Title="位置" />
            <TemplateColumn Title="设备状态" Sortable="false">
                <CellTemplate>
                    <MudChip Color="@GetEquipmentStatusColor(context.Item.EquipmentStatus)" Size="Size.Small">
                        @context.Item.EquipmentStatusName
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.ComponentName" Title="部件名称" />
            <PropertyColumn Property="x => x.ComponentCode" Title="部件编码" />
            <PropertyColumn Property="x => x.ComponentCategoryName" Title="部件分类" />
            <PropertyColumn Property="x => x.ComponentModel" Title="部件型号" />
            <PropertyColumn Property="x => x.ComponentSpecifications" Title="规格" />
            <PropertyColumn Property="x => x.StandardQuantity" Title="标准数量" />
            <PropertyColumn Property="x => x.ComponentUnit" Title="单位" />
            <TemplateColumn Title="部件类型" Sortable="false">
                <CellTemplate>
                    <MudChip Color="@(context.Item.IsRequired ? Color.Error : Color.Info)" Size="Size.Small">
                        @context.Item.ComponentTypeName
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.ReplacementCycleDescription" Title="更换周期" />
            <TemplateColumn Title="库存状态" Sortable="false">
                <CellTemplate>
                    <MudChip Color="@GetStockStatusColor(context.Item.ComponentStockStatusColor)" Size="Size.Small">
                        @context.Item.ComponentStockStatus
                    </MudChip>
                </CellTemplate>
            </TemplateColumn>
            <PropertyColumn Property="x => x.ComponentStockQuantity" Title="当前库存" />
            <PropertyColumn Property="x => x.ComponentUnitPrice" Title="单价" Format="C2" />
            <PropertyColumn Property="x => x.TotalValue" Title="总价值" Format="C2" />
            <PropertyColumn Property="x => x.ComponentSupplier" Title="供应商" />
            <PropertyColumn Property="x => x.ComponentBrand" Title="品牌" />
            <PropertyColumn Property="x => x.MaintenanceNotes" Title="维护说明" />
            <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                <CellTemplate>
                    @if (context.Item.EquipmentComponentId.HasValue)
                    {
                        <MudTooltip Text="删除此部件关联">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                         Color="Color.Error"
                                         Size="Size.Small"
                                         OnClick="@(() => DeleteEquipmentComponent(context.Item))" />
                        </MudTooltip>
                    }
                </CellTemplate>
            </TemplateColumn>
        </Columns>
    </MudDataGrid>
</MudContainer>

@code {
    private List<EquipmentComponentDetailDto> details = new();
    private List<EquipmentComponentDetailDto> filteredDetails = new();
    private List<Equipment> equipment = new();
    private List<EquipmentModel> equipmentModels = new();
    private List<Department> departments = new();
    private List<ComponentCategory> componentCategories = new();
    private EquipmentComponentDetailStatisticsDto? statistics;
    private HashSet<EquipmentComponentDetailDto> selectedItems = new();

    private string searchKeyword = string.Empty;
    private int? selectedEquipmentId;
    private int? selectedModelId;
    private int? selectedDepartmentId;
    private int? selectedComponentCategoryId;
    private bool loading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
        await LoadFilterOptions();
        await LoadStatistics();
    }

    private async Task LoadData()
    {
        try
        {
            loading = true;
            StateHasChanged();

            details = await EquipmentComponentDetailService.GetAllEquipmentComponentDetailsAsync();
            FilterData();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备部件明细失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task LoadFilterOptions()
    {
        try
        {
            equipment = await EquipmentService.GetAllEquipmentAsync();
            equipmentModels = await EquipmentModelService.GetAllEquipmentModelsAsync();
            departments = await DepartmentService.GetAllDepartmentsAsync();
            componentCategories = await ComponentCategoryService.GetEnabledCategoriesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载过滤选项失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadStatistics()
    {
        try
        {
            statistics = await EquipmentComponentDetailService.GetEquipmentComponentDetailStatisticsAsync();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载统计信息失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterData()
    {
        var filteredQuery = details.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(searchKeyword))
        {
            filteredQuery = filteredQuery.Where(d =>
                d.EquipmentName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                d.EquipmentCode.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                d.ComponentName.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                d.ComponentCode.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
                (d.ComponentModel?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (d.ComponentBrand?.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        if (selectedEquipmentId.HasValue)
        {
            filteredQuery = filteredQuery.Where(d => d.EquipmentId == selectedEquipmentId.Value);
        }

        if (selectedModelId.HasValue)
        {
            filteredQuery = filteredQuery.Where(d => d.EquipmentModelId == selectedModelId.Value);
        }

        if (selectedDepartmentId.HasValue)
        {
            filteredQuery = filteredQuery.Where(d => d.DepartmentId == selectedDepartmentId.Value);
        }

        if (selectedComponentCategoryId.HasValue)
        {
            filteredQuery = filteredQuery.Where(d => d.ComponentCategoryId == selectedComponentCategoryId.Value);
        }

        filteredDetails = filteredQuery.ToList();
        StateHasChanged();
    }

    private async Task OnSearchKeyUp(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            FilterData();
        }
    }

    private async Task ExportData()
    {
        try
        {
            var exportData = await EquipmentComponentDetailService.ExportEquipmentComponentDetailsAsync(
                selectedEquipmentId, selectedModelId, selectedDepartmentId);

            Snackbar.Add($"导出 {exportData.Count} 条设备部件明细数据", Severity.Success);
            // TODO: 实现实际的导出功能
        }
        catch (Exception ex)
        {
            Snackbar.Add($"导出数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ShowLowStockComponents()
    {
        try
        {
            var lowStockComponents = await EquipmentComponentDetailService.GetLowStockEquipmentComponentDetailsAsync();
            if (lowStockComponents.Any())
            {
                Snackbar.Add($"发现 {lowStockComponents.Count} 个库存不足的设备部件", Severity.Warning);
                // 可以在这里打开一个对话框显示详细信息
            }
            else
            {
                Snackbar.Add("当前没有库存不足的设备部件", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取库存预警失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ShowMaintenanceDueComponents()
    {
        try
        {
            var maintenanceDueComponents = await EquipmentComponentDetailService.GetMaintenanceDueEquipmentComponentDetailsAsync();
            if (maintenanceDueComponents.Any())
            {
                Snackbar.Add($"发现 {maintenanceDueComponents.Count} 个需要维护的设备部件", Severity.Info);
                // 可以在这里打开一个对话框显示详细信息
            }
            else
            {
                Snackbar.Add("当前没有需要维护的设备部件", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取维护提醒失败: {ex.Message}", Severity.Error);
        }
    }

    private Color GetEquipmentStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Success,  // 正常
            2 => Color.Warning,  // 维修中
            3 => Color.Default,  // 停用
            4 => Color.Error,    // 报废
            _ => Color.Default
        };
    }

    private Color GetStockStatusColor(string colorCode)
    {
        return colorCode switch
        {
            "success" => Color.Success,
            "warning" => Color.Warning,
            "error" => Color.Error,
            _ => Color.Default
        };
    }

    private async Task DeleteEquipmentComponent(EquipmentComponentDetailDto detail)
    {
        if (!detail.EquipmentComponentId.HasValue)
        {
            Snackbar.Add("无法删除：缺少设备部件关联ID", Severity.Error);
            return;
        }

        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除设备 '{detail.EquipmentName}' 的部件 '{detail.ComponentName}' 吗？\n\n此操作不可撤销！",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await EquipmentComponentDetailService.DeleteEquipmentComponentAsync(detail.EquipmentComponentId.Value);

                if (result.IsSuccess)
                {
                    Snackbar.Add("删除成功", Severity.Success);
                    await LoadData(); // 重新加载数据
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private void OnSelectedItemsChanged(HashSet<EquipmentComponentDetailDto> items)
    {
        selectedItems = items;
        StateHasChanged();
    }

    private async Task BatchDeleteSelected()
    {
        var itemsToDelete = selectedItems.Where(item => item.EquipmentComponentId.HasValue).ToList();

        if (!itemsToDelete.Any())
        {
            Snackbar.Add("没有可删除的项目", Severity.Warning);
            return;
        }

        var confirmed = await DialogService.ShowMessageBox(
            "确认批量删除",
            $"确定要删除选中的 {itemsToDelete.Count} 个设备部件关联吗？\n\n此操作不可撤销！",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var ids = itemsToDelete.Select(item => item.EquipmentComponentId!.Value).ToList();
                var result = await EquipmentComponentDetailService.BatchDeleteEquipmentComponentsAsync(ids);

                if (result.IsSuccess)
                {
                    Snackbar.Add($"批量删除成功: {result.ErrorMessage}", Severity.Success);
                    selectedItems.Clear();
                    await LoadData(); // 重新加载数据
                }
                else
                {
                    Snackbar.Add($"批量删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"批量删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
