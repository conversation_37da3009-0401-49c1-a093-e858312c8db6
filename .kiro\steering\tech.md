---
inclusion: always
---

# 技术栈和开发规范

## 核心技术栈
- **.NET 8.0** - 目标框架，使用最新C#特性
- **Blazor Server** - Web UI框架，服务器端渲染
- **.NET MAUI** - 跨平台移动/桌面应用框架
- **MudBlazor 7.8.0** - 主要UI组件库
- **SQL Server** - 数据库（开发环境支持LocalDB）
- **SqlSugar 5.1.4.196** - ORM框架

## 架构模式
- **共享库架构** - CoreHub.Shared包含共享业务逻辑
- **依赖注入** - 所有服务通过DI容器管理
- **仓储模式** - 数据访问层抽象
- **服务层模式** - 业务逻辑封装在服务类中
- **基于角色的权限控制** - 自定义权限系统

## 代码规范
- **可空引用类型** - 所有项目启用，严格空值检查
- **隐式using** - 减少样板代码
- **异步优先** - 所有I/O操作使用async/await
- **中文注释** - XML文档和行内注释使用中文
- **英文命名** - 类、方法、变量使用英文，遵循.NET命名约定

## 服务注册模式
```csharp
// 在Program.cs (Web) 或 MauiProgram.cs (MAUI)中注册服务
builder.Services.AddScoped<IDeviceService, DeviceService>();
```

## 数据访问模式
- 使用SqlSugar进行数据库操作
- 实体模型位于CoreHub.Shared/Models
- 数据库上下文通过DatabaseContext管理
- 所有数据库操作使用异步方法

## 组件开发规范
- 共享组件放在CoreHub.Shared/Components
- 使用MudBlazor组件作为基础
- 组件参数使用[Parameter]特性
- 事件回调使用EventCallback<T>

## 错误处理
- 使用Serilog进行结构化日志记录
- 日志文件存储在logs/目录
- 异常信息使用中文描述
- 关键操作添加try-catch块

## 安全实践
- 使用ASP.NET Core Identity进行身份验证
- BCrypt.Net进行密码哈希
- JWT令牌用于API身份验证
- 基于角色的细粒度权限控制

## 平台特定开发
- MAUI平台特定代码放在Platforms/文件夹
- 使用条件编译指令处理平台差异
- 通知服务使用平台原生API
- 二维码扫描使用ZXing.Net.Maui

## 构建和部署
```bash
# 构建解决方案
dotnet build

# 运行Web应用
dotnet run --project CoreHub.Web

# MAUI应用构建（指定平台）
dotnet build CoreHub.Maui -f net8.0-android
```

## 配置管理
- Web应用使用appsettings.json层次化配置
- MAUI应用通过DatabaseConfig.GetConfigurationData()获取配置
- 敏感信息使用环境变量或用户机密
- SSL证书管理通过专用服务处理