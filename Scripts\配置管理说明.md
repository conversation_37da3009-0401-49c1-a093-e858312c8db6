# 🔧 CoreHub 配置管理说明

## 📋 配置值修改指南

现在所有脚本都使用变量来存储配置值，修改配置时只需要更改脚本顶部的变量定义即可。

### 🎯 开发环境配置 (`设置开发环境.bat`)

```batch
set "ENV_NAME=Development"
set "API_URL=http://************:8080"
set "DB_CONN_STR=Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
set "USE_HTTPS=false"
set "VERBOSE_LOG=true"
```

### 🎯 测试环境配置 (`设置测试环境.bat`)

```batch
set "ENV_NAME=Staging"
set "API_URL=https://api-staging.saintyeartex.com:8081"
set "DB_CONN_STR=Server=***********;Database=CoreHub_Staging;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
set "USE_HTTPS=true"
set "VERBOSE_LOG=false"
```

### 🎯 生产环境配置 (`设置生产环境.bat`)

```batch
set "ENV_NAME=Production"
set "API_URL=https://api.saintyeartex.com:8081"
set "DB_CONN_STR=Server=*************;Database=CoreHub;User Id=sa;Password=*********;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
set "USE_HTTPS=true"
set "VERBOSE_LOG=false"
```

## 🔄 修改配置的步骤

### 1. 修改数据库连接
如果需要更改数据库服务器或连接信息：

1. 打开对应环境的脚本文件
2. 找到 `set "DB_CONN_STR=..."` 这一行
3. 修改连接字符串
4. 保存文件

**示例**：
```batch
# 原来的配置
set "DB_CONN_STR=Server=***********;Database=CoreHub;User Id=sa;Password=****;..."

# 修改后的配置
set "DB_CONN_STR=Server=新服务器地址;Database=CoreHub;User Id=sa;Password=*********;..."
```

### 2. 修改API地址
如果需要更改API服务器地址：

1. 打开对应环境的脚本文件
2. 找到 `set "API_URL=..."` 这一行
3. 修改URL地址
4. 保存文件

**示例**：
```batch
# 原来的配置
set "API_URL=https://api.saintyeartex.com:8081"

# 修改后的配置
set "API_URL=https://新域名.com:8081"
```

### 3. 修改其他配置
- `ENV_NAME`: 环境名称 (Development/Staging/Production)
- `USE_HTTPS`: HTTPS重定向 (true/false)
- `VERBOSE_LOG`: 详细日志 (true/false)

## ✅ 优势

### 1. **动态显示**
- echo 显示的值与实际设置的值完全一致
- 修改配置值后，显示内容自动更新
- 避免了硬编码导致的显示不一致问题

### 2. **易于维护**
- 所有配置值集中在脚本顶部
- 修改配置只需要改一个地方
- 减少了出错的可能性

### 3. **一致性保证**
- setx 命令和 echo 显示使用相同的变量
- 确保设置的值和显示的值完全匹配

## 🔍 验证配置

修改配置后，可以通过以下方式验证：

### 1. 运行脚本查看输出
```cmd
Scripts\设置生产环境.bat
```

输出示例：
```
[OK] COREHUB_ENVIRONMENT = Production
[OK] COREHUB_API_BASE_URL = https://api.saintyeartex.com:8081
[OK] COREHUB_DB_CONNECTION_STRING = Server=*************;Database=CoreHub;User Id=sa;Password=*********;...
[OK] COREHUB_USE_HTTPS_REDIRECTION = true
[OK] COREHUB_ENABLE_VERBOSE_LOGGING = false
```

### 2. 查看当前环境
```cmd
Scripts\查看当前环境.bat
```

### 3. 检查环境变量
```cmd
set | findstr COREHUB
```

## 📝 配置模板

如果需要添加新的环境或配置项，可以参考以下模板：

```batch
@echo off
echo ========================================
echo    CoreHub - Setting [环境名称] Environment
echo    (Project-Specific Environment Variables)
echo ========================================
echo.

echo WARNING: You are setting CoreHub [环境名称] environment!
echo This will configure CoreHub for [环境描述].
echo Other projects will NOT be affected.
echo.
set /p confirm=Are you sure? (y/N): 
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Setting CoreHub-specific environment variables...

:: 配置变量定义
set "ENV_NAME=[环境名称]"
set "API_URL=[API地址]"
set "DB_CONN_STR=[数据库连接字符串]"
set "USE_HTTPS=[true/false]"
set "VERBOSE_LOG=[true/false]"

:: 设置环境变量
setx COREHUB_ENVIRONMENT "%ENV_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENVIRONMENT = %ENV_NAME%
) else (
    echo [ERROR] Failed to set COREHUB_ENVIRONMENT
)

setx COREHUB_API_BASE_URL "%API_URL%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_API_BASE_URL = %API_URL%
) else (
    echo [ERROR] Failed to set COREHUB_API_BASE_URL
)

setx COREHUB_DB_CONNECTION_STRING "%DB_CONN_STR%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_DB_CONNECTION_STRING = %DB_CONN_STR%
) else (
    echo [ERROR] Failed to set COREHUB_DB_CONNECTION_STRING
)

setx COREHUB_USE_HTTPS_REDIRECTION "%USE_HTTPS%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_USE_HTTPS_REDIRECTION = %USE_HTTPS%
) else (
    echo [ERROR] Failed to set COREHUB_USE_HTTPS_REDIRECTION
)

setx COREHUB_ENABLE_VERBOSE_LOGGING "%VERBOSE_LOG%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENABLE_VERBOSE_LOGGING = %VERBOSE_LOG%
) else (
    echo [ERROR] Failed to set COREHUB_ENABLE_VERBOSE_LOGGING
)

echo.
echo ========================================
echo CoreHub [环境名称] environment configured!
echo ========================================
echo.
echo Configuration Summary:
echo - Environment: %ENV_NAME% (CoreHub-specific)
echo - API URL: %API_URL%
echo - HTTPS Redirect: %USE_HTTPS%
echo - Verbose Logging: %VERBOSE_LOG%
echo - Database: [数据库描述]
echo.
echo ADVANTAGE: These settings only affect CoreHub project!
echo Other projects using different environment variables will not be affected.
echo.
echo IMPORTANT: Please restart your application
echo for the changes to take effect:
echo - Visual Studio: Restart Visual Studio
echo - Command Line: Open new command window
echo - IIS: Restart application pool
echo.
pause
```

## 💡 最佳实践

1. **修改前备份**：修改配置前先备份原始脚本
2. **测试验证**：修改后运行脚本验证配置是否正确
3. **文档更新**：如果有重大配置变更，更新相关文档
4. **团队同步**：配置变更后及时通知团队成员

现在您可以轻松地修改任何环境的配置，而且显示的值会自动与实际设置的值保持一致！
