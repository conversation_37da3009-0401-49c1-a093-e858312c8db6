using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备部位实体
    /// </summary>
    [SugarTable("EquipmentParts")]
    public class EquipmentPart
    {
        /// <summary>
        /// 设备部位ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 部位编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "部位编码不能为空")]
        [StringLength(50, ErrorMessage = "部位编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部位名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "部位名称不能为空")]
        [StringLength(100, ErrorMessage = "部位名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 所属设备ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "所属设备不能为空")]
        public int EquipmentId { get; set; }

        /// <summary>
        /// 父级部位ID（支持层级结构）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 层级级别（1=一级部位，2=二级部位，以此类推）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 部位描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "部位描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 所属设备
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Equipment? Equipment { get; set; }

        /// <summary>
        /// 父级部位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentPart? Parent { get; set; }

        /// <summary>
        /// 子级部位列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<EquipmentPart> Children { get; set; } = new List<EquipmentPart>();

        /// <summary>
        /// 使用此部位的报修单列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<RepairOrder> RepairOrders { get; set; } = new List<RepairOrder>();

        /// <summary>
        /// 完整路径名称（包含父级部位）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FullName
        {
            get
            {
                if (Parent != null)
                {
                    return $"{Parent.Name} - {Name}";
                }
                return Name;
            }
        }

        /// <summary>
        /// 层级缩进显示名称（用于树形显示）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string IndentedName
        {
            get
            {
                var indent = new string(' ', (Level - 1) * 4);
                return $"{indent}{Name}";
            }
        }

        /// <summary>
        /// 是否为根节点（一级部位）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsRoot => ParentId == null;

        /// <summary>
        /// 是否为叶子节点（没有子部位）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool IsLeaf => Children == null || Children.Count == 0;

        /// <summary>
        /// 获取所有祖先部位的ID列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<int> AncestorIds
        {
            get
            {
                var ancestors = new List<int>();
                var current = Parent;
                while (current != null)
                {
                    ancestors.Insert(0, current.Id);
                    current = current.Parent;
                }
                return ancestors;
            }
        }

        /// <summary>
        /// 获取所有后代部位的ID列表（递归）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<int> DescendantIds
        {
            get
            {
                var descendants = new List<int>();
                foreach (var child in Children)
                {
                    descendants.Add(child.Id);
                    descendants.AddRange(child.DescendantIds);
                }
                return descendants;
            }
        }

        /// <summary>
        /// 部位路径（从根到当前部位的完整路径）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string Path
        {
            get
            {
                var pathParts = new List<string>();
                var current = this;
                while (current != null)
                {
                    pathParts.Insert(0, current.Name);
                    current = current.Parent;
                }
                return string.Join(" > ", pathParts);
            }
        }
    }
}
