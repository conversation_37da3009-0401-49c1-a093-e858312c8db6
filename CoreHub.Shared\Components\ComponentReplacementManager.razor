@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IComponentReplacementRecordService ComponentReplacementRecordService
@inject IComponentService ComponentService
@inject IEquipmentModelComponentTemplateService TemplateService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<MudCard>
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                部件更换记录
            </MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudButton Variant="Variant.Filled" 
                     Color="Color.Primary" 
                     StartIcon="@Icons.Material.Filled.Add"
                     OnClick="OpenAddDialog"
                     Disabled="@(!CanEdit)">
                添加更换记录
            </MudButton>
            <MudButton Variant="Variant.Outlined" 
                     Color="Color.Info" 
                     StartIcon="@Icons.Material.Filled.List"
                     OnClick="ShowStandardComponents"
                     Disabled="@(!CanEdit)">
                标准部件清单
            </MudButton>
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        @if (loading)
        {
            <MudProgressLinear Indeterminate="true" />
            <MudText Class="mt-2">加载中...</MudText>
        }
        else if (replacementRecords.Any())
        {
            <MudDataGrid T="ComponentReplacementRecord" 
                       Items="@replacementRecords" 
                       Dense="true"
                       Hover="true"
                       Striped="true">
                <Columns>
                    <PropertyColumn Property="x => x.Component!.Name" Title="部件名称" />
                    <PropertyColumn Property="x => x.Component!.Code" Title="部件编码" />
                    <PropertyColumn Property="x => x.Quantity" Title="数量" />
                    <PropertyColumn Property="x => x.Component!.Unit" Title="单位" />
                    <TemplateColumn Title="单价" Sortable="false">
                        <CellTemplate>
                            @if (context.Item.UnitPrice.HasValue)
                            {
                                <MudText>¥@context.Item.UnitPrice.Value.ToString("F2")</MudText>
                            }
                            else
                            {
                                <MudText Color="Color.Secondary">-</MudText>
                            }
                        </CellTemplate>
                    </TemplateColumn>
                    <TemplateColumn Title="总价" Sortable="false">
                        <CellTemplate>
                            @if (context.Item.TotalPrice.HasValue)
                            {
                                <MudText>¥@context.Item.TotalPrice.Value.ToString("F2")</MudText>
                            }
                            else
                            {
                                <MudText Color="Color.Secondary">-</MudText>
                            }
                        </CellTemplate>
                    </TemplateColumn>
                    <PropertyColumn Property="x => x.Reason" Title="更换原因" />
                    <PropertyColumn Property="x => x.ReplacedByUser!.DisplayName" Title="更换人员" />
                    <TemplateColumn Title="更换时间" Sortable="true" SortBy="@(x => x.ReplacedAt)">
                        <CellTemplate>
                            <MudText>@context.Item.ReplacedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
                        </CellTemplate>
                    </TemplateColumn>
                    <TemplateColumn Title="操作" Sortable="false">
                        <CellTemplate>
                            <MudStack Row Spacing="1">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             OnClick="() => OpenEditDialog(context.Item)"
                                             Disabled="@(!CanEdit)" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                             Color="Color.Error" 
                                             Size="Size.Small"
                                             OnClick="() => DeleteRecord(context.Item)"
                                             Disabled="@(!CanEdit)" />
                            </MudStack>
                        </CellTemplate>
                    </TemplateColumn>
                </Columns>
            </MudDataGrid>

            @if (replacementRecords.Any(r => r.TotalPrice.HasValue))
            {
                <MudDivider Class="my-4" />
                <MudStack Row Justify="Justify.FlexEnd">
                    <MudText Typo="Typo.h6" Color="Color.Primary">
                        总成本：¥@replacementRecords.Where(r => r.TotalPrice.HasValue).Sum(r => r.TotalPrice!.Value).ToString("F2")
                    </MudText>
                </MudStack>
            }
        }
        else
        {
            <MudAlert Severity="Severity.Info">
                暂无部件更换记录
            </MudAlert>
        }
    </MudCardContent>
</MudCard>

@code {
    [Parameter] public int RepairOrderId { get; set; }
    [Parameter] public int EquipmentId { get; set; }
    [Parameter] public bool CanEdit { get; set; } = true;
    [Parameter] public EventCallback OnRecordsChanged { get; set; }

    private List<ComponentReplacementRecord> replacementRecords = new();
    private bool loading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadReplacementRecords();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (RepairOrderId > 0)
        {
            await LoadReplacementRecords();
        }
    }

    private async Task LoadReplacementRecords()
    {
        try
        {
            loading = true;
            StateHasChanged();

            replacementRecords = await ComponentReplacementRecordService.GetRecordsByRepairOrderAsync(RepairOrderId);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部件更换记录失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task OpenAddDialog()
    {
        var parameters = new DialogParameters();
        parameters.Add("RepairOrderId", RepairOrderId);
        parameters.Add("EquipmentId", EquipmentId);
        parameters.Add("IsEdit", false);

        var dialog = await DialogService.ShowAsync<ComponentReplacementDialog>("添加部件更换记录", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadReplacementRecords();
            await OnRecordsChanged.InvokeAsync();
        }
    }

    private async Task OpenEditDialog(ComponentReplacementRecord record)
    {
        var parameters = new DialogParameters();
        parameters.Add("Record", record);
        parameters.Add("RepairOrderId", RepairOrderId);
        parameters.Add("EquipmentId", EquipmentId);
        parameters.Add("IsEdit", true);

        var dialog = await DialogService.ShowAsync<ComponentReplacementDialog>("编辑部件更换记录", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadReplacementRecords();
            await OnRecordsChanged.InvokeAsync();
        }
    }

    private async Task ShowStandardComponents()
    {
        try
        {
            var standardComponents = await ComponentReplacementRecordService.GetStandardComponentsByEquipmentAsync(EquipmentId);
            
            var parameters = new DialogParameters();
            parameters.Add("StandardComponents", standardComponents);
            parameters.Add("RepairOrderId", RepairOrderId);
            parameters.Add("EquipmentId", EquipmentId);

            var dialog = await DialogService.ShowAsync<StandardComponentsDialog>("标准部件清单", parameters);
            var result = await dialog.Result;

            if (!result.Canceled)
            {
                await LoadReplacementRecords();
                await OnRecordsChanged.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取标准部件清单失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteRecord(ComponentReplacementRecord record)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除部件 '{record.Component?.Name}' 的更换记录吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await ComponentReplacementRecordService.DeleteRecordAsync(record.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("部件更换记录删除成功", Severity.Success);
                    await LoadReplacementRecords();
                    await OnRecordsChanged.InvokeAsync();
                }
                else
                {
                    Snackbar.Add(result.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    public async Task RefreshAsync()
    {
        await LoadReplacementRecords();
    }
}
