# 🛠️ CoreHub 环境管理脚本

这个文件夹包含了 CoreHub 项目的环境管理脚本，用于在不同环境间快速切换。

## ⚡ 快速开始

**最简单的使用方法：**
1. 双击 `设置开发环境.bat` - 设置CoreHub开发环境
2. 双击 `设置生产环境.bat` - 设置CoreHub生产环境
3. 双击 `查看当前环境.bat` - 查看当前配置

**特点：使用 COREHUB_* 环境变量，不会与其他项目冲突！**
**需要重启应用程序才能生效！**

## 📁 文件说明

### 🎯 一键设置脚本（推荐使用）
- `设置开发环境.bat` - 一键设置CoreHub开发环境
- `设置测试环境.bat` - 一键设置CoreHub测试环境
- `设置生产环境.bat` - 一键设置CoreHub生产环境
- `查看当前环境.bat` - 查看当前CoreHub环境配置
- `清除环境变量.bat` - 清除所有CoreHub环境变量

### 🔧 交互式脚本
- `环境切换工具.bat` - 交互式菜单，支持多种操作

### 📚 文档和指南
- `故障排除指南.md` - 详细的问题解决方案和故障排除指南

## 🚀 使用方法

### 方法一：一键设置（最简单，推荐）
直接双击对应的脚本文件：
- 开发环境：双击 `设置开发环境.bat`
- 测试环境：双击 `设置测试环境.bat`
- 生产环境：双击 `设置生产环境.bat`
- 查看配置：双击 `查看当前环境.bat`
- 清除变量：双击 `清除环境变量.bat`

### 方法二：交互式菜单
```cmd
cd Scripts
.\环境切换工具.bat
```
然后根据菜单提示选择相应的操作。

### 方法三：从主入口访问
```cmd
# 在项目根目录执行
.\环境管理.bat
```
通过主入口脚本可以方便地访问所有环境管理功能。

## 🎯 环境配置说明

### 🔧 CoreHub环境变量
所有脚本使用 `COREHUB_*` 前缀的环境变量：
- `COREHUB_ENVIRONMENT` - 环境名称
- `COREHUB_API_BASE_URL` - API基础地址
- `COREHUB_DB_CONNECTION_STRING` - 数据库连接字符串
- `COREHUB_USE_HTTPS_REDIRECTION` - HTTPS重定向开关
- `COREHUB_ENABLE_VERBOSE_LOGGING` - 详细日志开关

### 开发环境 (Development)
- API地址：http://************:8080
- HTTPS重定向：关闭
- 详细日志：开启
- 数据库：CoreHub (开发库)

### 测试环境 (Staging)
- API地址：https://api-staging.saintyeartex.com:8081
- HTTPS重定向：开启
- 详细日志：关闭
- 数据库：CoreHub_Staging (测试库)

### 生产环境 (Production)
- API地址：https://api.saintyeartex.com:8081
- HTTPS重定向：开启
- 详细日志：关闭
- 数据库：CoreHub (生产库)

## 🚀 项目隔离优势

✅ **完全隔离** - 使用 `COREHUB_*` 环境变量，不会与其他项目冲突
✅ **多项目支持** - 可以在同一台机器上运行多个项目
✅ **安全可靠** - 每个项目使用独立的配置

## ⚠️ 重要提示

1. **管理员权限**：某些系统可能需要管理员权限来设置环境变量
2. **重启应用**：设置环境变量后需要重启应用程序才能生效
3. **生产环境确认**：设置生产环境时会要求确认，请谨慎操作
4. **配置验证**：设置完成后建议查看当前环境确认配置正确
5. **项目隔离**：CoreHub使用COREHUB_*环境变量，不会影响其他项目

## 🔍 故障排除

如果遇到问题，请参考：
- `故障排除指南.md` - 详细的问题解决方案和常见问题处理

## 📞 获取帮助

如果脚本无法正常工作，请：
1. 以管理员身份运行命令提示符
2. 检查系统是否支持 `setx` 命令
3. 参考故障排除指南中的解决方案
4. 使用 `查看当前环境.bat` 检查当前配置状态
