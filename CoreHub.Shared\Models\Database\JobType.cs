using SqlSugar;
using System.Text.Json.Serialization;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 工种类型
    /// </summary>
    [SugarTable("JobTypes")]
    public class JobType
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 工种编码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 工种名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 工种分类（生产、维修、管理、支持）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 导航属性：该工种的用户（保留用于向后兼容）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<User> Users { get; set; } = new();

        /// <summary>
        /// 用户工种关联（多对多关系）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public List<UserJobType> UserJobTypes { get; set; } = new();
    }

    /// <summary>
    /// 工种分类枚举
    /// </summary>
    public enum JobCategoryEnum
    {
        /// <summary>
        /// 生产
        /// </summary>
        Production,

        /// <summary>
        /// 维修
        /// </summary>
        Maintenance,

        /// <summary>
        /// 管理
        /// </summary>
        Management,

        /// <summary>
        /// 支持
        /// </summary>
        Support
    }

    /// <summary>
    /// 工种分类常量
    /// </summary>
    public static class JobCategories
    {
        public const string Production = "生产";
        public const string Maintenance = "维修";
        public const string Management = "管理";
        public const string Support = "支持";
    }

    /// <summary>
    /// 维修工种代码常量
    /// </summary>
    public static class MaintenanceJobCodes
    {
        public const string ElectricalTechnician = "ElectricalTechnician";
        public const string MechanicalTechnician = "MechanicalTechnician";
        public const string HydraulicTechnician = "HydraulicTechnician";
        public const string ControlSystemEngineer = "ControlSystemEngineer";
        public const string MaintenanceSupervisor = "MaintenanceSupervisor";
    }

    /// <summary>
    /// 生产工种代码常量
    /// </summary>
    public static class ProductionJobCodes
    {
        public const string Operator = RoleCodes.Operator;
        public const string QualityInspector = "QualityInspector";
        public const string ProductionSupervisor = "ProductionSupervisor";
    }
}
