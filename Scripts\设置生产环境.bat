@echo off
echo ========================================
echo    CoreHub - Setting Production Environment
echo    (Project-Specific Environment Variables)
echo ========================================
echo.

echo WARNING: You are setting CoreHub PRODUCTION environment!
echo This will configure CoreHub for live production use.
echo Other projects will NOT be affected.
echo.
set /p confirm=Are you sure? (y/N):
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Setting CoreHub-specific environment variables...
set "ENV_NAME=Production"
setx COREHUB_ENVIRONMENT "%ENV_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENVIRONMENT = %ENV_NAME%
) else (
    echo [ERROR] Failed to set COREHUB_ENVIRONMENT
)

set "API_URL=https://api.saintyeartex.com:8081"
setx COREHUB_API_BASE_URL "%API_URL%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_API_BASE_URL = %API_URL%
) else (
    echo [ERROR] Failed to set COREHUB_API_BASE_URL
)

set "DB_CONN_STR=Server=*************;Database=CoreHub;User Id=sa;Password=*********;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
setx COREHUB_DB_CONNECTION_STRING "%DB_CONN_STR%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_DB_CONNECTION_STRING = %DB_CONN_STR%
) else (
    echo [ERROR] Failed to set COREHUB_DB_CONNECTION_STRING
)

set "USE_HTTPS=true"
setx COREHUB_USE_HTTPS_REDIRECTION "%USE_HTTPS%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_USE_HTTPS_REDIRECTION = %USE_HTTPS%
) else (
    echo [ERROR] Failed to set COREHUB_USE_HTTPS_REDIRECTION
)

set "VERBOSE_LOG=false"
setx COREHUB_ENABLE_VERBOSE_LOGGING "%VERBOSE_LOG%" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENABLE_VERBOSE_LOGGING = %VERBOSE_LOG%
) else (
    echo [ERROR] Failed to set COREHUB_ENABLE_VERBOSE_LOGGING
)

echo.
echo ========================================
echo CoreHub Production environment configured!
echo ========================================
echo.
echo Configuration Summary:
echo - Environment: Production (CoreHub-specific)
echo - API URL: https://api.saintyeartex.com:8081
echo - HTTPS Redirect: Enabled
echo - Verbose Logging: Disabled
echo - Database: CoreHub (Production)
echo.
echo ADVANTAGE: These settings only affect CoreHub project!
echo Other projects using different environment variables will not be affected.
echo.
echo SECURITY REMINDER:
echo - Ensure SSL certificates are valid
echo - Verify database backups are working
echo - Monitor application logs
echo.
echo IMPORTANT: Please restart your application
echo for the changes to take effect:
echo - Visual Studio: Restart Visual Studio
echo - Command Line: Open new command window
echo - IIS: Restart application pool
echo.
pause
