using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 部件更换记录实体
    /// </summary>
    [SugarTable("ComponentReplacementRecords")]
    public class ComponentReplacementRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 维修工单ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "维修工单不能为空")]
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备不能为空")]
        public int EquipmentId { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "部件不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 更换数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "更换数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "更换数量必须大于0")]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 更换原因
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "更换原因长度不能超过500个字符")]
        public string? Reason { get; set; }

        /// <summary>
        /// 旧部件状态描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "旧部件状态描述长度不能超过500个字符")]
        public string? OldComponentCondition { get; set; }

        /// <summary>
        /// 更换人员ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "更换人员不能为空")]
        public int ReplacedBy { get; set; }

        /// <summary>
        /// 更换时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime ReplacedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 单价（记录更换时的价格）
        /// </summary>
        [SugarColumn(ColumnDataType = "decimal(18,2)", IsNullable = true)]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        [SugarColumn(ColumnDataType = "decimal(18,2)", IsNullable = true)]
        public decimal? TotalPrice { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 维修工单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public RepairOrder? RepairOrder { get; set; }

        /// <summary>
        /// 设备信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Equipment? Equipment { get; set; }

        /// <summary>
        /// 部件信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Component? Component { get; set; }

        /// <summary>
        /// 更换人员
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? ReplacedByUser { get; set; }
    }
}
