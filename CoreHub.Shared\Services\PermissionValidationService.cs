using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 权限验证服务实现
    /// </summary>
    public class PermissionValidationService : IPermissionValidationService
    {
        private readonly IRoleDepartmentPermissionService _roleDepartmentPermissionService;
        private readonly IJobTypeService _jobTypeService;
        private readonly IEquipmentService _equipmentService;
        private readonly IRepairOrderService _repairOrderService;
        private readonly IUserManagementService _userManagementService;
        private readonly ILogger<PermissionValidationService> _logger;

        public PermissionValidationService(
            IRoleDepartmentPermissionService roleDepartmentPermissionService,
            IJobTypeService jobTypeService,
            IEquipmentService equipmentService,
            IRepairOrderService repairOrderService,
            IUserManagementService userManagementService,
            ILogger<PermissionValidationService> logger)
        {
            _roleDepartmentPermissionService = roleDepartmentPermissionService;
            _jobTypeService = jobTypeService;
            _equipmentService = equipmentService;
            _repairOrderService = repairOrderService;
            _userManagementService = userManagementService;
            _logger = logger;
        }

        public async Task<PermissionValidationResult> ValidateEquipmentReportPermissionAsync(int userId, int equipmentId)
        {
            try
            {
                var equipment = await _equipmentService.GetEquipmentByIdAsync(equipmentId);
                if (equipment == null)
                {
                    return PermissionValidationResult.Failure("设备不存在");
                }

                var canReport = await _roleDepartmentPermissionService.CanUserReportEquipmentAsync(userId, equipment.DepartmentId);
                if (!canReport)
                {
                    return PermissionValidationResult.Failure("您没有权限报修该部门的设备");
                }

                return PermissionValidationResult.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证设备报修权限失败：{userId}, {equipmentId}", userId, equipmentId);
                return PermissionValidationResult.Failure("权限验证失败");
            }
        }

        public async Task<PermissionValidationResult> ValidateRepairReceivePermissionAsync(int userId, int departmentId)
        {
            try
            {
                var canReceive = await _roleDepartmentPermissionService.CanUserReceiveRepairAsync(userId, departmentId);
                if (!canReceive)
                {
                    return PermissionValidationResult.Failure("您没有权限接收该部门的报修");
                }

                return PermissionValidationResult.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证报修接收权限失败：{userId}, {departmentId}", userId, departmentId);
                return PermissionValidationResult.Failure("权限验证失败");
            }
        }

        public async Task<PermissionValidationResult> ValidateEquipmentMaintenancePermissionAsync(int userId, int equipmentId)
        {
            try
            {
                var equipment = await _equipmentService.GetEquipmentByIdAsync(equipmentId);
                if (equipment == null)
                {
                    return PermissionValidationResult.Failure("设备不存在");
                }

                var canMaintain = await _roleDepartmentPermissionService.CanUserMaintainEquipmentAsync(userId, equipment.DepartmentId);
                if (!canMaintain)
                {
                    return PermissionValidationResult.Failure("您没有权限维修该部门的设备");
                }

                return PermissionValidationResult.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证设备维修权限失败：{userId}, {equipmentId}", userId, equipmentId);
                return PermissionValidationResult.Failure("权限验证失败");
            }
        }

        public async Task<PermissionValidationResult> ValidateMaintenanceSkillsAsync(int personnelId, int equipmentModelId)
        {
            try
            {
                // 简化版本：不再进行技能验证，直接返回成功
                // 维修人员分配基于等级和工作负载，不再考虑技能匹配
                return PermissionValidationResult.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证维修技能失败：{personnelId}, {equipmentModelId}", personnelId, equipmentModelId);
                return PermissionValidationResult.Failure("技能验证失败");
            }
        }

        public async Task<PermissionValidationResult> ValidateRepairOrderViewPermissionAsync(int userId, int repairOrderId)
        {
            try
            {
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return PermissionValidationResult.Failure("报修单不存在");
                }

                // 报修人可以查看自己的报修单
                if (repairOrder.ReporterId == userId)
                {
                    return PermissionValidationResult.Success();
                }

                // 被分配的维修人员可以查看
                if (repairOrder.AssignedTo == userId)
                {
                    return PermissionValidationResult.Success();
                }

                // 有维修部门权限的用户可以查看
                var canReceive = await _roleDepartmentPermissionService.CanUserReceiveRepairAsync(userId, repairOrder.MaintenanceDepartmentId);
                if (canReceive)
                {
                    return PermissionValidationResult.Success();
                }

                return PermissionValidationResult.Failure("您没有权限查看此报修单");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证报修单查看权限失败：{userId}, {repairOrderId}", userId, repairOrderId);
                return PermissionValidationResult.Failure("权限验证失败");
            }
        }

        public async Task<PermissionValidationResult> ValidateRepairOrderOperationPermissionAsync(int userId, int repairOrderId, RepairOrderOperation operation)
        {
            try
            {
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return PermissionValidationResult.Failure("报修单不存在");
                }

                return operation switch
                {
                    RepairOrderOperation.View => await ValidateRepairOrderViewPermissionAsync(userId, repairOrderId),
                    RepairOrderOperation.Edit => await ValidateEditPermission(userId, repairOrder),
                    RepairOrderOperation.Assign => await ValidateAssignPermission(userId, repairOrder),
                    RepairOrderOperation.StartRepair => await ValidateStartRepairPermission(userId, repairOrder),
                    RepairOrderOperation.CompleteRepair => await ValidateCompleteRepairPermission(userId, repairOrder),
                    RepairOrderOperation.Cancel => await ValidateCancelPermission(userId, repairOrder),
                    RepairOrderOperation.Rate => await ValidateRatePermission(userId, repairOrder),
                    _ => PermissionValidationResult.Failure("未知操作类型")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证报修单操作权限失败：{userId}, {repairOrderId}, {operation}", userId, repairOrderId, operation);
                return PermissionValidationResult.Failure("权限验证失败");
            }
        }

        public async Task<UserPermissionSummary> GetUserPermissionSummaryAsync(int userId)
        {
            try
            {
                var user = await _userManagementService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return new UserPermissionSummary { UserId = userId };
                }

                var userRoles = await _userManagementService.GetUserRolesAsync(userId);
                var reportableDepartments = await _roleDepartmentPermissionService.GetUserReportableDepartmentsAsync(userId);
                var receivableDepartments = await _roleDepartmentPermissionService.GetUserReceivableDepartmentsAsync(userId);
                var maintainableDepartments = await _roleDepartmentPermissionService.GetUserMaintainableDepartmentsAsync(userId);

                // 检查用户是否具有维修工种
                var maintenanceJobTypes = await _jobTypeService.GetJobTypesByCategoryAsync(JobCategories.Maintenance);
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasMaintenanceJobType = userJobTypes.Any(ujt => maintenanceJobTypes.Any(mjt => mjt.Id == ujt.UserJobType.JobTypeId));

                return new UserPermissionSummary
                {
                    UserId = userId,
                    UserName = user.Username,
                    DisplayName = user.DisplayName ?? user.Username,
                    DepartmentId = user.DepartmentId,
                    DepartmentName = user.Department?.Name,
                    Roles = userRoles.Select(r => r.Name).ToList(),
                    ReportableDepartments = reportableDepartments,
                    ReceivableDepartments = receivableDepartments,
                    MaintainableDepartments = maintainableDepartments,
                    IsMaintenancePersonnel = hasMaintenanceJobType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户权限摘要失败：{userId}", userId);
                return new UserPermissionSummary { UserId = userId };
            }
        }

        private async Task<PermissionValidationResult> ValidateEditPermission(int userId, RepairOrder repairOrder)
        {
            // 只有报修人在报修单未被处理时可以编辑
            if (repairOrder.ReporterId == userId && repairOrder.Status == 1)
            {
                return PermissionValidationResult.Success();
            }
            return PermissionValidationResult.Failure("您没有权限编辑此报修单");
        }

        private async Task<PermissionValidationResult> ValidateAssignPermission(int userId, RepairOrder repairOrder)
        {
            // 有接收权限的用户可以分配
            var canReceive = await _roleDepartmentPermissionService.CanUserReceiveRepairAsync(userId, repairOrder.MaintenanceDepartmentId);
            if (canReceive)
            {
                return PermissionValidationResult.Success();
            }
            return PermissionValidationResult.Failure("您没有权限分配此报修单");
        }

        private async Task<PermissionValidationResult> ValidateStartRepairPermission(int userId, RepairOrder repairOrder)
        {
            // 被分配的维修人员可以开始维修
            if (repairOrder.AssignedTo == userId)
            {
                return PermissionValidationResult.Success();
            }
            return PermissionValidationResult.Failure("您没有权限开始维修此报修单");
        }

        private async Task<PermissionValidationResult> ValidateCompleteRepairPermission(int userId, RepairOrder repairOrder)
        {
            // 被分配的维修人员可以完成维修
            if (repairOrder.AssignedTo == userId)
            {
                return PermissionValidationResult.Success();
            }
            return PermissionValidationResult.Failure("您没有权限完成此报修单");
        }

        private async Task<PermissionValidationResult> ValidateCancelPermission(int userId, RepairOrder repairOrder)
        {
            // 报修人或有接收权限的用户可以取消
            if (repairOrder.ReporterId == userId)
            {
                return PermissionValidationResult.Success();
            }

            var canReceive = await _roleDepartmentPermissionService.CanUserReceiveRepairAsync(userId, repairOrder.MaintenanceDepartmentId);
            if (canReceive)
            {
                return PermissionValidationResult.Success();
            }

            return PermissionValidationResult.Failure("您没有权限取消此报修单");
        }

        private async Task<PermissionValidationResult> ValidateRatePermission(int userId, RepairOrder repairOrder)
        {
            // 只有报修人可以评价已完成的报修单
            if (repairOrder.ReporterId == userId && repairOrder.Status == 3)
            {
                return PermissionValidationResult.Success();
            }
            return PermissionValidationResult.Failure("您没有权限评价此报修单");
        }
    }
}
