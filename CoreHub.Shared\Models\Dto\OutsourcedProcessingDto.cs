using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 委外加工记录DTO
    /// </summary>
    public class OutsourcedProcessingDto
    {
        #region 基本信息

        /// <summary>
        /// 主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 零件申请ID
        /// </summary>
        public int PartRequestId { get; set; }

        /// <summary>
        /// 委外加工单号
        /// </summary>
        public string? ProcessingOrderNo { get; set; }

        #endregion

        #region 零件信息

        /// <summary>
        /// 零件名称
        /// </summary>
        public string PartName { get; set; } = string.Empty;

        /// <summary>
        /// 零件规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 申请数量
        /// </summary>
        public int RequestedQuantity { get; set; }

        /// <summary>
        /// 维修单号
        /// </summary>
        public string? RepairOrderNo { get; set; }

        #endregion

        #region 供应商信息

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Required(ErrorMessage = "供应商名称不能为空")]
        [StringLength(200, ErrorMessage = "供应商名称长度不能超过200个字符")]
        public string SupplierName { get; set; } = string.Empty;

        /// <summary>
        /// 供应商联系人
        /// </summary>
        [StringLength(100, ErrorMessage = "供应商联系人长度不能超过100个字符")]
        public string? SupplierContact { get; set; }

        /// <summary>
        /// 供应商联系电话
        /// </summary>
        [StringLength(50, ErrorMessage = "供应商联系电话长度不能超过50个字符")]
        public string? SupplierPhone { get; set; }

        #endregion

        #region 加工信息

        /// <summary>
        /// 加工要求
        /// </summary>
        [StringLength(1000, ErrorMessage = "加工要求长度不能超过1000个字符")]
        public string? ProcessingRequirements { get; set; }

        /// <summary>
        /// 预计完成时间
        /// </summary>
        public DateTime? EstimatedCompletionDate { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        public DateTime? ActualCompletionDate { get; set; }

        /// <summary>
        /// 加工费用
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "加工费用不能为负数")]
        public decimal? ProcessingCost { get; set; }

        #endregion

        #region 进度状态

        /// <summary>
        /// 加工状态
        /// </summary>
        public int ProcessingStatus { get; set; } = 1;

        /// <summary>
        /// 加工状态名称
        /// </summary>
        public string ProcessingStatusName => ProcessingStatus switch
        {
            1 => "已下单",
            2 => "加工中",
            3 => "待验收",
            4 => "已完成",
            5 => "已取消",
            _ => "未知"
        };

        #endregion

        #region 时间戳

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string CreatedByName { get; set; } = string.Empty;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 更新人姓名
        /// </summary>
        public string? UpdatedByName { get; set; }

        #endregion

        #region 备注信息

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        #endregion

        #region 计算属性

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted => ProcessingStatus == 4;

        /// <summary>
        /// 是否已取消
        /// </summary>
        public bool IsCancelled => ProcessingStatus == 5;

        /// <summary>
        /// 是否可以更新进度
        /// </summary>
        public bool CanUpdateProgress => ProcessingStatus is 1 or 2 or 3;

        /// <summary>
        /// 是否超期
        /// </summary>
        public bool IsOverdue => EstimatedCompletionDate.HasValue &&
                                EstimatedCompletionDate.Value < DateTime.Now &&
                                !IsCompleted && !IsCancelled;

        /// <summary>
        /// 加工天数
        /// </summary>
        public int ProcessingDays
        {
            get
            {
                var endDate = ActualCompletionDate ?? DateTime.Now;
                return (int)(endDate - CreatedAt).TotalDays;
            }
        }

        /// <summary>
        /// 状态颜色
        /// </summary>
        public string StatusColor => ProcessingStatus switch
        {
            1 => "warning",   // 已下单 - 橙色
            2 => "info",      // 加工中 - 蓝色
            3 => "primary",   // 待验收 - 主色
            4 => "success",   // 已完成 - 绿色
            5 => "error",     // 已取消 - 红色
            _ => "default"
        };

        #endregion


    }
}