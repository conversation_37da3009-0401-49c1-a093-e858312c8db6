@echo off
echo ========================================
echo    CoreHub - Current Environment Status
echo ========================================
echo.

echo CoreHub Environment Variables:
echo ----------------------------------------

if defined COREHUB_ENVIRONMENT (
    echo COREHUB_ENVIRONMENT = %COREHUB_ENVIRONMENT%

    if /i "%COREHUB_ENVIRONMENT%"=="Development" (
        echo [INFO] CoreHub Development environment detected
    ) else if /i "%COREHUB_ENVIRONMENT%"=="Staging" (
        echo [INFO] CoreHub Staging environment detected
    ) else if /i "%COREHUB_ENVIRONMENT%"=="Production" (
        echo [WARNING] CoreHub Production environment detected
    ) else (
        echo [WARNING] Unknown CoreHub environment: %COREHUB_ENVIRONMENT%
    )
) else (
    echo COREHUB_ENVIRONMENT = [NOT SET] (will use default configuration)
)

echo.

if defined COREHUB_API_BASE_URL (
    echo COREHUB_API_BASE_URL = %COREHUB_API_BASE_URL%
) else (
    echo COREHUB_API_BASE_URL = [NOT SET] (will use default)
)

if defined COREHUB_DB_CONNECTION_STRING (
    echo COREHUB_DB_CONNECTION_STRING = [SET] (hidden for security)
) else (
    echo COREHUB_DB_CONNECTION_STRING = [NOT SET] (will use default)
)

if defined COREHUB_USE_HTTPS_REDIRECTION (
    echo COREHUB_USE_HTTPS_REDIRECTION = %COREHUB_USE_HTTPS_REDIRECTION%
) else (
    echo COREHUB_USE_HTTPS_REDIRECTION = [NOT SET] (will use default)
)

if defined COREHUB_ENABLE_VERBOSE_LOGGING (
    echo COREHUB_ENABLE_VERBOSE_LOGGING = %COREHUB_ENABLE_VERBOSE_LOGGING%
) else (
    echo COREHUB_ENABLE_VERBOSE_LOGGING = [NOT SET] (will use default)
)

echo.
echo System Information:
echo ----------------------------------------
echo Computer Name: %COMPUTERNAME%
echo User Name: %USERNAME%
echo Current Directory: %CD%
echo Date/Time: %DATE% %TIME%

echo.
echo Configuration Source:
echo ----------------------------------------
if defined COREHUB_API_BASE_URL (
    echo Using CoreHub-specific environment variables
) else (
    echo Using default configuration from appsettings.json
)

echo.
echo Project Isolation:
echo ----------------------------------------
echo CoreHub uses COREHUB_* environment variables
echo This ensures no conflicts with other projects
echo that may use generic environment variables

echo.
echo ========================================
echo.
pause
