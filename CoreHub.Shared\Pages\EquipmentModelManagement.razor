@page "/equipment-model-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IEquipmentModelService EquipmentModelService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备型号管理</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.PrecisionManufacturing" Class="mr-2" />
                    设备型号管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索型号名称、编码或类别..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudSelect T="string" @bind-Value="selectedCategory"
                                 Label="设备类别"
                                 Clearable="true"
                                 OnSelectionChanged="OnCategoryChanged"
                                 Class="mr-4">
                            @foreach (var category in categories)
                            {
                                <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                            }
                        </MudSelect>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadEquipmentModels">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增设备型号
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="EquipmentModel" 
                           Items="@filteredEquipmentModels" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.Code" Title="型号编码" />
                        <PropertyColumn Property="x => x.Name" Title="型号名称" />
                        <PropertyColumn Property="x => x.Category" Title="设备类别" />
                        <PropertyColumn Property="x => x.Brand" Title="品牌" />
                        <PropertyColumn Property="x => x.Model" Title="规格型号" />
                        <PropertyColumn Property="x => x.Description" Title="描述" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteEquipmentModel(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private List<EquipmentModel> equipmentModels = new();
    private List<EquipmentModel> filteredEquipmentModels = new();
    private List<string> categories = new();
    private bool loading = false;
    private string searchText = string.Empty;
    private string? selectedCategory = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadEquipmentModels();
        await LoadCategories();
    }

    private async Task LoadEquipmentModels()
    {
        loading = true;
        try
        {
            equipmentModels = await EquipmentModelService.GetAllEquipmentModelsAsync();
            FilterEquipmentModels();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备型号数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = await EquipmentModelService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备类别失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterEquipmentModels()
    {
        var filtered = equipmentModels.AsEnumerable();

        // 按搜索文本过滤
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            filtered = filtered.Where(em => 
                em.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                em.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                em.Category.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (em.Brand?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (em.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            );
        }

        // 按类别过滤
        if (!string.IsNullOrWhiteSpace(selectedCategory))
        {
            filtered = filtered.Where(em => em.Category == selectedCategory);
        }

        filteredEquipmentModels = filtered.ToList();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterEquipmentModels();
    }

    private void OnCategoryChanged(string? category)
    {
        selectedCategory = category;
        FilterEquipmentModels();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<EquipmentModelEditDialog>
        {
            { x => x.EquipmentModel, new EquipmentModel() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<EquipmentModelEditDialog>("新增设备型号", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipmentModels();
            await LoadCategories();
        }
    }

    private async Task OpenEditDialog(EquipmentModel equipmentModel)
    {
        var parameters = new DialogParameters<EquipmentModelEditDialog>
        {
            { x => x.EquipmentModel, equipmentModel },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<EquipmentModelEditDialog>("编辑设备型号", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadEquipmentModels();
            await LoadCategories();
        }
    }

    private async Task ToggleStatus(EquipmentModel equipmentModel)
    {
        try
        {
            var result = await EquipmentModelService.ToggleStatusAsync(equipmentModel.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"设备型号状态已{(equipmentModel.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadEquipmentModels();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteEquipmentModel(EquipmentModel equipmentModel)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除设备型号 '{equipmentModel.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await EquipmentModelService.DeleteEquipmentModelAsync(equipmentModel.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("设备型号删除成功", Severity.Success);
                    await LoadEquipmentModels();
                    await LoadCategories();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
