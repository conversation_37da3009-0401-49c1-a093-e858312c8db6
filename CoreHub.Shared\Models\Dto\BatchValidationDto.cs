namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 批量操作验证结果DTO
    /// </summary>
    public class BatchValidationResultDto
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<ValidationErrorDto> Errors { get; set; } = new List<ValidationErrorDto>();

        /// <summary>
        /// 验证警告列表
        /// </summary>
        public List<ValidationWarningDto> Warnings { get; set; } = new List<ValidationWarningDto>();

        /// <summary>
        /// 验证摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 可执行的操作数量
        /// </summary>
        public int ValidOperationCount { get; set; }

        /// <summary>
        /// 无效的操作数量
        /// </summary>
        public int InvalidOperationCount { get; set; }
    }

    /// <summary>
    /// 验证错误DTO
    /// </summary>
    public class ValidationErrorDto
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 相关对象ID
        /// </summary>
        public string? RelatedId { get; set; }

        /// <summary>
        /// 相关对象名称
        /// </summary>
        public string? RelatedName { get; set; }

        /// <summary>
        /// 错误级别
        /// </summary>
        public ValidationErrorLevel Level { get; set; } = ValidationErrorLevel.Error;
    }

    /// <summary>
    /// 验证警告DTO
    /// </summary>
    public class ValidationWarningDto
    {
        /// <summary>
        /// 警告代码
        /// </summary>
        public string WarningCode { get; set; } = string.Empty;

        /// <summary>
        /// 警告消息
        /// </summary>
        public string WarningMessage { get; set; } = string.Empty;

        /// <summary>
        /// 相关对象ID
        /// </summary>
        public string? RelatedId { get; set; }

        /// <summary>
        /// 相关对象名称
        /// </summary>
        public string? RelatedName { get; set; }

        /// <summary>
        /// 建议操作
        /// </summary>
        public string? SuggestedAction { get; set; }
    }

    /// <summary>
    /// 验证错误级别
    /// </summary>
    public enum ValidationErrorLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info = 1,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 2,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// 批量操作影响预览DTO
    /// </summary>
    public class BatchImpactPreviewDto
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 影响的设备数量
        /// </summary>
        public int AffectedEquipmentCount { get; set; }

        /// <summary>
        /// 影响的部件数量
        /// </summary>
        public int AffectedComponentCount { get; set; }

        /// <summary>
        /// 新增的配置数量
        /// </summary>
        public int NewConfigurationCount { get; set; }

        /// <summary>
        /// 更新的配置数量
        /// </summary>
        public int UpdatedConfigurationCount { get; set; }

        /// <summary>
        /// 删除的配置数量
        /// </summary>
        public int DeletedConfigurationCount { get; set; }

        /// <summary>
        /// 影响详情列表
        /// </summary>
        public List<ImpactDetailDto> ImpactDetails { get; set; } = new List<ImpactDetailDto>();

        /// <summary>
        /// 预估执行时间（秒）
        /// </summary>
        public int EstimatedExecutionTimeSeconds { get; set; }

        /// <summary>
        /// 操作摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// 影响详情DTO
    /// </summary>
    public class ImpactDetailDto
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 影响的部件列表
        /// </summary>
        public List<ComponentImpactDto> ComponentImpacts { get; set; } = new List<ComponentImpactDto>();

        /// <summary>
        /// 操作状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 部件影响DTO
    /// </summary>
    public class ComponentImpactDto
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 原始配置
        /// </summary>
        public ComponentConfigurationDto? OriginalConfiguration { get; set; }

        /// <summary>
        /// 新配置
        /// </summary>
        public ComponentConfigurationDto? NewConfiguration { get; set; }

        /// <summary>
        /// 变更描述
        /// </summary>
        public string ChangeDescription { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量操作进度DTO
    /// </summary>
    public class BatchOperationProgressDto
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 已处理数量
        /// </summary>
        public int ProcessedCount { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 进度百分比
        /// </summary>
        public double ProgressPercentage => TotalCount > 0 ? (double)ProcessedCount / TotalCount * 100 : 0;

        /// <summary>
        /// 当前状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 当前处理项目
        /// </summary>
        public string? CurrentItem { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 预计完成时间
        /// </summary>
        public DateTime? EstimatedCompletionTime { get; set; }

        /// <summary>
        /// 错误消息列表
        /// </summary>
        public List<string> ErrorMessages { get; set; } = new List<string>();
    }
}
