using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Linq;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部位服务实现
    /// </summary>
    public class EquipmentPartService : IEquipmentPartService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentPartService> _logger;

        public EquipmentPartService(
            DatabaseContext dbContext,
            ILogger<EquipmentPartService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<EquipmentPart>> GetAllEquipmentPartsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.IsEnabled)
                    .OrderBy(ep => new { ep.EquipmentId, ep.Level, ep.SortOrder })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备部位失败");
                throw;
            }
        }

        public async Task<EquipmentPart?> GetEquipmentPartByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取设备部位失败: {id}", id);
                throw;
            }
        }

        public async Task<List<EquipmentPart>> GetEquipmentPartsByEquipmentIdAsync(int equipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.EquipmentId == equipmentId && ep.IsEnabled)
                    .OrderBy(ep => new { ep.Level, ep.SortOrder })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备ID获取设备部位失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<EquipmentPart>> GetRootPartsByEquipmentIdAsync(int equipmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.EquipmentId == equipmentId && ep.ParentId == null && ep.IsEnabled)
                    .OrderBy(ep => ep.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备根级部位失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<EquipmentPart>> GetChildPartsByParentIdAsync(int parentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.ParentId == parentId && ep.IsEnabled)
                    .OrderBy(ep => ep.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取子部位失败: {parentId}", parentId);
                throw;
            }
        }

        public async Task<List<EquipmentPart>> GetEquipmentPartTreeAsync(int equipmentId)
        {
            try
            {
                // 获取所有部位
                var allParts = await GetEquipmentPartsByEquipmentIdAsync(equipmentId);
                
                // 构建树形结构
                var partDict = allParts.ToDictionary(p => p.Id, p => p);
                var rootParts = new List<EquipmentPart>();

                foreach (var part in allParts)
                {
                    if (part.ParentId == null)
                    {
                        rootParts.Add(part);
                    }
                    else if (partDict.ContainsKey(part.ParentId.Value))
                    {
                        var parent = partDict[part.ParentId.Value];
                        parent.Children.Add(part);
                        part.Parent = parent;
                    }
                }

                return rootParts.OrderBy(p => p.SortOrder).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备部位树形结构失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<EquipmentPartDto>> GetEquipmentPartsFlatListAsync(int equipmentId)
        {
            try
            {
                var tree = await GetEquipmentPartTreeAsync(equipmentId);
                var flatList = new List<EquipmentPartDto>();

                void FlattenTree(List<EquipmentPart> parts)
                {
                    foreach (var part in parts)
                    {
                        flatList.Add(new EquipmentPartDto
                        {
                            Id = part.Id,
                            Code = part.Code,
                            Name = part.Name,
                            FullName = part.FullName,
                            IndentedName = part.IndentedName,
                            Level = part.Level,
                            ParentId = part.ParentId,
                            IsEnabled = part.IsEnabled,
                            Description = part.Description
                        });

                        if (part.Children.Any())
                        {
                            FlattenTree(part.Children);
                        }
                    }
                }

                FlattenTree(tree);
                return flatList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备部位扁平化列表失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int? PartId)> CreateEquipmentPartAsync(EquipmentPart equipmentPart)
        {
            try
            {
                // 验证设备是否存在
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == equipmentPart.EquipmentId)
                    .FirstAsync();
                if (equipment == null)
                {
                    return (false, "指定的设备不存在", null);
                }

                // 验证编码唯一性
                var isCodeUnique = await IsPartCodeUniqueAsync(equipmentPart.Code, equipmentPart.EquipmentId);
                if (!isCodeUnique)
                {
                    return (false, "部位编码已存在", null);
                }

                // 验证父级部位
                if (equipmentPart.ParentId.HasValue)
                {
                    var parent = await GetEquipmentPartByIdAsync(equipmentPart.ParentId.Value);
                    if (parent == null || parent.EquipmentId != equipmentPart.EquipmentId)
                    {
                        return (false, "指定的父级部位不存在或不属于同一设备", null);
                    }
                    equipmentPart.Level = parent.Level + 1;
                }
                else
                {
                    equipmentPart.Level = 1;
                }

                equipmentPart.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(equipmentPart).ExecuteReturnIdentityAsync();

                _logger.LogInformation("创建设备部位成功: {code}", equipmentPart.Code);
                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建设备部位失败");
                return (false, $"创建设备部位失败: {ex.Message}", null);
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentPartAsync(EquipmentPart equipmentPart)
        {
            try
            {
                // 验证部位是否存在
                var existingPart = await GetEquipmentPartByIdAsync(equipmentPart.Id);
                if (existingPart == null)
                {
                    return (false, "设备部位不存在");
                }

                // 验证编码唯一性
                var isCodeUnique = await IsPartCodeUniqueAsync(equipmentPart.Code, equipmentPart.EquipmentId, equipmentPart.Id);
                if (!isCodeUnique)
                {
                    return (false, "部位编码已存在");
                }

                // 验证父级部位
                if (equipmentPart.ParentId.HasValue)
                {
                    if (equipmentPart.ParentId == equipmentPart.Id)
                    {
                        return (false, "不能将自己设置为父级部位");
                    }

                    var parent = await GetEquipmentPartByIdAsync(equipmentPart.ParentId.Value);
                    if (parent == null || parent.EquipmentId != equipmentPart.EquipmentId)
                    {
                        return (false, "指定的父级部位不存在或不属于同一设备");
                    }
                    equipmentPart.Level = parent.Level + 1;
                }
                else
                {
                    equipmentPart.Level = 1;
                }

                equipmentPart.UpdatedAt = DateTime.Now;
                await _dbContext.Db.Updateable(equipmentPart).ExecuteCommandAsync();

                _logger.LogInformation("更新设备部位成功: {id}", equipmentPart.Id);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备部位失败: {id}", equipmentPart.Id);
                return (false, $"更新设备部位失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentPartAsync(int id)
        {
            try
            {
                // 检查是否可以删除
                var canDelete = await CanDeletePartAsync(id);
                if (!canDelete)
                {
                    return (false, "该部位存在子部位或关联的报修单，无法删除");
                }

                await _dbContext.Db.Deleteable<EquipmentPart>().Where(ep => ep.Id == id).ExecuteCommandAsync();

                _logger.LogInformation("删除设备部位成功: {id}", id);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备部位失败: {id}", id);
                return (false, $"删除设备部位失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteEquipmentPartsAsync(List<int> ids)
        {
            try
            {
                // 检查所有部位是否可以删除
                foreach (var id in ids)
                {
                    var canDelete = await CanDeletePartAsync(id);
                    if (!canDelete)
                    {
                        var part = await GetEquipmentPartByIdAsync(id);
                        return (false, $"部位 '{part?.Name}' 存在子部位或关联的报修单，无法删除");
                    }
                }

                await _dbContext.Db.Deleteable<EquipmentPart>().Where(ep => ids.Contains(ep.Id)).ExecuteCommandAsync();

                _logger.LogInformation("批量删除设备部位成功: {count} 条", ids.Count);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除设备部位失败");
                return (false, $"批量删除设备部位失败: {ex.Message}");
            }
        }

        public async Task<bool> IsPartCodeUniqueAsync(string code, int equipmentId, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.Code == code && ep.EquipmentId == equipmentId);

                if (excludeId.HasValue)
                {
                    query = query.Where(ep => ep.Id != excludeId.Value);
                }

                var count = await query.CountAsync();
                return count == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部位编码唯一性失败");
                throw;
            }
        }

        public async Task<bool> CanDeletePartAsync(int id)
        {
            try
            {
                // 检查是否有子部位
                var childCount = await _dbContext.Db.Queryable<EquipmentPart>()
                    .Where(ep => ep.ParentId == id)
                    .CountAsync();

                if (childCount > 0)
                {
                    return false;
                }

                // 检查是否有关联的报修单
                var repairOrderCount = await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.FaultPartId == id)
                    .CountAsync();

                return repairOrderCount == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部位是否可删除失败: {id}", id);
                throw;
            }
        }

        public async Task<string> GetPartFullPathAsync(int id)
        {
            try
            {
                var part = await GetEquipmentPartByIdAsync(id);
                if (part == null)
                {
                    return string.Empty;
                }

                var pathParts = new List<string> { part.Name };
                var currentParentId = part.ParentId;

                while (currentParentId.HasValue)
                {
                    var parent = await GetEquipmentPartByIdAsync(currentParentId.Value);
                    if (parent == null) break;

                    pathParts.Insert(0, parent.Name);
                    currentParentId = parent.ParentId;
                }

                return string.Join(" > ", pathParts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部位完整路径失败: {id}", id);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> MovePartAsync(int partId, int? newParentId)
        {
            try
            {
                var part = await GetEquipmentPartByIdAsync(partId);
                if (part == null)
                {
                    return (false, "设备部位不存在");
                }

                // 验证新父级部位
                if (newParentId.HasValue)
                {
                    if (newParentId == partId)
                    {
                        return (false, "不能将自己设置为父级部位");
                    }

                    var newParent = await GetEquipmentPartByIdAsync(newParentId.Value);
                    if (newParent == null || newParent.EquipmentId != part.EquipmentId)
                    {
                        return (false, "指定的父级部位不存在或不属于同一设备");
                    }

                    // 检查是否会形成循环引用
                    var descendants = await GetDescendantIdsAsync(partId);
                    if (descendants.Contains(newParentId.Value))
                    {
                        return (false, "不能移动到自己的子部位下，这会形成循环引用");
                    }

                    part.Level = newParent.Level + 1;
                }
                else
                {
                    part.Level = 1;
                }

                part.ParentId = newParentId;
                part.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(part).ExecuteCommandAsync();

                // 更新所有子部位的层级
                await UpdateChildrenLevelsAsync(partId);

                _logger.LogInformation("移动设备部位成功: {partId} -> {newParentId}", partId, newParentId);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动设备部位失败: {partId}", partId);
                return (false, $"移动设备部位失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CopyPartStructureAsync(int sourceEquipmentId, int targetEquipmentId)
        {
            try
            {
                // 验证目标设备是否存在
                var targetEquipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == targetEquipmentId)
                    .FirstAsync();
                if (targetEquipment == null)
                {
                    return (false, "目标设备不存在");
                }

                // 获取源设备的部位结构
                var sourceParts = await GetEquipmentPartTreeAsync(sourceEquipmentId);
                if (!sourceParts.Any())
                {
                    return (false, "源设备没有部位结构可复制");
                }

                // 递归复制部位结构
                var partIdMapping = new Dictionary<int, int>();
                await CopyPartsRecursiveAsync(sourceParts, targetEquipmentId, null, partIdMapping);

                _logger.LogInformation("复制设备部位结构成功: {sourceId} -> {targetId}", sourceEquipmentId, targetEquipmentId);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制设备部位结构失败: {sourceId} -> {targetId}", sourceEquipmentId, targetEquipmentId);
                return (false, $"复制设备部位结构失败: {ex.Message}");
            }
        }

        public async Task<Dictionary<int, int>> GetPartUsageStatisticsAsync(int equipmentId)
        {
            try
            {
                var statistics = await _dbContext.Db.Queryable<RepairOrder>()
                    .LeftJoin<EquipmentPart>((ro, ep) => ro.FaultPartId == ep.Id)
                    .Where((ro, ep) => ep.EquipmentId == equipmentId)
                    .GroupBy((ro, ep) => ep.Id)
                    .Select((ro, ep) => new { PartId = ep.Id, Count = SqlFunc.AggregateCount(ro.Id) })
                    .ToListAsync();

                return statistics.ToDictionary(s => s.PartId, s => s.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部位使用统计失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<EquipmentPart>> SearchEquipmentPartsAsync(EquipmentPartSearchDto searchDto)
        {
            try
            {
                var query = _dbContext.Db.Queryable<EquipmentPart>().Where(ep => true);

                if (!string.IsNullOrWhiteSpace(searchDto.SearchText))
                {
                    query = query.Where(ep => ep.Name.Contains(searchDto.SearchText) ||
                                            ep.Code.Contains(searchDto.SearchText) ||
                                            ep.Description.Contains(searchDto.SearchText));
                }

                if (searchDto.EquipmentId.HasValue)
                {
                    query = query.Where(ep => ep.EquipmentId == searchDto.EquipmentId.Value);
                }

                if (searchDto.ParentId.HasValue)
                {
                    query = query.Where(ep => ep.ParentId == searchDto.ParentId.Value);
                }

                if (searchDto.Level.HasValue)
                {
                    query = query.Where(ep => ep.Level == searchDto.Level.Value);
                }

                if (searchDto.IsEnabled.HasValue)
                {
                    query = query.Where(ep => ep.IsEnabled == searchDto.IsEnabled.Value);
                }

                return await query.OrderBy(ep => new { ep.EquipmentId, ep.Level, ep.SortOrder })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索设备部位失败");
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> TogglePartStatusAsync(int id, bool isEnabled)
        {
            try
            {
                var part = await GetEquipmentPartByIdAsync(id);
                if (part == null)
                {
                    return (false, "设备部位不存在");
                }

                part.IsEnabled = isEnabled;
                part.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(part).ExecuteCommandAsync();

                _logger.LogInformation("切换设备部位状态成功: {id} -> {status}", id, isEnabled);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换设备部位状态失败: {id}", id);
                return (false, $"切换设备部位状态失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdatePartSortOrderAsync(List<PartSortOrderDto> sortOrders)
        {
            try
            {
                foreach (var sortOrder in sortOrders)
                {
                    await _dbContext.Db.Updateable<EquipmentPart>()
                        .SetColumns(ep => ep.SortOrder == sortOrder.SortOrder)
                        .SetColumns(ep => ep.UpdatedAt == DateTime.Now)
                        .Where(ep => ep.Id == sortOrder.Id)
                        .ExecuteCommandAsync();
                }

                _logger.LogInformation("批量更新部位排序成功: {count} 条", sortOrders.Count);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新部位排序失败");
                return (false, $"批量更新部位排序失败: {ex.Message}");
            }
        }

        #region 私有辅助方法

        private async Task<List<int>> GetDescendantIdsAsync(int partId)
        {
            var descendants = new List<int>();
            var children = await GetChildPartsByParentIdAsync(partId);

            foreach (var child in children)
            {
                descendants.Add(child.Id);
                var childDescendants = await GetDescendantIdsAsync(child.Id);
                descendants.AddRange(childDescendants);
            }

            return descendants;
        }

        private async Task UpdateChildrenLevelsAsync(int parentId)
        {
            var parent = await GetEquipmentPartByIdAsync(parentId);
            if (parent == null) return;

            var children = await GetChildPartsByParentIdAsync(parentId);
            foreach (var child in children)
            {
                child.Level = parent.Level + 1;
                child.UpdatedAt = DateTime.Now;
                await _dbContext.Db.Updateable(child).ExecuteCommandAsync();

                // 递归更新子部位的层级
                await UpdateChildrenLevelsAsync(child.Id);
            }
        }

        private async Task CopyPartsRecursiveAsync(List<EquipmentPart> parts, int targetEquipmentId, int? parentId, Dictionary<int, int> partIdMapping)
        {
            foreach (var part in parts)
            {
                var newPart = new EquipmentPart
                {
                    Code = $"{part.Code}_COPY",
                    Name = part.Name,
                    EquipmentId = targetEquipmentId,
                    ParentId = parentId,
                    Level = part.Level,
                    SortOrder = part.SortOrder,
                    Description = part.Description,
                    IsEnabled = part.IsEnabled,
                    CreatedAt = DateTime.Now
                };

                var newPartId = await _dbContext.Db.Insertable(newPart).ExecuteReturnIdentityAsync();
                partIdMapping[part.Id] = newPartId;

                // 递归复制子部位
                if (part.Children.Any())
                {
                    await CopyPartsRecursiveAsync(part.Children, targetEquipmentId, newPartId, partIdMapping);
                }
            }
        }

        #endregion
    }
}
