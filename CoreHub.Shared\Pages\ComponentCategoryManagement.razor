@page "/component-category-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IComponentCategoryService ComponentCategoryService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>部件分类管理</PageTitle>

<div>
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Category" Class="mr-2" />
                    部件分类管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索分类名称或编码..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadCategories">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Success" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增分类
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="ComponentCategory" 
                           Items="@filteredCategories" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px"
                           SortMode="SortMode.Single"
                           Groupable="true"
                           GroupExpanded="true">
                    <Columns>
                        <PropertyColumn Property="x => x.Level" Title="级别" Grouping />
                        <PropertyColumn Property="x => x.Code" Title="分类编码" />
                        <PropertyColumn Property="x => x.Name" Title="分类名称" />
                        <TemplateColumn Title="父级分类" Sortable="false">
                            <CellTemplate>
                                @if (context.Item.Parent != null)
                                {
                                    <MudText>@context.Item.Parent.Name</MudText>
                                }
                                else
                                {
                                    <MudText Color="Color.Secondary">根级分类</MudText>
                                }
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.Description" Title="描述" />
                        <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="操作" Sortable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudTooltip Text="编辑">
                                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                     Color="Color.Primary"
                                                     Size="Size.Small"
                                                     OnClick="() => OpenEditDialog(context.Item)" />
                                    </MudTooltip>
                                    <MudTooltip Text="@(context.Item.IsEnabled ? "禁用" : "启用")">
                                        <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.VisibilityOff : Icons.Material.Filled.Visibility)"
                                                     Color="Color.Warning"
                                                     Size="Size.Small"
                                                     OnClick="() => ToggleStatus(context.Item)" />
                                    </MudTooltip>
                                    <MudTooltip Text="删除">
                                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                     Color="Color.Error"
                                                     Size="Size.Small"
                                                     OnClick="() => DeleteCategory(context.Item)" />
                                    </MudTooltip>
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</div>

@code {
    private List<ComponentCategory> categories = new();
    private List<ComponentCategory> filteredCategories = new();
    private string searchText = string.Empty;
    private bool loading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    private async Task LoadCategories()
    {
        try
        {
            loading = true;
            StateHasChanged();

            categories = await ComponentCategoryService.GetAllCategoryTreeAsync();
            
            // 构建平铺列表用于显示
            filteredCategories = FlattenCategoryTree(categories);
            FilterCategories();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载分类失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private List<ComponentCategory> FlattenCategoryTree(List<ComponentCategory> tree)
    {
        var result = new List<ComponentCategory>();
        foreach (var category in tree)
        {
            result.Add(category);
            if (category.Children.Any())
            {
                result.AddRange(FlattenCategoryTree(category.Children));
            }
        }
        return result;
    }

    private void FilterCategories()
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            filteredCategories = FlattenCategoryTree(categories);
        }
        else
        {
            var allCategories = FlattenCategoryTree(categories);
            filteredCategories = allCategories.Where(c => 
                c.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                c.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(c.Description) && c.Description.Contains(searchText, StringComparison.OrdinalIgnoreCase))
            ).ToList();
        }
        StateHasChanged();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterCategories();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters();
        parameters.Add("Category", new ComponentCategory());
        parameters.Add("IsEdit", false);
        parameters.Add("AllCategories", categories);

        var dialog = await DialogService.ShowAsync<ComponentCategoryEditDialog>("新增部件分类", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadCategories();
        }
    }

    private async Task OpenEditDialog(ComponentCategory category)
    {
        var parameters = new DialogParameters();
        parameters.Add("Category", category);
        parameters.Add("IsEdit", true);
        parameters.Add("AllCategories", categories);

        var dialog = await DialogService.ShowAsync<ComponentCategoryEditDialog>("编辑部件分类", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadCategories();
        }
    }

    private async Task ToggleStatus(ComponentCategory category)
    {
        try
        {
            var result = await ComponentCategoryService.ToggleStatusAsync(category.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"分类状态已{(category.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadCategories();
            }
            else
            {
                Snackbar.Add(result.ErrorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteCategory(ComponentCategory category)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除分类 '{category.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await ComponentCategoryService.DeleteCategoryAsync(category.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("分类删除成功", Severity.Success);
                    await LoadCategories();
                }
                else
                {
                    Snackbar.Add(result.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
