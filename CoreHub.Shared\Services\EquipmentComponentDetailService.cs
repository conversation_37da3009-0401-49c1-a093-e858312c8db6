using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件明细服务实现
    /// </summary>
    public class EquipmentComponentDetailService : IEquipmentComponentDetailService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentComponentDetailService> _logger;

        public EquipmentComponentDetailService(
            DatabaseContext dbContext,
            ILogger<EquipmentComponentDetailService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<EquipmentComponentDetailDto>> GetAllEquipmentComponentDetailsAsync()
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备部件明细失败");
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> GetEquipmentComponentDetailsByEquipmentIdAsync(int equipmentId)
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync(equipmentId: equipmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备ID获取部件明细失败: {equipmentId}", equipmentId);
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> GetEquipmentComponentDetailsByModelIdAsync(int modelId)
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync(modelId: modelId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备型号ID获取部件明细失败: {modelId}", modelId);
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> GetEquipmentComponentDetailsByDepartmentIdAsync(int departmentId)
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync(departmentId: departmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据部门ID获取设备部件明细失败: {departmentId}", departmentId);
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> SearchEquipmentComponentDetailsAsync(
            string? keyword = null, 
            int? equipmentId = null, 
            int? modelId = null, 
            int? departmentId = null, 
            int? componentCategoryId = null)
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync(
                    keyword: keyword,
                    equipmentId: equipmentId,
                    modelId: modelId,
                    departmentId: departmentId,
                    componentCategoryId: componentCategoryId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索设备部件明细失败");
                throw;
            }
        }

        public async Task<EquipmentComponentDetailStatisticsDto> GetEquipmentComponentDetailStatisticsAsync()
        {
            try
            {
                var details = await GetAllEquipmentComponentDetailsAsync();

                var statistics = new EquipmentComponentDetailStatisticsDto
                {
                    TotalEquipmentCount = details.Select(d => d.EquipmentId).Distinct().Count(),
                    TotalComponentTypeCount = details.Select(d => d.ComponentId).Distinct().Count(),
                    TotalComponentQuantity = details.Sum(d => d.StandardQuantity),
                    RequiredComponentCount = details.Where(d => d.IsRequired).Sum(d => d.StandardQuantity),
                    OptionalComponentCount = details.Where(d => !d.IsRequired).Sum(d => d.StandardQuantity),
                    LowStockComponentCount = details.Where(d => d.ComponentStockStatus == "库存不足" || d.ComponentStockStatus == "缺货").Count(),
                    MaintenanceDueComponentCount = details.Where(d => d.ReplacementCycleDays.HasValue).Count(),
                    TotalComponentValue = details.Sum(d => d.TotalValue ?? 0)
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备部件明细统计信息失败");
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> ExportEquipmentComponentDetailsAsync(
            int? equipmentId = null, 
            int? modelId = null, 
            int? departmentId = null)
        {
            try
            {
                return await GetEquipmentComponentDetailsInternalAsync(
                    equipmentId: equipmentId,
                    modelId: modelId,
                    departmentId: departmentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出设备部件明细数据失败");
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> GetLowStockEquipmentComponentDetailsAsync()
        {
            try
            {
                var details = await GetAllEquipmentComponentDetailsAsync();
                return details.Where(d => d.ComponentStockStatus == "库存不足" || d.ComponentStockStatus == "缺货").ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存不足的设备部件明细失败");
                throw;
            }
        }

        public async Task<List<EquipmentComponentDetailDto>> GetMaintenanceDueEquipmentComponentDetailsAsync()
        {
            try
            {
                var details = await GetAllEquipmentComponentDetailsAsync();
                return details.Where(d => d.ReplacementCycleDays.HasValue).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取需要维护的设备部件明细失败");
                throw;
            }
        }

        /// <summary>
        /// 内部方法：获取设备部件明细数据
        /// </summary>
        private async Task<List<EquipmentComponentDetailDto>> GetEquipmentComponentDetailsInternalAsync(
            string? keyword = null,
            int? equipmentId = null,
            int? modelId = null,
            int? departmentId = null,
            int? componentCategoryId = null)
        {
            // 从实际的设备部件关联表中获取数据
            var query = _dbContext.Db.Queryable<EquipmentComponent>()
                .LeftJoin<Equipment>((ec, eq) => ec.EquipmentId == eq.Id)
                .LeftJoin<Component>((ec, eq, c) => ec.ComponentId == c.Id)
                .LeftJoin<EquipmentModel>((ec, eq, c, em) => eq.ModelId == em.Id)
                .LeftJoin<Department>((ec, eq, c, em, d) => eq.DepartmentId == d.Id)
                .LeftJoin<Location>((ec, eq, c, em, d, l) => eq.LocationId == l.Id)
                .LeftJoin<ComponentCategory>((ec, eq, c, em, d, l, cc) => c.CategoryId == cc.Id)
                .Where((ec, eq, c, em, d, l, cc) => ec.IsEnabled && eq.IsEnabled && c.IsEnabled);

            // 应用筛选条件
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                query = query.Where((ec, eq, c, em, d, l, cc) =>
                    eq.Name.Contains(keyword) ||
                    eq.Code.Contains(keyword) ||
                    c.Name.Contains(keyword) ||
                    c.Code.Contains(keyword) ||
                    (c.Model != null && c.Model.Contains(keyword)) ||
                    (c.Brand != null && c.Brand.Contains(keyword)) ||
                    (em.Name != null && em.Name.Contains(keyword)));
            }

            if (equipmentId.HasValue)
            {
                query = query.Where((ec, eq, c, em, d, l, cc) => eq.Id == equipmentId.Value);
            }

            if (modelId.HasValue)
            {
                query = query.Where((ec, eq, c, em, d, l, cc) => eq.ModelId == modelId.Value);
            }

            if (departmentId.HasValue)
            {
                query = query.Where((ec, eq, c, em, d, l, cc) => eq.DepartmentId == departmentId.Value);
            }

            if (componentCategoryId.HasValue)
            {
                query = query.Where((ec, eq, c, em, d, l, cc) => c.CategoryId == componentCategoryId.Value);
            }

            var results = await query.Select((ec, eq, c, em, d, l, cc) => new EquipmentComponentDetailDto
            {
                // 设备部件关联信息
                EquipmentComponentId = ec.Id,
                Quantity = ec.Quantity,
                InstallDate = ec.InstallDate,
                LastReplacementDate = ec.LastReplacementDate,
                NextReplacementDate = ec.NextReplacementDate,
                ComponentStatus = ec.Status,
                // ComponentStatusName 将在后面计算

                // 设备信息
                EquipmentId = eq.Id,
                EquipmentCode = eq.Code,
                EquipmentName = eq.Name,
                EquipmentModelId = eq.ModelId,
                EquipmentModelName = em.Name,
                EquipmentModelCategory = em.Category,
                EquipmentModelBrand = em.Brand,
                DepartmentId = eq.DepartmentId,
                DepartmentName = d.Name,
                LocationId = eq.LocationId,
                LocationName = l.Name,
                EquipmentStatus = eq.Status,
                // EquipmentStatusName 将在后面计算
                EquipmentSerialNumber = eq.SerialNumber,
                EquipmentAssetNumber = eq.AssetNumber,

                // 部件信息
                ComponentId = c.Id,
                ComponentCode = c.Code,
                ComponentName = c.Name,
                ComponentCategoryId = c.CategoryId,
                ComponentCategoryName = cc.Name,
                ComponentModel = c.Model,
                ComponentSpecifications = c.Specifications,
                ComponentSupplier = c.Supplier,
                ComponentBrand = c.Brand,
                ComponentUnit = c.Unit,
                StandardQuantity = ec.Quantity, // 使用实际数量而不是模板数量
                IsRequired = ec.IsRequired,
                ComponentTypeName = ec.IsRequired ? "必需部件" : "可选部件",
                ReplacementCycleDays = ec.ReplacementCycleDays,
                MaintenanceNotes = ec.MaintenanceNotes,
                ComponentStockQuantity = c.StockQuantity,
                ComponentMinStockQuantity = c.MinStockQuantity,
                ComponentStockStatus = c.StockQuantity <= 0 ? "缺货" :
                                     c.StockQuantity <= c.MinStockQuantity ? "库存不足" : "库存充足",
                ComponentStockStatusColor = c.StockQuantity <= 0 ? "error" :
                                          c.StockQuantity <= c.MinStockQuantity ? "warning" : "success",
                ComponentUnitPrice = c.UnitPrice,
                ComponentDescription = c.Description,
                IsEnabled = ec.IsEnabled,
                SortOrder = ec.SortOrder,
                CreatedAt = ec.CreatedAt,
                Remark = ec.Remark
            }).ToListAsync();

            // 在内存中进行排序
            results = results.OrderBy(r => r.DepartmentName)
                           .ThenBy(r => r.EquipmentName)
                           .ThenBy(r => r.SortOrder)
                           .ThenBy(r => r.ComponentName)
                           .ToList();

            // 计算派生属性
            foreach (var result in results)
            {
                result.ReplacementCycleDescription = GetReplacementCycleDescription(result.ReplacementCycleDays);

                // 计算部件状态名称
                result.ComponentStatusName = result.ComponentStatus switch
                {
                    1 => "正常",
                    2 => "需要维护",
                    3 => "需要更换",
                    4 => "已损坏",
                    _ => "未知"
                };

                // 计算设备状态名称
                result.EquipmentStatusName = result.EquipmentStatus switch
                {
                    1 => "正常",
                    2 => "维护中",
                    3 => "故障",
                    4 => "停用",
                    _ => "未知"
                };
            }

            return results;
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentComponentAsync(int equipmentComponentId)
        {
            try
            {
                // 检查设备部件关联是否存在
                var equipmentComponent = await _dbContext.Db.Queryable<EquipmentComponent>()
                    .Where(ec => ec.Id == equipmentComponentId)
                    .FirstAsync();

                if (equipmentComponent == null)
                {
                    return (false, "设备部件关联不存在");
                }

                // 获取设备和部件信息用于日志
                var equipment = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == equipmentComponent.EquipmentId)
                    .FirstAsync();

                var component = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == equipmentComponent.ComponentId)
                    .FirstAsync();

                // 执行删除
                var result = await _dbContext.Db.Deleteable<EquipmentComponent>()
                    .Where(ec => ec.Id == equipmentComponentId)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("成功删除设备部件关联: 设备{equipmentName}, 部件{componentName}",
                        equipment?.Name ?? "未知", component?.Name ?? "未知");
                    return (true, "删除成功");
                }
                else
                {
                    return (false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备部件关联失败: {equipmentComponentId}", equipmentComponentId);
                return (false, $"删除失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int DeletedCount)> BatchDeleteEquipmentComponentsAsync(List<int> equipmentComponentIds)
        {
            try
            {
                if (!equipmentComponentIds.Any())
                {
                    return (false, "没有选择要删除的项目", 0);
                }

                // 检查所有ID是否存在
                var existingComponents = await _dbContext.Db.Queryable<EquipmentComponent>()
                    .Where(ec => equipmentComponentIds.Contains(ec.Id))
                    .ToListAsync();

                if (!existingComponents.Any())
                {
                    return (false, "没有找到要删除的设备部件关联", 0);
                }

                // 执行批量删除
                var result = await _dbContext.Db.Deleteable<EquipmentComponent>()
                    .Where(ec => equipmentComponentIds.Contains(ec.Id))
                    .ExecuteCommandAsync();

                _logger.LogInformation("批量删除设备部件关联: 请求删除{requestCount}个, 实际删除{deletedCount}个",
                    equipmentComponentIds.Count, result);

                return (true, $"成功删除 {result} 个设备部件关联", result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除设备部件关联失败");
                return (false, $"批量删除失败: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// 获取更换周期描述
        /// </summary>
        private string GetReplacementCycleDescription(int? replacementCycleDays)
        {
            if (!replacementCycleDays.HasValue)
                return "无固定周期";

            if (replacementCycleDays.Value < 30)
                return $"{replacementCycleDays.Value}天";
            else if (replacementCycleDays.Value < 365)
                return $"{replacementCycleDays.Value / 30}个月";
            else
                return $"{replacementCycleDays.Value / 365}年";
        }
    }
}
