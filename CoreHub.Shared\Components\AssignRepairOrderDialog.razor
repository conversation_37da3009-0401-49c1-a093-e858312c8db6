@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IRepairOrderService RepairOrderService

@inject IMaintenanceDashboardService MaintenanceDashboardService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudText Typo="Typo.h6" Class="mb-4">
                分配报修单
            </MudText>

            @if (RepairOrder != null)
            {
                <!-- 报修单信息 -->
                <MudCard Class="mb-4">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-2">报修单信息</MudText>
                        <MudGrid>
                            <MudItem xs="6">
                                <MudText><strong>报修单号:</strong> @RepairOrder.OrderNumber</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>设备名称:</strong> @RepairOrder.EquipmentName</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>报修人:</strong> @RepairOrder.ReporterName</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>紧急程度:</strong> 
                                    <MudChip T="string" Color="@GetUrgencyColor(RepairOrder.UrgencyLevel)" Size="Size.Small">
                                        @GetUrgencyName(RepairOrder.UrgencyLevel)
                                    </MudChip>
                                </MudText>
                            </MudItem>
                            <MudItem xs="12">
                                <MudText><strong>故障描述:</strong> @RepairOrder.FaultDescription</MudText>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>

                <!-- 技术员选择 -->
                <MudCard Class="mb-4">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-3">选择技术员</MudText>
                        
                        @if (loading)
                        {
                            <MudProgressLinear Indeterminate="true" />
                        }
                        else if (availableTechnicians.Any())
                        {
                            <MudRadioGroup T="int?" @bind-SelectedOption="selectedTechnicianId">
                                @foreach (var technician in availableTechnicians)
                                {
                                    <MudCard Class="mb-2" Style="@(selectedTechnicianId == technician.Id ? "border: 2px solid var(--mud-palette-primary);" : "")">
                                        <MudCardContent Class="pa-3">
                                            <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                                <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                                    <MudRadio T="int?" Option="technician.Id" />
                                                    <div>
                                                        <MudText Typo="Typo.subtitle2">@technician.DisplayName</MudText>
                                                        <MudStack Row Spacing="1" Class="mt-1">
                                                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                                维修人员
                                                            </MudChip>
                                                            <MudChip T="string" Color="Color.Secondary" Size="Size.Small">
                                                                @technician.Department?.Name
                                                            </MudChip>
                                                            <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                                                可用
                                                            </MudChip>
                                                        </MudStack>
                                                    </div>
                                                </MudStack>
                                                <div class="text-right">
                                                    @if (technicianWorkloads.ContainsKey(technician.Id))
                                                    {
                                                        var workload = technicianWorkloads[technician.Id];
                                                        <MudText Typo="Typo.caption">当前任务: @workload.AssignedCount</MudText>
                                                        <MudText Typo="Typo.caption">进行中: @workload.InProgressCount</MudText>
                                                    }
                                                </div>
                                            </MudStack>
                                        </MudCardContent>
                                    </MudCard>
                                }
                            </MudRadioGroup>

                            <!-- 推荐技术员提示 -->
                            @if (recommendedTechnicians.Any())
                            {
                                <MudAlert Severity="Severity.Info" Class="mt-3">
                                    <MudText Typo="Typo.body2">
                                        <MudIcon Icon="@Icons.Material.Filled.Lightbulb" Size="Size.Small" Class="mr-1" />
                                        推荐技术员: @string.Join(", ", recommendedTechnicians.Take(3).Select(t => t.DisplayName))
                                    </MudText>
                                </MudAlert>
                            }
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Warning">
                                当前没有可用的技术员
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>

                <!-- 分配备注 -->
                <MudTextField @bind-Value="assignmentComment"
                            Label="分配备注"
                            Placeholder="可选：添加分配说明或特殊要求..."
                            Lines="3"
                            Variant="Variant.Outlined"
                            Class="mb-4" />
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="ConfirmAssignment"
                 Disabled="@(selectedTechnicianId == null || assigning)">
            @if (assigning)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">分配中...</MudText>
            }
            else
            {
                <MudText>确认分配</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public RepairOrderDetailDto? RepairOrder { get; set; }
    [Parameter] public int AssignedByUserId { get; set; }

    private bool loading = true;
    private bool assigning = false;
    private int? selectedTechnicianId;
    private string assignmentComment = string.Empty;
    private List<User> availableTechnicians = new();
    private List<User> recommendedTechnicians = new();
    private Dictionary<int, TechnicianWorkloadDto> technicianWorkloads = new();

    protected override async Task OnInitializedAsync()
    {
        if (RepairOrder != null)
        {
            await LoadTechnicians();
        }
    }

    private async Task LoadTechnicians()
    {
        loading = true;
        try
        {
            if (RepairOrder == null) return;

            // 获取可用技术员
            availableTechnicians = await RepairOrderService.GetAvailableTechniciansForRepairOrderAsync(RepairOrder.Id);
            
            // 获取推荐技术员
            recommendedTechnicians = await MaintenanceDashboardService.GetRecommendedTechniciansAsync(RepairOrder.Id);
            
            // 获取技术员工作负载
            var workloadList = await MaintenanceDashboardService.GetTechnicianWorkloadAsync(RepairOrder.MaintenanceDepartmentId);
            technicianWorkloads = workloadList.ToDictionary(w => w.UserId, w => w);

            // 默认选择推荐的第一个技术员
            if (recommendedTechnicians.Any())
            {
                selectedTechnicianId = recommendedTechnicians.First().Id;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载技术员信息失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task ConfirmAssignment()
    {
        if (selectedTechnicianId == null || RepairOrder == null) return;

        assigning = true;
        try
        {
            var result = await MaintenanceDashboardService.ReassignRepairOrderAsync(
                RepairOrder.Id, 
                selectedTechnicianId.Value, 
                AssignedByUserId);

            if (result.IsSuccess)
            {
                Snackbar.Add("分配成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"分配失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"分配失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            assigning = false;
        }
    }

    private void Cancel() => MudDialog.Cancel();

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,
            2 => Color.Warning,
            3 => Color.Info,
            4 => Color.Default,
            _ => Color.Default
        };
    }

    private string GetUrgencyName(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };
    }
}
