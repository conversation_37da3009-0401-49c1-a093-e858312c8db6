using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 维修仪表板服务接口
    /// </summary>
    public interface IMaintenanceDashboardService
    {
        #region 仪表板数据查询

        /// <summary>
        /// 获取维修仪表板数据
        /// </summary>
        Task<MaintenanceDashboardData> GetDashboardDataAsync(int userId);

        /// <summary>
        /// 获取维修人员的工作负载统计
        /// </summary>
        Task<List<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync(int departmentId);

        /// <summary>
        /// 获取报修单状态统计
        /// </summary>
        Task<RepairOrderStatusStatistics> GetRepairOrderStatusStatisticsAsync(int userId);

        /// <summary>
        /// 获取紧急报修单列表
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetUrgentRepairOrdersAsync(int userId);

        #endregion

        #region 过滤和搜索

        /// <summary>
        /// 根据条件过滤报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> FilterRepairOrdersAsync(int userId, RepairOrderFilterDto filter);

        /// <summary>
        /// 搜索报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> SearchRepairOrdersAsync(int userId, string searchTerm);

        #endregion

        #region 工作流操作

        /// <summary>
        /// 批量分配报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> BatchAssignRepairOrdersAsync(List<int> repairOrderIds, int technicianId, int assignedByUserId);

        /// <summary>
        /// 重新分配报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ReassignRepairOrderAsync(int repairOrderId, int newTechnicianId, int assignedByUserId);

        /// <summary>
        /// 获取推荐的技术员
        /// </summary>
        Task<List<User>> GetRecommendedTechniciansAsync(int repairOrderId);

        /// <summary>
        /// 更新报修单优先级
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderPriorityAsync(int repairOrderId, int urgencyLevel, int updatedByUserId);

        #endregion

        #region 通知和提醒

        /// <summary>
        /// 获取需要关注的报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetAttentionRequiredRepairOrdersAsync(int userId);

        /// <summary>
        /// 获取超时报修单
        /// </summary>
        Task<List<RepairOrderDetailDto>> GetOverdueRepairOrdersAsync(int userId);

        #endregion
    }

    /// <summary>
    /// 维修仪表板数据
    /// </summary>
    public class MaintenanceDashboardData
    {
        public int TotalPending { get; set; }
        public int TotalInProgress { get; set; }
        public int TotalCompleted { get; set; }
        public int TotalOverdue { get; set; }
        public int MyAssignedCount { get; set; }
        public int UrgentCount { get; set; }
        public List<RepairOrderDetailDto> RecentRepairOrders { get; set; } = new();
        public List<TechnicianWorkloadDto> TechnicianWorkloads { get; set; } = new();
    }

    /// <summary>
    /// 技术员工作负载DTO
    /// </summary>
    public class TechnicianWorkloadDto
    {
        public int UserId { get; set; }
        public string TechnicianName { get; set; } = string.Empty;
        public int AssignedCount { get; set; }
        public int InProgressCount { get; set; }
        public int CompletedThisWeek { get; set; }
        public bool IsAvailable { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Specialties { get; set; } = string.Empty;
    }

    /// <summary>
    /// 报修单状态统计
    /// </summary>
    public class RepairOrderStatusStatistics
    {
        public int PendingCount { get; set; }
        public int InProgressCount { get; set; }
        public int CompletedCount { get; set; }
        public int CancelledCount { get; set; }
        public int ClosedCount { get; set; }
    }

    /// <summary>
    /// 报修单过滤条件DTO
    /// </summary>
    public class RepairOrderFilterDto
    {
        public int? Status { get; set; }
        public int? UrgencyLevel { get; set; }
        public int? AssignedTo { get; set; }
        public int? MaintenanceDepartmentId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsOverdue { get; set; }
        public bool? IsUnassigned { get; set; }
    }
}
