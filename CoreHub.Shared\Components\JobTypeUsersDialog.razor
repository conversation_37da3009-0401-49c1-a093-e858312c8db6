@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-2" />
                工种用户列表：@JobType?.Name
            </MudText>

            @if (loading)
            {
                <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
            }
            else if (!users.Any())
            {
                <MudAlert Severity="Severity.Info">该工种暂无分配用户</MudAlert>
            }
            else
            {
                <MudStack Spacing="3">
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <MudText Typo="Typo.subtitle1">共 @users.Count 个用户</MudText>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadUsers">
                            刷新
                        </MudButton>
                    </MudStack>

                    <MudDataGrid T="UserJobTypeDetail" 
                               Items="@users" 
                               Dense="true"
                               Hover="true"
                               Striped="true"
                               FixedHeader="true"
                               Height="500px">
                        <Columns>
                            <PropertyColumn Property="x => x.User.Username" Title="用户名" />
                            <PropertyColumn Property="x => x.User.DisplayName" Title="显示名称" />
                            <PropertyColumn Property="x => x.User.Email" Title="邮箱" />
                            <PropertyColumn Property="x => x.User.Phone" Title="电话" />
                            <TemplateColumn Title="主要工种" Sortable="false">
                                <CellTemplate>
                                    <MudChip Color="@(context.Item.UserJobType.IsPrimary ? Color.Primary : Color.Default)" 
                                           Size="Size.Small">
                                        @(context.Item.UserJobType.IsPrimary ? "是" : "否")
                                    </MudChip>
                                </CellTemplate>
                            </TemplateColumn>
                            <TemplateColumn Title="熟练程度" Sortable="false">
                                <CellTemplate>
                                    <MudRating SelectedValue="@context.Item.UserJobType.SkillLevel" 
                                             MaxValue="5" 
                                             ReadOnly="true" 
                                             Size="Size.Small" />
                                </CellTemplate>
                            </TemplateColumn>
                            <PropertyColumn Property="x => x.UserJobType.AcquiredAt" Title="获得时间" Format="yyyy-MM-dd" />
                            <PropertyColumn Property="x => x.UserJobType.Remark" Title="备注" />
                        </Columns>
                    </MudDataGrid>
                </MudStack>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public JobType? JobType { get; set; }

    private List<UserJobTypeDetail> users = new();
    private bool loading = false;

    // 用户工种详情类
    public class UserJobTypeDetail
    {
        public UserJobType UserJobType { get; set; } = null!;
        public User User { get; set; } = null!;
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        if (JobType == null) return;

        loading = true;
        try
        {
            var userJobTypesWithDetails = await JobTypeService.GetJobTypeUsersWithDetailsAsync(JobType.Id);
            users = userJobTypesWithDetails.Select(x => new UserJobTypeDetail
            {
                UserJobType = x.UserJobType,
                User = x.User
            }).OrderBy(x => x.User.DisplayName).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户列表失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    void Cancel() => MudDialog.Cancel();
}
