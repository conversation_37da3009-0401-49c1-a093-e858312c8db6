@page "/department-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>部门管理</PageTitle>

@* <div> *@
@* <MudContainer> *@
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Class="mr-2" />
                    部门管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索部门名称或编码..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadDepartments">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增部门
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="Department" 
                           Items="@filteredDepartments" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.Code" Title="部门编码" />
                        <PropertyColumn Property="x => x.Name" Title="部门名称" />
                        <TemplateColumn Title="部门类型" Sortable="false">
                            <CellTemplate>
                                @if (context.Item.DepartmentType != null)
                                {
                                    <MudChip Color="@GetDepartmentTypeColor(context.Item.DepartmentType.Code)"
                                           Size="Size.Small">
                                        @context.Item.DepartmentType.Name
                                    </MudChip>
                                }
                                else
                                {
                                    <MudText Typo="Typo.body2" Class="text-muted">未设置</MudText>
                                }
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.Description" Title="描述" />
                        <PropertyColumn Property="x => x.Level" Title="级别" />
                        <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteDepartment(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
@* </div> *@
@* </MudContainer> *@

@code {
    private List<Department> departments = new();
    private List<Department> filteredDepartments = new();
    private bool loading = false;
    private string searchText = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
    }

    private async Task LoadDepartments()
    {
        loading = true;
        try
        {
            departments = await DepartmentService.GetAllDepartmentsAsync();

            // 加载部门类型信息
            var departmentTypes = await DepartmentTypeService.GetAllDepartmentTypesAsync();
            foreach (var dept in departments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = departmentTypes.FirstOrDefault(dt => dt.Id == dept.DepartmentTypeId.Value);
                }
            }

            FilterDepartments();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private void FilterDepartments()
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            filteredDepartments = departments.ToList();
        }
        else
        {
            filteredDepartments = departments.Where(d => 
                d.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                d.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (d.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();
        }
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterDepartments();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<DepartmentEditDialog>
        {
            { x => x.Department, new Department() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<DepartmentEditDialog>("新增部门", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadDepartments();
        }
    }

    private async Task OpenEditDialog(Department department)
    {
        var parameters = new DialogParameters<DepartmentEditDialog>
        {
            { x => x.Department, department },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<DepartmentEditDialog>("编辑部门", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadDepartments();
        }
    }

    private async Task ToggleStatus(Department department)
    {
        try
        {
            var result = await DepartmentService.ToggleStatusAsync(department.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"部门状态已{(department.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadDepartments();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteDepartment(Department department)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除部门 '{department.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await DepartmentService.DeleteDepartmentAsync(department.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("部门删除成功", Severity.Success);
                    await LoadDepartments();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private Color GetDepartmentTypeColor(string typeCode)
    {
        return typeCode switch
        {
            DepartmentTypeCodes.Production => Color.Primary,
            DepartmentTypeCodes.Maintenance => Color.Warning,
            DepartmentTypeCodes.Management => Color.Info,
            DepartmentTypeCodes.Support => Color.Success,
            _ => Color.Default
        };
    }
}
