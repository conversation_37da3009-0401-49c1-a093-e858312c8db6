using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部门类型服务接口
    /// </summary>
    public interface IDepartmentTypeService
    {
        /// <summary>
        /// 获取所有启用的部门类型
        /// </summary>
        Task<List<DepartmentType>> GetEnabledDepartmentTypesAsync();

        /// <summary>
        /// 获取所有部门类型
        /// </summary>
        Task<List<DepartmentType>> GetAllDepartmentTypesAsync();

        /// <summary>
        /// 根据ID获取部门类型
        /// </summary>
        Task<DepartmentType?> GetDepartmentTypeByIdAsync(int id);

        /// <summary>
        /// 根据编码获取部门类型
        /// </summary>
        Task<DepartmentType?> GetDepartmentTypeByCodeAsync(string code);

        /// <summary>
        /// 创建部门类型
        /// </summary>
        Task<bool> CreateDepartmentTypeAsync(DepartmentType departmentType);

        /// <summary>
        /// 更新部门类型
        /// </summary>
        Task<bool> UpdateDepartmentTypeAsync(DepartmentType departmentType);

        /// <summary>
        /// 删除部门类型
        /// </summary>
        Task<bool> DeleteDepartmentTypeAsync(int id);

        /// <summary>
        /// 检查部门类型编码是否存在
        /// </summary>
        Task<bool> ExistsByCodeAsync(string code, int? excludeId = null);

        /// <summary>
        /// 获取维修类型的部门
        /// </summary>
        Task<List<Department>> GetMaintenanceDepartmentsAsync();

        /// <summary>
        /// 获取生产类型的部门
        /// </summary>
        Task<List<Department>> GetProductionDepartmentsAsync();

        /// <summary>
        /// 根据部门类型获取部门列表
        /// </summary>
        Task<List<Department>> GetDepartmentsByTypeAsync(string departmentTypeCode);
    }
}
