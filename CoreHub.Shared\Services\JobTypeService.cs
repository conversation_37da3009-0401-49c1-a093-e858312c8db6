using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 工种类型服务实现
    /// </summary>
    public class JobTypeService : IJobTypeService
    {
        private readonly DatabaseContext _context;

        public JobTypeService(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<List<JobType>> GetEnabledJobTypesAsync()
        {
            return await _context.Db.Queryable<JobType>()
                .Where(jt => jt.IsEnabled)
                .OrderBy(jt => jt.SortOrder)
                .ToListAsync();
        }

        public async Task<List<JobType>> GetAllJobTypesAsync()
        {
            return await _context.Db.Queryable<JobType>()
                .ToListAsync();
        }

        public async Task<JobType?> GetJobTypeByIdAsync(int id)
        {
            return await _context.Db.Queryable<JobType>()
                .Where(jt => jt.Id == id)
                .FirstAsync();
        }

        public async Task<JobType?> GetJobTypeByCodeAsync(string code)
        {
            return await _context.Db.Queryable<JobType>()
                .Where(jt => jt.Code == code)
                .FirstAsync();
        }

        public async Task<List<JobType>> GetJobTypesByCategoryAsync(string category)
        {
            return await _context.Db.Queryable<JobType>()
                .Where(jt => jt.Category == category && jt.IsEnabled)
                .ToListAsync();
        }

        public async Task<List<JobType>> GetMaintenanceJobTypesAsync()
        {
            return await GetJobTypesByCategoryAsync(JobCategories.Maintenance);
        }

        public async Task<List<JobType>> GetProductionJobTypesAsync()
        {
            return await GetJobTypesByCategoryAsync(JobCategories.Production);
        }

        public async Task<bool> CreateJobTypeAsync(JobType jobType)
        {
            jobType.CreatedAt = DateTime.Now;
            var result = await _context.Db.Insertable(jobType).ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> UpdateJobTypeAsync(JobType jobType)
        {
            jobType.UpdatedAt = DateTime.Now;
            var result = await _context.Db.Updateable(jobType).ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> DeleteJobTypeAsync(int id)
        {
            var result = await _context.Db.Deleteable<JobType>()
                .Where(jt => jt.Id == id)
                .ExecuteCommandAsync();
            return result > 0;
        }

        public async Task<bool> ExistsByCodeAsync(string code, int? excludeId = null)
        {
            var query = _context.Db.Queryable<JobType>()
                .Where(jt => jt.Code == code);

            if (excludeId.HasValue)
            {
                query = query.Where(jt => jt.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据用户所属部门获取维修人员列表
        /// 注意：这里使用的是用户的所属部门(User.DepartmentId)，表示维修人员来自哪个部门
        /// 与角色权限部门(RoleDepartmentAssignments)不同，后者表示可以访问哪些部门的数据
        /// </summary>
        /// <param name="departmentId">维修部门ID（如工程部、动力部）</param>
        /// <returns>该部门下具有维修工种的用户列表</returns>
        public async Task<List<User>> GetMaintenanceUsersByDepartmentAsync(int departmentId)
        {
            try
            {
                // 获取维修工种
                var maintenanceJobTypes = await GetJobTypesByCategoryAsync(JobCategories.Maintenance);
                var maintenanceJobTypeIds = maintenanceJobTypes.Select(jt => jt.Id).ToList();

                if (!maintenanceJobTypeIds.Any())
                    return new List<User>();

                // 获取拥有维修工种的用户工种关系
                var userJobTypes = await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => maintenanceJobTypeIds.Contains(ujt.JobTypeId) && ujt.IsEnabled)
                    .ToListAsync();

                if (!userJobTypes.Any())
                    return new List<User>();

                // 获取维修工种用户的ID列表
                var maintenanceUserIds = userJobTypes.Select(ujt => ujt.UserId).Distinct().ToList();

                // 根据用户所属部门筛选维修人员
                // 例如：获取工程部的维修人员，用于处理分配给工程部的维修任务
                var users = await _context.Db.Queryable<User>()
                    .Where(u => maintenanceUserIds.Contains(u.Id) &&
                               u.DepartmentId == departmentId &&  // 用户所属部门
                               u.IsEnabled)
                    .ToListAsync();

                return users;
            }
            catch
            {
                return new List<User>();
            }
        }

        public async Task<bool> IsMaintenanceJobAsync(int userId)
        {
            try
            {
                // 基于多对多关系检查用户是否拥有维修工种
                var maintenanceJobTypes = await GetJobTypesByCategoryAsync(JobCategories.Maintenance);
                var maintenanceJobTypeIds = maintenanceJobTypes.Select(jt => jt.Id).ToList();

                if (!maintenanceJobTypeIds.Any())
                    return false;

                return await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => ujt.UserId == userId &&
                                 maintenanceJobTypeIds.Contains(ujt.JobTypeId) &&
                                 ujt.IsEnabled)
                    .AnyAsync();
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<User>> GetUsersByJobTypeAsync(string jobTypeCode)
        {
            try
            {
                // 基于多对多关系查询
                var jobType = await _context.Db.Queryable<JobType>()
                    .Where(jt => jt.Code == jobTypeCode)
                    .FirstAsync();

                if (jobType != null)
                {
                    var userJobTypesDetails = await GetJobTypeUsersWithDetailsAsync(jobType.Id);
                    return userJobTypesDetails.Select(x => x.User).ToList();
                }

                return new List<User>();
            }
            catch
            {
                return new List<User>();
            }
        }

        // 用户工种多对多关系管理
        public async Task<bool> AssignJobTypeToUserAsync(int userId, int jobTypeId, bool isPrimary = false, int skillLevel = 1, string? remark = null)
        {
            try
            {
                // 检查是否已存在
                var exists = await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => ujt.UserId == userId && ujt.JobTypeId == jobTypeId)
                    .AnyAsync();

                if (exists)
                {
                    return false; // 已存在关系
                }

                // 如果设置为主要工种，先取消其他主要工种
                if (isPrimary)
                {
                    await _context.Db.Updateable<UserJobType>()
                        .SetColumns(ujt => ujt.IsPrimary == false)
                        .Where(ujt => ujt.UserId == userId && ujt.IsPrimary)
                        .ExecuteCommandAsync();
                }

                var userJobType = new UserJobType
                {
                    UserId = userId,
                    JobTypeId = jobTypeId,
                    IsPrimary = isPrimary,
                    SkillLevel = skillLevel,
                    Remark = remark,
                    AcquiredAt = DateTime.Now,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now
                };

                var result = await _context.Db.Insertable(userJobType).ExecuteCommandAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RemoveJobTypeFromUserAsync(int userId, int jobTypeId)
        {
            try
            {
                var result = await _context.Db.Deleteable<UserJobType>()
                    .Where(ujt => ujt.UserId == userId && ujt.JobTypeId == jobTypeId)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<UserJobType>> GetUserJobTypesAsync(int userId)
        {
            try
            {
                return await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => ujt.UserId == userId && ujt.IsEnabled)
                    .ToListAsync();
            }
            catch
            {
                // 如果表不存在或其他错误，返回空列表
                return new List<UserJobType>();
            }
        }

        public async Task<List<UserJobType>> GetJobTypeUsersAsync(int jobTypeId)
        {
            try
            {
                return await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => ujt.JobTypeId == jobTypeId && ujt.IsEnabled)
                    .ToListAsync();
            }
            catch
            {
                // 如果表不存在或其他错误，返回空列表
                return new List<UserJobType>();
            }
        }

        public async Task<bool> UpdateUserJobTypeAsync(UserJobType userJobType)
        {
            try
            {
                userJobType.UpdatedAt = DateTime.Now;

                // 如果设置为主要工种，先取消其他主要工种
                if (userJobType.IsPrimary)
                {
                    await _context.Db.Updateable<UserJobType>()
                        .SetColumns(ujt => ujt.IsPrimary == false)
                        .Where(ujt => ujt.UserId == userJobType.UserId && ujt.IsPrimary && ujt.Id != userJobType.Id)
                        .ExecuteCommandAsync();
                }

                var result = await _context.Db.Updateable(userJobType).ExecuteCommandAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> BatchAssignJobTypesToUserAsync(int userId, List<int> jobTypeIds, int primaryJobTypeId)
        {
            try
            {
                // 开始事务
                await _context.Db.Ado.BeginTranAsync();

                // 删除现有的工种关系
                await _context.Db.Deleteable<UserJobType>()
                    .Where(ujt => ujt.UserId == userId)
                    .ExecuteCommandAsync();

                // 添加新的工种关系
                var userJobTypes = jobTypeIds.Select(jobTypeId => new UserJobType
                {
                    UserId = userId,
                    JobTypeId = jobTypeId,
                    IsPrimary = jobTypeId == primaryJobTypeId,
                    SkillLevel = 1,
                    AcquiredAt = DateTime.Now,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now
                }).ToList();

                await _context.Db.Insertable(userJobTypes).ExecuteCommandAsync();

                // 提交事务
                await _context.Db.Ado.CommitTranAsync();
                return true;
            }
            catch
            {
                await _context.Db.Ado.RollbackTranAsync();
                return false;
            }
        }

        public async Task<int> GetJobTypeUserCountAsync(int jobTypeId)
        {
            try
            {
                // 先获取启用的用户工种关系
                var userJobTypes = await _context.Db.Queryable<UserJobType>()
                    .Where(ujt => ujt.JobTypeId == jobTypeId && ujt.IsEnabled)
                    .ToListAsync();

                if (!userJobTypes.Any())
                    return 0;

                // 检查这些用户是否启用
                var userIds = userJobTypes.Select(ujt => ujt.UserId).Distinct().ToList();
                var enabledUserCount = await _context.Db.Queryable<User>()
                    .Where(u => userIds.Contains(u.Id) && u.IsEnabled)
                    .CountAsync();

                return enabledUserCount;
            }
            catch
            {
                // 如果表不存在或其他错误，返回0
                return 0;
            }
        }

        public async Task<bool> UserHasJobTypeAsync(int userId, int jobTypeId)
        {
            return await _context.Db.Queryable<UserJobType>()
                .Where(ujt => ujt.UserId == userId && ujt.JobTypeId == jobTypeId && ujt.IsEnabled)
                .AnyAsync();
        }

        // 获取用户工种的详细信息（包含工种名称等）
        public async Task<List<(UserJobType UserJobType, JobType JobType)>> GetUserJobTypesWithDetailsAsync(int userId)
        {
            try
            {
                var userJobTypes = await GetUserJobTypesAsync(userId);
                if (!userJobTypes.Any())
                    return new List<(UserJobType, JobType)>();

                var jobTypeIds = userJobTypes.Select(ujt => ujt.JobTypeId).Distinct().ToList();
                var jobTypes = await _context.Db.Queryable<JobType>()
                    .Where(jt => jobTypeIds.Contains(jt.Id))
                    .ToListAsync();

                return userJobTypes.Select(ujt =>
                    (ujt, jobTypes.FirstOrDefault(jt => jt.Id == ujt.JobTypeId)!)
                ).Where(x => x.Item2 != null).ToList();
            }
            catch
            {
                return new List<(UserJobType, JobType)>();
            }
        }

        // 获取工种用户的详细信息（包含用户名称等）
        public async Task<List<(UserJobType UserJobType, User User)>> GetJobTypeUsersWithDetailsAsync(int jobTypeId)
        {
            try
            {
                var userJobTypes = await GetJobTypeUsersAsync(jobTypeId);
                if (!userJobTypes.Any())
                    return new List<(UserJobType, User)>();

                var userIds = userJobTypes.Select(ujt => ujt.UserId).Distinct().ToList();
                var users = await _context.Db.Queryable<User>()
                    .Where(u => userIds.Contains(u.Id) && u.IsEnabled)
                    .ToListAsync();

                return userJobTypes.Select(ujt =>
                    (ujt, users.FirstOrDefault(u => u.Id == ujt.UserId)!)
                ).Where(x => x.Item2 != null).ToList();
            }
            catch
            {
                return new List<(UserJobType, User)>();
            }
        }
    }
}
