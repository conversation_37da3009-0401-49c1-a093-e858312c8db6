@page "/app-update-management"
@using CoreHub.Shared.Models.AppUpdate
@using CoreHub.Shared.Services
@inject IAppUpdateService UpdateService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>应用更新管理</PageTitle>

<div>
    <MudText Typo="Typo.h4" Class="mb-4">
        <MudIcon Icon="@Icons.Material.Filled.SystemUpdate" Class="mr-2" />
        应用更新管理
    </MudText>

    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">版本列表</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          OnClick="OpenCreateDialog">
                    创建新版本
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <!-- 筛选器 -->
            <MudGrid Class="mb-4">
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect T="string" 
                              Label="平台" 
                              @bind-Value="selectedPlatform" 
                              Clearable="true"
                              OnClearButtonClick="@(() => { selectedPlatform = null; LoadVersions(); })">
                        <MudSelectItem Value="@("Android")">Android</MudSelectItem>
                        <MudSelectItem Value="@("iOS")">iOS</MudSelectItem>
                        <MudSelectItem Value="@("Windows")">Windows</MudSelectItem>
                        <MudSelectItem Value="@("MacOS")">MacOS</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudButton Variant="Variant.Outlined" 
                              Color="Color.Primary" 
                              OnClick="LoadVersions"
                              StartIcon="@Icons.Material.Filled.Search">
                        查询
                    </MudButton>
                </MudItem>
            </MudGrid>

            <!-- 版本列表 -->
            <MudTable Items="@versions" 
                     Hover="true" 
                     Striped="true" 
                     Loading="@loading"
                     LoadingProgressColor="Color.Info">
                <HeaderContent>
                    <MudTh>平台</MudTh>
                    <MudTh>版本号</MudTh>
                    <MudTh>版本代码</MudTh>
                    <MudTh>标题</MudTh>
                    <MudTh>更新类型</MudTh>
                    <MudTh>状态</MudTh>
                    <MudTh>强制更新</MudTh>
                    <MudTh>文件大小</MudTh>
                    <MudTh>发布时间</MudTh>
                    <MudTh>操作</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="平台">
                        <MudChip T="string" Color="GetPlatformColor(context.Platform)" Size="Size.Small">
                            @context.Platform
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="版本号">@context.VersionNumber</MudTd>
                    <MudTd DataLabel="版本代码">@context.VersionCode</MudTd>
                    <MudTd DataLabel="标题">@context.Title</MudTd>
                    <MudTd DataLabel="更新类型">
                        <MudChip T="string" Color="GetUpdateTypeColor(context.UpdateType)" Size="Size.Small">
                            @GetUpdateTypeText(context.UpdateType)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="状态">
                        <MudChip T="string" Color="GetStatusColor(context.Status)" Size="Size.Small">
                            @GetStatusText(context.Status)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="强制更新">
                        @if (context.IsForceUpdate)
                        {
                            <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" />
                        }
                        else
                        {
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                        }
                    </MudTd>
                    <MudTd DataLabel="文件大小">@FormatFileSize(context.FileSize)</MudTd>
                    <MudTd DataLabel="发布时间">
                        @(context.ReleaseTime?.ToString("yyyy-MM-dd HH:mm") ?? "-")
                    </MudTd>
                    <MudTd DataLabel="操作">
                        <MudStack Row Spacing="1">
                            @if (context.Status == "Draft")
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Publish"
                                              Color="Color.Success"
                                              Size="Size.Small"
                                              Title="发布"
                                              OnClick="@(() => PublishVersion(context.Id))" />
                            }
                            else if (context.Status == "Released")
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Unpublished"
                                              Color="Color.Warning"
                                              Size="Size.Small"
                                              Title="撤回"
                                              OnClick="@(() => WithdrawVersion(context.Id))" />
                            }
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Title="编辑"
                                          OnClick="@(() => OpenEditDialog(context))" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          Title="删除"
                                          OnClick="@(() => DeleteVersion(context.Id))" />
                        </MudStack>
                    </MudTd>
                </RowTemplate>
                <NoRecordsContent>
                    <MudText>暂无版本数据</MudText>
                </NoRecordsContent>
                <LoadingContent>
                    <MudText>加载中...</MudText>
                </LoadingContent>
            </MudTable>

            <!-- 分页 -->
            <MudPagination Count="totalPages"
                          Selected="currentPage"
                          SelectedChanged="OnPageChanged"
                          Class="mt-4" />
        </MudCardContent>
    </MudCard>
</div>

@code {
    private List<AppVersion> versions = new();
    private bool loading = false;
    private string? selectedPlatform;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadVersions();
    }

    private async Task LoadVersions()
    {
        loading = true;
        try
        {
            var (versionList, totalCount) = await UpdateService.GetVersionsAsync(selectedPlatform, currentPage, pageSize);
            versions = versionList;
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载版本列表失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadVersions();
    }

    private async Task OpenCreateDialog()
    {
        var dialog = await DialogService.ShowAsync<CreateVersionDialog>("创建新版本");
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadVersions();
        }
    }

    private async Task OpenEditDialog(AppVersion version)
    {
        var parameters = new DialogParameters { ["Version"] = version };
        var dialog = await DialogService.ShowAsync<EditVersionDialog>("编辑版本", parameters);
        var result = await dialog.Result;
        
        if (!result.Canceled)
        {
            await LoadVersions();
        }
    }

    private async Task PublishVersion(int versionId)
    {
        var confirm = await DialogService.ShowMessageBox(
            "确认发布",
            "确定要发布此版本吗？发布后用户将能够检查到此更新。",
            yesText: "发布", cancelText: "取消");

        if (confirm == true)
        {
            try
            {
                var (isSuccess, errorMessage) = await UpdateService.PublishVersionAsync(versionId, 1); // 假设当前用户ID为1
                if (isSuccess)
                {
                    Snackbar.Add("版本发布成功", Severity.Success);
                    await LoadVersions();
                }
                else
                {
                    Snackbar.Add($"发布失败: {errorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"发布失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task WithdrawVersion(int versionId)
    {
        var confirm = await DialogService.ShowMessageBox(
            "确认撤回",
            "确定要撤回此版本吗？撤回后用户将无法检查到此更新。",
            yesText: "撤回", cancelText: "取消");

        if (confirm == true)
        {
            try
            {
                var (isSuccess, errorMessage) = await UpdateService.WithdrawVersionAsync(versionId, 1); // 假设当前用户ID为1
                if (isSuccess)
                {
                    Snackbar.Add("版本撤回成功", Severity.Success);
                    await LoadVersions();
                }
                else
                {
                    Snackbar.Add($"撤回失败: {errorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"撤回失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task DeleteVersion(int versionId)
    {
        var confirm = await DialogService.ShowMessageBox(
            "确认删除",
            "确定要删除此版本吗？此操作不可恢复。",
            yesText: "删除", cancelText: "取消");

        if (confirm == true)
        {
            try
            {
                var (isSuccess, errorMessage) = await UpdateService.DeleteVersionAsync(versionId);
                if (isSuccess)
                {
                    Snackbar.Add("版本删除成功", Severity.Success);
                    await LoadVersions();
                }
                else
                {
                    Snackbar.Add($"删除失败: {errorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private Color GetPlatformColor(string platform) => platform switch
    {
        "Android" => Color.Success,
        "iOS" => Color.Info,
        "Windows" => Color.Primary,
        "MacOS" => Color.Secondary,
        _ => Color.Default
    };

    private Color GetUpdateTypeColor(string updateType) => updateType switch
    {
        "Major" => Color.Error,
        "Minor" => Color.Warning,
        "Patch" => Color.Info,
        "Hotfix" => Color.Success,
        _ => Color.Default
    };

    private string GetUpdateTypeText(string updateType) => updateType switch
    {
        "Major" => "重大更新",
        "Minor" => "功能更新",
        "Patch" => "修复更新",
        "Hotfix" => "紧急修复",
        _ => updateType
    };

    private Color GetStatusColor(string status) => status switch
    {
        "Draft" => Color.Default,
        "Testing" => Color.Warning,
        "Released" => Color.Success,
        "Archived" => Color.Secondary,
        _ => Color.Default
    };

    private string GetStatusText(string status) => status switch
    {
        "Draft" => "草稿",
        "Testing" => "测试",
        "Released" => "已发布",
        "Archived" => "已归档",
        _ => status
    };

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}
