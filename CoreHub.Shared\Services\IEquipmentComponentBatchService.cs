using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件批量管理服务接口
    /// </summary>
    public interface IEquipmentComponentBatchService
    {
        /// <summary>
        /// 从设备型号模板批量应用部件到设备
        /// </summary>
        /// <param name="request">批量应用请求</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResultDto> ApplyTemplateToEquipmentsAsync(ApplyTemplateToEquipmentsRequestDto request);

        /// <summary>
        /// 从源设备复制部件配置到目标设备
        /// </summary>
        /// <param name="request">复制请求</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResultDto> CopyComponentsFromEquipmentAsync(CopyComponentsFromEquipmentRequestDto request);

        /// <summary>
        /// 批量为设备添加指定部件
        /// </summary>
        /// <param name="request">批量添加请求</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResultDto> BatchAddComponentsToEquipmentsAsync(BatchAddComponentsRequestDto request);

        /// <summary>
        /// 批量移除设备的指定部件
        /// </summary>
        /// <param name="request">批量移除请求</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResultDto> BatchRemoveComponentsFromEquipmentsAsync(BatchRemoveComponentsRequestDto request);

        /// <summary>
        /// 批量更新设备部件配置
        /// </summary>
        /// <param name="request">批量更新请求</param>
        /// <returns>操作结果</returns>
        Task<BatchOperationResultDto> BatchUpdateEquipmentComponentsAsync(BatchUpdateComponentsRequestDto request);

        /// <summary>
        /// 获取设备的部件配置预览
        /// </summary>
        /// <param name="equipmentIds">设备ID列表</param>
        /// <returns>部件配置预览</returns>
        Task<List<EquipmentComponentPreviewDto>> GetEquipmentComponentsPreviewAsync(List<int> equipmentIds);

        /// <summary>
        /// 获取可用的模板选项
        /// </summary>
        /// <param name="equipmentIds">设备ID列表</param>
        /// <returns>可用模板列表</returns>
        Task<List<TemplateOptionDto>> GetAvailableTemplatesAsync(List<int> equipmentIds);

        /// <summary>
        /// 获取可复制的设备选项
        /// </summary>
        /// <param name="targetEquipmentIds">目标设备ID列表</param>
        /// <returns>可复制的设备列表</returns>
        Task<List<EquipmentOptionDto>> GetCopyableEquipmentsAsync(List<int> targetEquipmentIds);

        /// <summary>
        /// 验证批量操作的可行性
        /// </summary>
        /// <param name="request">操作请求</param>
        /// <returns>验证结果</returns>
        Task<BatchValidationResultDto> ValidateBatchOperationAsync(object request);

        /// <summary>
        /// 获取批量操作的影响预览
        /// </summary>
        /// <param name="request">操作请求</param>
        /// <returns>影响预览</returns>
        Task<BatchImpactPreviewDto> GetBatchOperationImpactAsync(object request);

        /// <summary>
        /// 获取所有可用的设备型号模板
        /// </summary>
        /// <returns>可用模板列表</returns>
        Task<List<TemplateOptionDto>> GetAllAvailableTemplatesAsync();
    }
}
