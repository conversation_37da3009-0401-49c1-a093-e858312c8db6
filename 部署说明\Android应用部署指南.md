# CoreHub Android应用部署指南

## 概述

本指南说明如何在Windows平台上构建和部署CoreHub Android应用。

## 系统要求

### 开发环境
- Windows 10/11
- Visual Studio 2022
- .NET 8.0 SDK
- Android SDK

### 目标设备
- Android 7.0+ (API Level 24)
- 至少2GB RAM
- 至少100MB存储空间
- 网络连接

### 应用信息
- **应用名称**: CoreHub
- **包名**: com.saintyeartex.corehub
- **当前版本**: 1.0.11 (Build 12)
- **支持架构**: ARM64, ARM32, x86, x64

## 环境准备

### 1. 安装Visual Studio 2022
1. 下载Visual Studio 2022 Community (免费版)
2. 安装时选择工作负载：**.NET Multi-platform App UI development**
3. 确保包含Android SDK和模拟器组件

### 2. 验证安装
打开命令提示符，检查以下工具：
```cmd
dotnet --version
adb version
java -version
```

## 项目配置

### 1. 环境配置
应用使用统一的环境配置系统 `CoreHub.Shared.Configuration.EnvironmentConfig`，在编译时确定环境配置。

#### 编译时环境配置
Android应用的环境配置在编译时确定，根据构建配置自动选择：

**Debug构建 (开发环境)**:
- 自动使用 Development 环境配置
- API地址: 开发环境API服务器
- 数据库: 开发环境数据库连接
- 启用详细日志

**Release构建 (生产环境)**:
- 自动使用 Production 环境配置  
- API地址: 生产环境API服务器
- 数据库: 生产环境数据库连接
- 启用HTTPS重定向，精简日志

#### 自定义环境配置
如需使用不同的环境配置，可以修改 `EnvironmentConfig.cs` 中的默认值，或在构建时通过编译器指令控制：

```csharp
// 在 EnvironmentConfig.cs 中
private static string GetCurrentEnvironment()
{
#if DEBUG
    return Environments.Development;  // Debug版本使用开发环境
#elif STAGING
    return Environments.Staging;      // 可选的测试环境
#else
    return Environments.Production;   // Release版本使用生产环境
#endif
}
```

### 2. Android权限配置
应用包含以下权限（已在AndroidManifest.xml中配置）：
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

## 构建应用

### 1. Debug构建 (开发测试)

#### 使用Visual Studio
1. 在Visual Studio中打开解决方案
2. 设置启动项目为 `CoreHub.Maui`
3. 选择目标框架：`net8.0-android`
4. 选择配置：`Debug`
5. 选择目标设备 (模拟器或连接的设备)
6. 点击"开始调试"或按F5

#### 使用命令行
```cmd
cd CoreHub.Maui
dotnet build -f net8.0-android -c Debug
```

### 2. Release构建 (生产部署)

#### 快速构建 (使用自动签名)
```cmd
cd CoreHub.Maui
dotnet clean CoreHub.csproj -c Release -f net8.0-android
dotnet publish CoreHub.csproj -f net8.0-android -c Release -p:AndroidPackageFormat=apk
```

**说明**: 此命令会自动生成调试签名的APK，适用于内部测试和分发。

生成的APK文件位置：
- 主要文件：`CoreHub.Maui\bin\Release\net8.0-android\com.saintyeartex.corehub-Signed.apk`
- 备用文件：`CoreHub.Maui\bin\Release\net8.0-android\publish\`

#### 正式签名 (可选，用于应用商店发布)

如需发布到Google Play Store或需要正式签名，可以配置自定义签名：

1. **生成正式签名证书**：
```cmd
keytool -genkey -v -keystore corehub-release-key.keystore -name corehub -keyalg RSA -keysize 2048 -validity 10000
```

2. **配置项目签名**：
编辑 `CoreHub.Maui\CoreHub.Maui.csproj`，添加签名配置：
```xml
<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0-android|AnyCPU'">
  <AndroidKeyStore>True</AndroidKeyStore>
  <AndroidSigningKeyStore>corehub-release-key.keystore</AndroidSigningKeyStore>
  <AndroidSigningKeyAlias>corehub</AndroidSigningKeyAlias>
  <AndroidSigningKeyPass>你的密钥密码</AndroidSigningKeyPass>
  <AndroidSigningStorePass>你的密钥库密码</AndroidSigningStorePass>
</PropertyGroup>
```

3. **构建正式签名APK**：
```cmd
dotnet publish CoreHub.csproj -f net8.0-android -c Release -p:AndroidPackageFormat=apk
```

## 应用分发

### 1. 测试安装
使用ADB工具安装到连接的Android设备：
```cmd
adb install "CoreHub.Maui\bin\Release\net8.0-android\com.saintyeartex.corehub-Signed.apk"
```

### 2. 内部分发
1. 将APK文件复制到Web服务器的更新目录：
   ```
   CoreHub.Web\wwwroot\updates\android\
   ```
2. 在管理后台添加版本信息：
   - 版本号：1.0.11
   - 版本代码：12
   - 文件路径：/updates/android/com.saintyeartex.corehub-Signed.apk

### 3. 不同环境的APK构建

#### 开发环境APK (Debug)
```cmd
cd CoreHub.Maui
dotnet build -f net8.0-android -c Debug
# 自动使用开发环境配置:
# - 连接开发环境API和数据库
# - 启用详细日志和调试功能
```

#### 生产环境APK (Release)
```cmd
cd CoreHub.Maui
dotnet publish -f net8.0-android -c Release -p:AndroidPackageFormat=apk
# 自动使用生产环境配置:
# - 连接生产环境API和数据库
# - 优化性能，精简日志
```

#### 测试环境APK (可选)
如需构建测试环境版本，需要在项目中添加 STAGING 编译条件：
```xml
<!-- 在 CoreHub.Maui.csproj 中添加 -->
<PropertyGroup Condition="'$(Configuration)'=='Staging'">
  <DefineConstants>STAGING</DefineConstants>
</PropertyGroup>
```

然后构建：
```cmd
dotnet publish -f net8.0-android -c Staging -p:AndroidPackageFormat=apk
```

### 4. 设备安装
1. 将APK文件传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件开始安装
4. 按照提示完成安装

## 签名说明

### 自动调试签名
- `dotnet publish` 命令默认使用调试签名
- 生成的APK文件名包含 `-Signed` 后缀
- 适用于内部测试和企业分发
- 无需额外配置，开箱即用

### 正式签名 vs 调试签名
| 特性 | 调试签名 | 正式签名 |
|------|----------|----------|
| 用途 | 内部测试、企业分发 | 应用商店发布 |
| 配置 | 自动生成 | 需要手动配置 |
| 安全性 | 较低 | 高 |
| 有效期 | 1年 | 可自定义 |

## 常见问题

### 构建问题
1. **Android SDK未找到**: 检查环境变量ANDROID_HOME设置
2. **构建失败**: 清理项目后重新构建
3. **APK未生成**: 检查目标框架和配置是否正确

### 安装问题
1. **安装失败**: 检查设备设置和APK签名
2. **权限被拒绝**: 在设备设置中启用"未知来源"
3. **应用崩溃**: 检查网络连接和服务器配置
4. **签名冲突**: 卸载旧版本后重新安装

## 维护更新

### 版本更新流程
1. 修改项目文件中的版本号：
   - `ApplicationDisplayVersion`: 显示版本号 (如 1.0.12)
   - `ApplicationVersion`: 版本代码 (如 13)
2. 重新构建Release版本
3. 上传APK到更新服务器
4. 在管理后台添加新版本记录

### 自动更新系统
应用内置自动更新功能：
- 启动时检查版本更新
- 支持可选更新和强制更新
- 自动下载和安装新版本

### 构建优化
当前构建配置已优化：
- ✅ AOT编译 (提升性能)
- ✅ 代码裁剪 (减小包大小)
- ✅ R8代码优化
- ✅ ProfiledAOT (快速启动)

---

本指南基于CoreHub v1.0.11 (Build 12) 的成功发布经验编写，提供了完整的Android应用部署流程。