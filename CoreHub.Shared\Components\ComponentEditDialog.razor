@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@component" Validation="@(new ComponentValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="component.Code"
                                For="@(() => component.Code)"
                                Label="部件编码"
                                Placeholder="请输入部件编码"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="component.Name"
                                For="@(() => component.Name)"
                                Label="部件名称"
                                Placeholder="请输入部件名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="component.CategoryId"
                             For="@(() => component.CategoryId)"
                             Label="部件分类"
                             Required="true">
                        @foreach (var category in Categories)
                        {
                            <MudSelectItem T="int" Value="@category.Id">@category.FullName</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="component.Model"
                                For="@(() => component.Model)"
                                Label="型号规格"
                                Placeholder="请输入型号规格" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="component.Brand"
                                For="@(() => component.Brand)"
                                Label="品牌"
                                Placeholder="请输入品牌" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="component.Supplier"
                                For="@(() => component.Supplier)"
                                Label="供应商"
                                Placeholder="请输入供应商" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="component.Unit"
                                For="@(() => component.Unit)"
                                Label="单位"
                                Placeholder="请输入单位"
                                Required="true" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudNumericField @bind-Value="component.StockQuantity"
                                   For="@(() => component.StockQuantity)"
                                   Label="库存数量"
                                   Min="0" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudNumericField @bind-Value="component.MinStockQuantity"
                                   For="@(() => component.MinStockQuantity)"
                                   Label="最小库存量"
                                   Min="0" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="component.UnitPrice"
                                   For="@(() => component.UnitPrice)"
                                   Label="单价"
                                   Min="0"
                                   Format="F2"
                                   Adornment="Adornment.Start"
                                   AdornmentText="¥" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSwitch @bind-Value="component.IsEnabled"
                             For="@(() => component.IsEnabled)"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="component.Specifications"
                                For="@(() => component.Specifications)"
                                Label="规格参数"
                                Placeholder="请输入规格参数"
                                Lines="2" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="component.Description"
                                For="@(() => component.Description)"
                                Label="部件描述"
                                Placeholder="请输入部件描述"
                                Lines="3" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="component.Remark"
                                For="@(() => component.Remark)"
                                Label="备注"
                                Placeholder="请输入备注"
                                Lines="2" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@isSubmitting">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public Component Component { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; }
    [Parameter] public List<ComponentCategory> Categories { get; set; } = new();

    private Component component = new();
    private MudForm form = null!;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        if (IsEdit)
        {
            // 编辑模式：复制现有数据
            component = new Component
            {
                Id = Component.Id,
                Code = Component.Code,
                Name = Component.Name,
                CategoryId = Component.CategoryId,
                Model = Component.Model,
                Specifications = Component.Specifications,
                Supplier = Component.Supplier,
                Brand = Component.Brand,
                Unit = Component.Unit,
                StockQuantity = Component.StockQuantity,
                MinStockQuantity = Component.MinStockQuantity,
                UnitPrice = Component.UnitPrice,
                Description = Component.Description,
                IsEnabled = Component.IsEnabled,
                Remark = Component.Remark
            };
        }
        else
        {
            // 新建模式：初始化默认值
            component = new Component
            {
                Unit = "个",
                IsEnabled = true,
                StockQuantity = 0,
                MinStockQuantity = 0
            };
        }
    }

    private async Task Submit()
    {
        try
        {
            await form.Validate();
            if (!form.IsValid) return;

            isSubmitting = true;
            StateHasChanged();

            var result = IsEdit 
                ? await ComponentService.UpdateComponentAsync(component)
                : await ComponentService.CreateComponentAsync(component);

            if (result.IsSuccess)
            {
                Snackbar.Add($"部件{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add(result.ErrorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    public class ComponentValidator : AbstractValidator<Component>
    {
        public ComponentValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("部件编码不能为空")
                .MaximumLength(50).WithMessage("部件编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("部件名称不能为空")
                .MaximumLength(100).WithMessage("部件名称长度不能超过100个字符");

            RuleFor(x => x.CategoryId)
                .GreaterThan(0).WithMessage("请选择部件分类");

            RuleFor(x => x.Unit)
                .NotEmpty().WithMessage("单位不能为空")
                .MaximumLength(20).WithMessage("单位长度不能超过20个字符");

            RuleFor(x => x.Model)
                .MaximumLength(200).WithMessage("型号规格长度不能超过200个字符");

            RuleFor(x => x.Specifications)
                .MaximumLength(500).WithMessage("规格参数长度不能超过500个字符");

            RuleFor(x => x.Supplier)
                .MaximumLength(100).WithMessage("供应商长度不能超过100个字符");

            RuleFor(x => x.Brand)
                .MaximumLength(100).WithMessage("品牌长度不能超过100个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("部件描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");

            RuleFor(x => x.StockQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("库存数量不能小于0");

            RuleFor(x => x.MinStockQuantity)
                .GreaterThanOrEqualTo(0).WithMessage("最小库存量不能小于0");

            RuleFor(x => x.UnitPrice)
                .GreaterThanOrEqualTo(0).When(x => x.UnitPrice.HasValue).WithMessage("单价不能小于0");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<Component>.CreateWithOptions((Component)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
