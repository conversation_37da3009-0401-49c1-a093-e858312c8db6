using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件管理服务实现
    /// </summary>
    public class ComponentService : IComponentService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<ComponentService> _logger;

        public ComponentService(
            DatabaseContext dbContext,
            ILogger<ComponentService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<ComponentDto>> GetAllComponentsAsync()
        {
            try
            {
                // 先获取所有部件
                var components = await _dbContext.Db.Queryable<Component>()
                    .Includes(c => c.Category)
                    .OrderBy(c => c.CategoryId, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.Name, SqlSugar.OrderByType.Asc)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有部件失败");
                throw;
            }
        }

        public async Task<Component?> GetComponentByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取部件失败: {id}", id);
                throw;
            }
        }

        public async Task<List<ComponentDto>> GetEnabledComponentsAsync()
        {
            try
            {
                // 先获取启用的部件
                var components = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.IsEnabled)
                    .Includes(c => c.Category)
                    .OrderBy(c => c.CategoryId, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.Name, SqlSugar.OrderByType.Asc)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的部件失败");
                throw;
            }
        }

        public async Task<List<ComponentDto>> GetComponentsByCategoryAsync(int categoryId)
        {
            try
            {
                // 先获取指定分类的部件
                var components = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.CategoryId == categoryId && c.IsEnabled)
                    .Includes(c => c.Category)
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据分类获取部件失败: {categoryId}", categoryId);
                throw;
            }
        }

        public async Task<List<ComponentDto>> SearchComponentsAsync(string keyword, int? categoryId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Component>()
                    .Where(c => c.IsEnabled);

                if (!string.IsNullOrWhiteSpace(keyword))
                {
                    query = query.Where(c => c.Name.Contains(keyword) ||
                                           c.Code.Contains(keyword) ||
                                           c.Model.Contains(keyword) ||
                                           c.Brand.Contains(keyword) ||
                                           c.Supplier.Contains(keyword));
                }

                if (categoryId.HasValue)
                {
                    query = query.Where(c => c.CategoryId == categoryId.Value);
                }

                var components = await query
                    .Includes(c => c.Category)
                    .OrderBy(c => c.CategoryId, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.Name, SqlSugar.OrderByType.Asc)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索部件失败: {keyword}", keyword);
                throw;
            }
        }

        public async Task<List<ComponentDto>> GetLowStockComponentsAsync()
        {
            try
            {
                var components = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.IsEnabled && c.StockQuantity <= c.MinStockQuantity)
                    .Includes(c => c.Category)
                    .OrderBy(c => c.StockQuantity, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.CategoryId, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.Name, SqlSugar.OrderByType.Asc)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取库存不足的部件失败");
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateComponentAsync(Component component)
        {
            try
            {
                // 检查编码是否唯一
                if (!await IsComponentCodeUniqueAsync(component.Code))
                {
                    return (false, "部件编码已存在");
                }

                // 验证分类是否存在
                var category = await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.Id == component.CategoryId && cc.IsEnabled)
                    .FirstAsync();
                if (category == null)
                {
                    return (false, "部件分类不存在或已禁用");
                }

                component.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(component).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建部件成功: {name} ({code})", component.Name, component.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建部件失败: {name}", component.Name);
                return (false, $"创建部件失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateComponentAsync(Component component)
        {
            try
            {
                // 检查编码是否唯一（排除自己）
                if (!await IsComponentCodeUniqueAsync(component.Code, component.Id))
                {
                    return (false, "部件编码已存在");
                }

                // 验证分类是否存在
                var category = await _dbContext.Db.Queryable<ComponentCategory>()
                    .Where(cc => cc.Id == component.CategoryId && cc.IsEnabled)
                    .FirstAsync();
                if (category == null)
                {
                    return (false, "部件分类不存在或已禁用");
                }

                component.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(component).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新部件成功: {name} ({code})", component.Name, component.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部件不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新部件失败: {id}", component.Id);
                return (false, $"更新部件失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteComponentAsync(int id)
        {
            try
            {
                if (!await CanDeleteComponentAsync(id))
                {
                    return (false, "该部件已被使用，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<Component>()
                    .Where(c => c.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除部件成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "部件不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除部件失败: {id}", id);
                return (false, $"删除部件失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var component = await GetComponentByIdAsync(id);
                if (component == null)
                {
                    return (false, "部件不存在");
                }

                component.IsEnabled = !component.IsEnabled;
                component.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(component)
                    .UpdateColumns(c => new { c.IsEnabled, c.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换部件状态成功: {id} -> {status}", id, component.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换部件状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteComponentsAsync(List<int> ids)
        {
            try
            {
                // 检查所有部件是否可以删除
                foreach (var id in ids)
                {
                    if (!await CanDeleteComponentAsync(id))
                    {
                        var component = await GetComponentByIdAsync(id);
                        return (false, $"部件 '{component?.Name}' 已被使用，无法删除");
                    }
                }

                var result = await _dbContext.Db.Deleteable<Component>()
                    .Where(c => ids.Contains(c.Id))
                    .ExecuteCommandAsync();

                _logger.LogInformation("批量删除部件成功: {count}个", result);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除部件失败");
                return (false, $"批量删除部件失败: {ex.Message}");
            }
        }

        public async Task<bool> IsComponentCodeUniqueAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                return !await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部件编码是否唯一失败: {code}", code);
                throw;
            }
        }

        public async Task<bool> CanDeleteComponentAsync(int id)
        {
            try
            {
                // 检查是否有关联的模板
                var templateCount = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .Where(t => t.ComponentId == id)
                    .CountAsync();

                if (templateCount > 0)
                {
                    return false;
                }

                // 检查是否有关联的更换记录
                var recordCount = await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .Where(r => r.ComponentId == id)
                    .CountAsync();

                return recordCount == 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查部件是否可删除失败: {id}", id);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateStockQuantityAsync(int id, int quantity, string reason)
        {
            try
            {
                var component = await GetComponentByIdAsync(id);
                if (component == null)
                {
                    return (false, "部件不存在");
                }

                component.StockQuantity = quantity;
                component.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(component)
                    .UpdateColumns(c => new { c.StockQuantity, c.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新部件库存成功: {id} -> {quantity}, 原因: {reason}", id, quantity, reason);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新部件库存失败: {id}", id);
                return (false, $"更新库存失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateStockAsync(List<(int ComponentId, int Quantity)> updates)
        {
            try
            {
                foreach (var update in updates)
                {
                    var component = await GetComponentByIdAsync(update.ComponentId);
                    if (component != null)
                    {
                        component.StockQuantity = update.Quantity;
                        component.UpdatedAt = DateTime.Now;

                        await _dbContext.Db.Updateable(component)
                            .UpdateColumns(c => new { c.StockQuantity, c.UpdatedAt })
                            .ExecuteCommandAsync();
                    }
                }

                _logger.LogInformation("批量更新部件库存成功: {count}个", updates.Count);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新部件库存失败");
                return (false, $"批量更新库存失败: {ex.Message}");
            }
        }

        public async Task<Dictionary<int, int>> GetComponentUsageStatisticsAsync()
        {
            try
            {
                var statistics = await _dbContext.Db.Queryable<ComponentReplacementRecord>()
                    .GroupBy(r => r.ComponentId)
                    .Select(g => new { ComponentId = g.ComponentId, Count = SqlFunc.AggregateCount(g.Id) })
                    .ToListAsync();

                return statistics.ToDictionary(s => s.ComponentId, s => s.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部件使用统计失败");
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int ImportedCount)> ImportComponentsAsync(List<Component> components)
        {
            try
            {
                var importedCount = 0;
                var errors = new List<string>();

                foreach (var component in components)
                {
                    // 检查编码是否唯一
                    if (!await IsComponentCodeUniqueAsync(component.Code))
                    {
                        errors.Add($"部件编码 '{component.Code}' 已存在");
                        continue;
                    }

                    // 验证分类是否存在
                    var category = await _dbContext.Db.Queryable<ComponentCategory>()
                        .Where(cc => cc.Id == component.CategoryId && cc.IsEnabled)
                        .FirstAsync();
                    if (category == null)
                    {
                        errors.Add($"部件 '{component.Name}' 的分类不存在或已禁用");
                        continue;
                    }

                    component.CreatedAt = DateTime.Now;
                    await _dbContext.Db.Insertable(component).ExecuteReturnIdentityAsync();
                    importedCount++;
                }

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                _logger.LogInformation("导入部件完成: 成功{imported}个, 失败{failed}个", importedCount, errors.Count);

                return (true, errorMessage, importedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入部件失败");
                return (false, $"导入部件失败: {ex.Message}", 0);
            }
        }

        public async Task<List<ComponentDto>> ExportComponentsAsync(int? categoryId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Component>();

                if (categoryId.HasValue)
                {
                    query = query.Where(c => c.CategoryId == categoryId.Value);
                }

                var components = await query
                    .Includes(c => c.Category)
                    .OrderBy(c => c.CategoryId, SqlSugar.OrderByType.Asc)
                    .OrderBy(c => c.Name, SqlSugar.OrderByType.Asc)
                    .ToListAsync();

                // 转换为DTO
                var result = components.Select(c => ComponentDto.FromEntity(c, c.Category)).ToList();

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出部件数据失败");
                throw;
            }
        }
    }
}
