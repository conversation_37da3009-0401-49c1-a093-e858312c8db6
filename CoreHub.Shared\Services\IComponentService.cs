using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件管理服务接口
    /// </summary>
    public interface IComponentService
    {
        /// <summary>
        /// 获取所有部件
        /// </summary>
        Task<List<ComponentDto>> GetAllComponentsAsync();

        /// <summary>
        /// 根据ID获取部件
        /// </summary>
        Task<Component?> GetComponentByIdAsync(int id);

        /// <summary>
        /// 获取启用的部件
        /// </summary>
        Task<List<ComponentDto>> GetEnabledComponentsAsync();

        /// <summary>
        /// 根据分类获取部件
        /// </summary>
        Task<List<ComponentDto>> GetComponentsByCategoryAsync(int categoryId);

        /// <summary>
        /// 搜索部件
        /// </summary>
        Task<List<ComponentDto>> SearchComponentsAsync(string keyword, int? categoryId = null);

        /// <summary>
        /// 获取库存不足的部件
        /// </summary>
        Task<List<ComponentDto>> GetLowStockComponentsAsync();

        /// <summary>
        /// 创建部件
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateComponentAsync(Component component);

        /// <summary>
        /// 更新部件
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateComponentAsync(Component component);

        /// <summary>
        /// 删除部件
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteComponentAsync(int id);

        /// <summary>
        /// 切换部件状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 批量删除部件
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteComponentsAsync(List<int> ids);

        /// <summary>
        /// 检查部件编码是否唯一
        /// </summary>
        Task<bool> IsComponentCodeUniqueAsync(string code, int? excludeId = null);

        /// <summary>
        /// 检查是否可以删除部件（没有关联的模板或更换记录）
        /// </summary>
        Task<bool> CanDeleteComponentAsync(int id);

        /// <summary>
        /// 更新库存数量
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateStockQuantityAsync(int id, int quantity, string reason);

        /// <summary>
        /// 批量更新库存
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> BatchUpdateStockAsync(List<(int ComponentId, int Quantity)> updates);

        /// <summary>
        /// 获取部件使用统计
        /// </summary>
        Task<Dictionary<int, int>> GetComponentUsageStatisticsAsync();

        /// <summary>
        /// 导入部件数据
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, int ImportedCount)> ImportComponentsAsync(List<Component> components);

        /// <summary>
        /// 导出部件数据
        /// </summary>
        Task<List<ComponentDto>> ExportComponentsAsync(int? categoryId = null);
    }
}
