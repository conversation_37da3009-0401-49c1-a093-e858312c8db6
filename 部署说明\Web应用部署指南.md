# CoreHub Web应用部署指南 (Windows平台)

## 概述

本指南说明如何在Windows平台上部署CoreHub Web应用，使用Kestrel作为Web服务器，不依赖IIS。

## 系统要求

### 运行环境
- Windows 10/11 或 Windows Server 2019+
- .NET 8.0 Runtime
- SQL Server 2019+ 或 SQL Server Express/LocalDB
- 至少2GB可用内存
- 至少1GB可用磁盘空间

## 环境准备

### 1. 安装.NET 8.0 Runtime
```powershell
# 使用winget安装
winget install Microsoft.DotNet.Runtime.8
winget install Microsoft.DotNet.AspNetCore.8

# 验证安装
dotnet --version
dotnet --list-runtimes
```

### 2. 配置SQL Server

#### 2.1 安装SQL Server Express (如果未安装)
```powershell
# 下载SQL Server Express
# 访问: https://www.microsoft.com/sql-server/sql-server-downloads
# 选择Express版本下载并安装

# 或使用Chocolatey安装
choco install sql-server-express
```

#### 2.2 创建数据库
```sql
-- 连接到SQL Server实例
-- 使用SQL Server Management Studio或sqlcmd

-- 创建数据库
CREATE DATABASE CoreHubDB;
GO

-- 创建应用用户（推荐用于生产环境）
CREATE LOGIN CoreHubUser WITH PASSWORD = 'YourStrongPassword123!';
USE CoreHubDB;
CREATE USER CoreHubUser FOR LOGIN CoreHubUser;
ALTER ROLE db_datareader ADD MEMBER CoreHubUser;
ALTER ROLE db_datawriter ADD MEMBER CoreHubUser;
ALTER ROLE db_ddladmin ADD MEMBER CoreHubUser;
GO
```

#### 2.3 配置SQL Server网络访问
```powershell
# 启用TCP/IP协议
# 1. 打开SQL Server Configuration Manager
# 2. 展开SQL Server网络配置
# 3. 选择对应实例的协议
# 4. 启用TCP/IP协议
# 5. 重启SQL Server服务

# 或使用PowerShell重启服务
Restart-Service -Name "MSSQL`$SQLEXPRESS" -Force
```

## 应用构建和配置

### 1. 构建应用

#### 1.1 使用Visual Studio发布
1. 在Visual Studio中打开CoreHub.Web项目
2. 右键项目 -> "发布"
3. 选择"文件夹"目标
4. 设置目标位置：`C:\CoreHub\WebApp`
5. 配置设置：
   - 配置：Release
   - 目标框架：net8.0
   - 部署模式：独立部署 (推荐) 或 框架依赖
   - 目标运行时：win-x64

#### 1.2 使用命令行构建
```powershell
# 进入Web项目目录
cd CoreHub.Web

# 独立部署 (推荐，包含运行时)
dotnet publish -c Release -o "C:\CoreHub\WebApp" --runtime win-x64 --self-contained true

# 或框架依赖部署 (需要安装.NET运行时)
dotnet publish -c Release -o "C:\CoreHub\WebApp" --runtime win-x64 --self-contained false
```

### 2. 配置应用设置

#### 2.1 创建生产配置文件
在 `C:\CoreHub\WebApp\` 目录下创建或编辑 `appsettings.Production.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=CoreHubDB;Integrated Security=true;TrustServerCertificate=true;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    },
    "File": {
      "Path": "C:\\CoreHub\\Logs\\log-.txt",
      "LogLevel": {
        "Default": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      },
      "Https": {
        "Url": "https://0.0.0.0:5001",
        "Certificate": {
          "Path": "C:\\CoreHub\\Certificates\\corehub.pfx",
          "Password": "YourCertificatePassword"
        }
      }
    }
  },
  "Urls": "http://0.0.0.0:5000;https://0.0.0.0:5001"
}
```

#### 2.2 创建必要目录
```powershell
# 创建应用目录结构
New-Item -ItemType Directory -Path "C:\CoreHub\WebApp" -Force
New-Item -ItemType Directory -Path "C:\CoreHub\Logs" -Force
New-Item -ItemType Directory -Path "C:\CoreHub\Certificates" -Force
New-Item -ItemType Directory -Path "C:\CoreHub\Backup" -Force
New-Item -ItemType Directory -Path "C:\CoreHub\Scripts" -Force
```

### 3. SSL证书配置 (可选)

#### 3.1 生成自签名证书
```powershell
# 生成自签名证书
$cert = New-SelfSignedCertificate -DnsName "localhost", "corehub.local", "127.0.0.1" -CertStoreLocation "cert:\LocalMachine\My" -NotAfter (Get-Date).AddYears(2) -KeyUsage DigitalSignature, KeyEncipherment -TextExtension @("*********={text}*******.*******.1")

# 导出证书到PFX文件
$certPassword = ConvertTo-SecureString -String "YourCertificatePassword" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath "C:\CoreHub\Certificates\corehub.pfx" -Password $certPassword

Write-Host "证书已生成: C:\CoreHub\Certificates\corehub.pfx"
Write-Host "证书指纹: $($cert.Thumbprint)"
```

## 部署和运行

### 1. 创建Windows服务 (推荐)

#### 1.1 安装NSSM (Non-Sucking Service Manager)
```powershell
# 使用Chocolatey安装
choco install nssm

# 或手动下载
# 访问: https://nssm.cc/download
# 下载并解压到 C:\Tools\nssm
```

#### 1.2 创建Windows服务
```powershell
# 使用NSSM创建服务
nssm install CoreHubWeb "C:\CoreHub\WebApp\CoreHub.Web.exe"

# 配置服务参数
nssm set CoreHubWeb AppDirectory "C:\CoreHub\WebApp"
nssm set CoreHubWeb AppEnvironmentExtra "ASPNETCORE_ENVIRONMENT=Production"
nssm set CoreHubWeb DisplayName "CoreHub Web Application"
nssm set CoreHubWeb Description "CoreHub企业设备管理系统Web应用"
nssm set CoreHubWeb Start SERVICE_AUTO_START

# 配置日志
nssm set CoreHubWeb AppStdout "C:\CoreHub\Logs\service-stdout.log"
nssm set CoreHubWeb AppStderr "C:\CoreHub\Logs\service-stderr.log"

# 启动服务
nssm start CoreHubWeb
```

#### 1.3 验证服务状态
```powershell
# 检查服务状态
Get-Service -Name "CoreHubWeb"

# 查看服务详细信息
nssm status CoreHubWeb

# 查看服务日志
Get-Content "C:\CoreHub\Logs\service-stdout.log" -Tail 20
```

### 2. 直接运行 (开发/测试)

#### 2.1 命令行运行
```powershell
# 设置环境变量
$env:ASPNETCORE_ENVIRONMENT = "Production"

# 进入应用目录
cd "C:\CoreHub\WebApp"

# 运行应用
.\CoreHub.Web.exe

# 或使用dotnet命令 (如果是框架依赖部署)
dotnet CoreHub.Web.dll
```

#### 2.2 创建启动脚本
创建 `C:\CoreHub\Scripts\start-web.ps1`:
```powershell
# start-web.ps1 - Web应用启动脚本
param(
    [switch]$Background
)

Write-Host "启动CoreHub Web应用..."

# 设置环境变量
$env:ASPNETCORE_ENVIRONMENT = "Production"
$env:ASPNETCORE_URLS = "http://0.0.0.0:5000;https://0.0.0.0:5001"

# 进入应用目录
Set-Location "C:\CoreHub\WebApp"

if ($Background) {
    # 后台运行
    Start-Process -FilePath ".\CoreHub.Web.exe" -WindowStyle Hidden
    Write-Host "应用已在后台启动"
} else {
    # 前台运行
    .\CoreHub.Web.exe
}
```

## 防火墙配置

### 1. 配置Windows防火墙
```powershell
# 允许HTTP端口5000
New-NetFirewallRule -DisplayName "CoreHub HTTP" -Direction Inbound -Protocol TCP -LocalPort 5000 -Action Allow

# 允许HTTPS端口5001
New-NetFirewallRule -DisplayName "CoreHub HTTPS" -Direction Inbound -Protocol TCP -LocalPort 5001 -Action Allow

# 或者允许应用程序
New-NetFirewallRule -DisplayName "CoreHub Web App" -Direction Inbound -Program "C:\CoreHub\WebApp\CoreHub.Web.exe" -Action Allow
```

### 2. 验证端口访问
```powershell
# 检查端口监听状态
netstat -ano | findstr :5000
netstat -ano | findstr :5001

# 测试本地连接
Test-NetConnection -ComputerName "localhost" -Port 5000
Invoke-WebRequest -Uri "http://localhost:5000" -UseBasicParsing
```

## 监控和维护

### 1. 日志监控

#### 1.1 查看应用日志
```powershell
# 查看最新日志
Get-Content "C:\CoreHub\Logs\log-*.txt" -Tail 50

# 实时监控日志
Get-Content "C:\CoreHub\Logs\log-*.txt" -Wait -Tail 10

# 查看错误日志
Select-String -Path "C:\CoreHub\Logs\log-*.txt" -Pattern "Error|Exception" | Select-Object -Last 20
```

#### 1.2 查看服务日志
```powershell
# 查看Windows服务日志
Get-Content "C:\CoreHub\Logs\service-stdout.log" -Tail 20
Get-Content "C:\CoreHub\Logs\service-stderr.log" -Tail 20

# 查看Windows事件日志
Get-EventLog -LogName Application -Source "CoreHub*" -Newest 10
```

### 2. 性能监控
```powershell
# 监控应用进程
Get-Process -Name "CoreHub.Web" | Select-Object ProcessName, Id, CPU, WorkingSet, VirtualMemorySize

# 监控端口连接
Get-NetTCPConnection -LocalPort 5000, 5001 | Select-Object LocalAddress, LocalPort, RemoteAddress, State

# 系统资源监控
Get-Counter "\Process(CoreHub.Web)\% Processor Time"
Get-Counter "\Process(CoreHub.Web)\Working Set"
```

### 3. 自动化脚本

#### 3.1 部署脚本
创建 `C:\CoreHub\Scripts\deploy.ps1`:
```powershell
# deploy.ps1 - 自动化部署脚本
param(
    [string]$SourcePath = ".\CoreHub.Web\bin\Release\net8.0\publish",
    [string]$BackupEnabled = $true
)

Write-Host "开始部署CoreHub Web应用..."

# 停止服务
Write-Host "停止Web服务..."
nssm stop CoreHubWeb
Start-Sleep -Seconds 5

# 备份当前版本
if ($BackupEnabled) {
    $backupPath = "C:\CoreHub\Backup\WebApp_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    if (Test-Path "C:\CoreHub\WebApp") {
        Copy-Item -Path "C:\CoreHub\WebApp" -Destination $backupPath -Recurse
        Write-Host "已备份到: $backupPath"
    }
}

# 部署新版本
Write-Host "复制新版本文件..."
if (Test-Path $SourcePath) {
    Copy-Item -Path "$SourcePath\*" -Destination "C:\CoreHub\WebApp" -Recurse -Force
    Write-Host "文件已复制完成"
} else {
    Write-Error "源路径不存在: $SourcePath"
    exit 1
}

# 启动服务
Write-Host "启动Web服务..."
nssm start CoreHubWeb
Start-Sleep -Seconds 3

# 验证服务状态
$serviceStatus = nssm status CoreHubWeb
Write-Host "服务状态: $serviceStatus"

if ($serviceStatus -eq "SERVICE_RUNNING") {
    Write-Host "部署成功! 应用正在运行"
    Write-Host "访问地址: http://localhost:5000"
} else {
    Write-Error "服务启动失败，请检查日志"
}
```

#### 3.2 备份脚本
创建 `C:\CoreHub\Scripts\backup.ps1`:
```powershell
# backup.ps1 - 备份脚本
$date = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "C:\CoreHub\Backup\Full_$date"

Write-Host "开始备份CoreHub系统..."

# 创建备份目录
New-Item -ItemType Directory -Path $backupDir -Force

# 备份数据库
Write-Host "备份数据库..."
$sqlBackupPath = "$backupDir\CoreHubDB.bak"
sqlcmd -S ".\SQLEXPRESS" -Q "BACKUP DATABASE CoreHubDB TO DISK = '$sqlBackupPath' WITH FORMAT, INIT"

# 备份应用文件
Write-Host "备份应用文件..."
Copy-Item -Path "C:\CoreHub\WebApp" -Destination "$backupDir\WebApp" -Recurse

# 备份配置和日志
Copy-Item -Path "C:\CoreHub\Logs" -Destination "$backupDir\Logs" -Recurse
Copy-Item -Path "C:\CoreHub\Certificates" -Destination "$backupDir\Certificates" -Recurse

# 创建备份信息文件
$backupInfo = @{
    BackupDate = Get-Date
    DatabaseBackup = $sqlBackupPath
    ApplicationPath = "$backupDir\WebApp"
    LogsPath = "$backupDir\Logs"
} | ConvertTo-Json

$backupInfo | Out-File -FilePath "$backupDir\backup-info.json"

Write-Host "备份完成: $backupDir"

# 清理旧备份 (保留最近7天)
$oldBackups = Get-ChildItem "C:\CoreHub\Backup" | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-7) }
foreach ($backup in $oldBackups) {
    Remove-Item $backup.FullName -Recurse -Force
    Write-Host "已删除旧备份: $($backup.Name)"
}
```

## 故障排除

### 1. 常见问题

#### 1.1 应用无法启动
```powershell
# 检查.NET运行时
dotnet --list-runtimes

# 检查端口占用
netstat -ano | findstr :5000

# 查看详细错误信息
$env:ASPNETCORE_ENVIRONMENT = "Development"
cd "C:\CoreHub\WebApp"
.\CoreHub.Web.exe
```

#### 1.2 数据库连接失败
```powershell
# 检查SQL Server服务状态
Get-Service -Name "*SQL*"

# 测试数据库连接
sqlcmd -S ".\SQLEXPRESS" -Q "SELECT @@VERSION"

# 检查连接字符串
# 编辑 appsettings.Production.json 中的连接字符串
```

#### 1.3 权限问题
```powershell
# 检查文件权限
icacls "C:\CoreHub\WebApp"
icacls "C:\CoreHub\Logs"

# 给当前用户完全控制权限
icacls "C:\CoreHub" /grant "$env:USERNAME:(OI)(CI)F" /T
```

### 2. 诊断命令
```powershell
# 系统诊断脚本
Write-Host "=== CoreHub Web应用诊断 ==="

# 检查.NET运行时
Write-Host "`n.NET运行时:"
dotnet --list-runtimes

# 检查服务状态
Write-Host "`n服务状态:"
Get-Service -Name "CoreHubWeb" -ErrorAction SilentlyContinue

# 检查端口监听
Write-Host "`n端口监听:"
netstat -ano | findstr ":5000\|:5001"

# 检查进程
Write-Host "`n相关进程:"
Get-Process -Name "*CoreHub*" -ErrorAction SilentlyContinue

# 检查防火墙规则
Write-Host "`n防火墙规则:"
Get-NetFirewallRule -DisplayName "*CoreHub*" | Select-Object DisplayName, Enabled, Direction

# 检查最新日志
Write-Host "`n最新日志 (最后5行):"
$logFiles = Get-ChildItem "C:\CoreHub\Logs\log-*.txt" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending
if ($logFiles) {
    Get-Content $logFiles[0].FullName -Tail 5
} else {
    Write-Host "未找到日志文件"
}
```

## 安全建议

### 1. 应用安全
- 定期更新.NET运行时和依赖包
- 使用强密码保护数据库连接
- 启用HTTPS并使用有效证书
- 限制应用运行权限

### 2. 网络安全
- 配置防火墙规则限制访问
- 使用VPN或内网访问
- 定期监控访问日志
- 实施IP白名单 (如需要)

### 3. 数据安全
- 定期备份数据库和应用
- 加密敏感配置信息
- 监控异常访问行为
- 实施数据访问审计

---

本指南提供了在Windows平台上不使用IIS部署CoreHub Web应用的完整流程。使用Kestrel作为Web服务器，配合Windows服务实现稳定运行。