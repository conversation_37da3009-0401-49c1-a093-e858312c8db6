@echo off
setlocal enabledelayedexpansion

:: Check admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo *** WARNING: May not have administrator privileges ***
    echo If setting environment variables fails, please run as administrator
    echo.
    pause
)

echo ========================================
echo           CoreHub Environment Tool
echo ========================================
echo Current User: %USERNAME%
echo Current Time: %DATE% %TIME%
echo ========================================
echo.

:MENU
echo Please select environment:
echo.
echo [1] Development Environment
echo [2] Staging Environment
echo [3] Production Environment
echo [4] Show Current Configuration
echo [5] Clear Environment Variables
echo [6] Check System Information
echo [0] Exit
echo.
set /p choice=Enter choice (0-6): 

if "%choice%"=="1" goto DEV
if "%choice%"=="2" goto STAGING
if "%choice%"=="3" goto PROD
if "%choice%"=="4" goto SHOW
if "%choice%"=="5" goto CLEAR
if "%choice%"=="6" goto CHECK_SYSTEM
if "%choice%"=="0" goto EXIT
echo Invalid choice, please try again.
echo.
goto MENU

:DEV
echo.
echo Setting CoreHub Development environment...
setx COREHUB_ENVIRONMENT "Development" >nul 2>&1
if %errorlevel% equ 0 (
    echo Set COREHUB_ENVIRONMENT successfully
) else (
    echo Failed to set COREHUB_ENVIRONMENT
)

setx COREHUB_API_BASE_URL "http://************:8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo Set COREHUB_API_BASE_URL successfully
) else (
    echo Failed to set COREHUB_API_BASE_URL
)

setx COREHUB_DB_CONNECTION_STRING "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True" >nul 2>&1
setx COREHUB_USE_HTTPS_REDIRECTION "false" >nul 2>&1
setx COREHUB_ENABLE_VERBOSE_LOGGING "true" >nul 2>&1

echo.
echo CoreHub Development environment configured
echo - Environment: Development (CoreHub-specific)
echo - API URL: http://************:8080
echo - HTTPS Redirect: Disabled
echo - Verbose Logging: Enabled
goto RESTART_PROMPT

:STAGING
echo.
echo Setting CoreHub Staging environment...
setx COREHUB_ENVIRONMENT "Staging" >nul 2>&1
setx COREHUB_API_BASE_URL "https://api-staging.saintyeartex.com:8081" >nul 2>&1
setx COREHUB_DB_CONNECTION_STRING "Server=***********;Database=CoreHub_Staging;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True" >nul 2>&1
setx COREHUB_USE_HTTPS_REDIRECTION "true" >nul 2>&1
setx COREHUB_ENABLE_VERBOSE_LOGGING "false" >nul 2>&1

echo.
echo CoreHub Staging environment configured
echo - Environment: Staging (CoreHub-specific)
echo - API URL: https://api-staging.saintyeartex.com:8081
echo - HTTPS Redirect: Enabled
echo - Verbose Logging: Disabled
goto RESTART_PROMPT

:PROD
echo.
echo Setting CoreHub Production environment...
setx COREHUB_ENVIRONMENT "Production" >nul 2>&1
setx COREHUB_API_BASE_URL "https://api.saintyeartex.com:8081" >nul 2>&1
setx COREHUB_DB_CONNECTION_STRING "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True" >nul 2>&1
setx COREHUB_USE_HTTPS_REDIRECTION "true" >nul 2>&1
setx COREHUB_ENABLE_VERBOSE_LOGGING "false" >nul 2>&1

echo.
echo CoreHub Production environment configured
echo - Environment: Production (CoreHub-specific)
echo - API URL: https://api.saintyeartex.com:8081
echo - HTTPS Redirect: Enabled
echo - Verbose Logging: Disabled
goto RESTART_PROMPT

:SHOW
echo.
echo Current CoreHub Configuration:
echo ========================================
echo Environment: %COREHUB_ENVIRONMENT%
echo API URL: %COREHUB_API_BASE_URL%
echo DB Connection: [HIDDEN FOR SECURITY]
echo HTTPS Redirect: %COREHUB_USE_HTTPS_REDIRECTION%
echo Verbose Logging: %COREHUB_ENABLE_VERBOSE_LOGGING%
echo ========================================
echo.
echo Project Isolation: CoreHub uses COREHUB_* variables
echo This ensures no conflicts with other projects
echo.
pause
goto MENU

:CLEAR
echo.
echo Clearing CoreHub environment variables...
setx COREHUB_ENVIRONMENT "" >nul 2>&1
setx COREHUB_API_BASE_URL "" >nul 2>&1
setx COREHUB_DB_CONNECTION_STRING "" >nul 2>&1
setx COREHUB_USE_HTTPS_REDIRECTION "" >nul 2>&1
setx COREHUB_ENABLE_VERBOSE_LOGGING "" >nul 2>&1
echo CoreHub environment variables cleared
echo Other projects are unaffected
echo.
goto RESTART_PROMPT

:RESTART_PROMPT
echo.
echo IMPORTANT:
echo Environment variables updated, please restart application
echo - Visual Studio: Restart Visual Studio
echo - Command Line: Open new command window
echo - IIS: Restart application pool
echo.
pause
goto MENU

:CHECK_SYSTEM
echo.
echo System Information:
echo ========================================
echo Operating System: %OS%
echo Computer Name: %COMPUTERNAME%
echo User Name: %USERNAME%
echo Current Directory: %CD%
echo.
echo Permission Check:
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo Has administrator privileges
) else (
    echo No administrator privileges (may affect environment variable setting)
)
echo.
echo CoreHub Environment Variable Check:
if defined COREHUB_ENVIRONMENT (
    echo COREHUB_ENVIRONMENT = %COREHUB_ENVIRONMENT%
) else (
    echo COREHUB_ENVIRONMENT not set
)
if defined COREHUB_API_BASE_URL (
    echo COREHUB_API_BASE_URL = %COREHUB_API_BASE_URL%
) else (
    echo COREHUB_API_BASE_URL not set
)
echo.
echo Project Isolation Status:
echo CoreHub uses COREHUB_* prefixed environment variables
echo This prevents conflicts with other projects
echo ========================================
echo.
pause
goto MENU

:EXIT
echo.
echo Thank you for using CoreHub Environment Tool!
echo.
pause
exit /b 0
