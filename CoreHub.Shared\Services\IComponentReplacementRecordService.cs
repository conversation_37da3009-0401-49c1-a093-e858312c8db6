using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 部件更换记录服务接口
    /// </summary>
    public interface IComponentReplacementRecordService
    {
        /// <summary>
        /// 获取所有更换记录
        /// </summary>
        Task<List<ComponentReplacementRecord>> GetAllRecordsAsync();

        /// <summary>
        /// 根据ID获取更换记录
        /// </summary>
        Task<ComponentReplacementRecord?> GetRecordByIdAsync(int id);

        /// <summary>
        /// 根据维修工单获取更换记录
        /// </summary>
        Task<List<ComponentReplacementRecord>> GetRecordsByRepairOrderAsync(int repairOrderId);

        /// <summary>
        /// 根据设备获取更换记录
        /// </summary>
        Task<List<ComponentReplacementRecord>> GetRecordsByEquipmentAsync(int equipmentId);

        /// <summary>
        /// 根据部件获取更换记录
        /// </summary>
        Task<List<ComponentReplacementRecord>> GetRecordsByComponentAsync(int componentId);

        /// <summary>
        /// 创建更换记录
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateRecordAsync(ComponentReplacementRecord record);

        /// <summary>
        /// 更新更换记录
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateRecordAsync(ComponentReplacementRecord record);

        /// <summary>
        /// 删除更换记录
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteRecordAsync(int id);

        /// <summary>
        /// 批量创建更换记录
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, int CreatedCount)> BatchCreateRecordsAsync(List<ComponentReplacementRecord> records);

        /// <summary>
        /// 获取设备的部件更换历史统计
        /// </summary>
        Task<Dictionary<int, int>> GetEquipmentComponentReplacementStatisticsAsync(int equipmentId);

        /// <summary>
        /// 获取部件的更换频率统计
        /// </summary>
        Task<Dictionary<int, int>> GetComponentReplacementFrequencyAsync();

        /// <summary>
        /// 获取指定时间范围内的更换记录
        /// </summary>
        Task<List<ComponentReplacementRecord>> GetRecordsByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 获取维修成本统计
        /// </summary>
        Task<decimal> GetMaintenanceCostByDateRangeAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 根据设备型号获取标准部件清单
        /// </summary>
        Task<List<EquipmentModelComponentTemplate>> GetStandardComponentsByEquipmentAsync(int equipmentId);
    }
}
