@using CoreHub.Shared.Models.AppUpdate
@using CoreHub.Shared.Services
@inject IAppUpdateService UpdateService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudGrid>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="version.VersionNumber" 
                                 Label="版本号" 
                                 Required="true"
                                 Placeholder="例如: 1.0.0" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="version.VersionCode" 
                                    Label="版本代码" 
                                    Required="true"
                                    Min="1" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="version.Platform" Label="平台" Required="true">
                        <MudSelectItem Value="@("Android")">Android</MudSelectItem>
                        <MudSelectItem Value="@("iOS")">iOS</MudSelectItem>
                        <MudSelectItem Value="@("Windows")">Windows</MudSelectItem>
                        <MudSelectItem Value="@("MacOS")">MacOS</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudSelect @bind-Value="version.UpdateType" Label="更新类型" Required="true">
                        <MudSelectItem Value="@("Major")">重大更新</MudSelectItem>
                        <MudSelectItem Value="@("Minor")">功能更新</MudSelectItem>
                        <MudSelectItem Value="@("Patch")">修复更新</MudSelectItem>
                        <MudSelectItem Value="@("Hotfix")">紧急修复</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="version.Title" 
                                 Label="更新标题" 
                                 Required="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="version.Description" 
                                 Label="更新描述" 
                                 Lines="4"
                                 Placeholder="描述此次更新的内容..." />
                </MudItem>
                <!-- 文件上传区域 -->
                <MudItem xs="12">
                    <MudText Typo="Typo.h6" Class="mb-2">文件上传</MudText>
                    <MudFileUpload T="IBrowserFile"
                                  Accept=".apk,.ipa,.msix,.dmg"
                                  FilesChanged="OnFileSelected"
                                  MaximumFileCount="1"
                                  For="@(() => selectedFile)">
                        <ActivatorContent>
                            <MudButton HtmlTag="label"
                                      Variant="Variant.Filled"
                                      Color="Color.Primary"
                                      StartIcon="@Icons.Material.Filled.CloudUpload">
                                选择文件
                            </MudButton>
                        </ActivatorContent>
                        <SelectedTemplate>
                            @if (context != null)
                            {
                                <MudText>@context.Name</MudText>
                                <MudText Typo="Typo.caption">@FormatFileSize(context.Size)</MudText>
                            }
                        </SelectedTemplate>
                    </MudFileUpload>

                    @if (selectedFile != null)
                    {
                        <MudButton Variant="Variant.Outlined"
                                  Color="Color.Primary"
                                  StartIcon="@Icons.Material.Filled.Upload"
                                  OnClick="UploadFile"
                                  Disabled="@isUploading"
                                  Class="mt-2">
                            @if (isUploading)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                <span class="ml-2">上传中...</span>
                            }
                            else
                            {
                                <span>上传文件</span>
                            }
                        </MudButton>
                    }

                    @if (uploadProgress > 0 && uploadProgress < 100)
                    {
                        <MudProgressLinear Value="uploadProgress" Class="mt-2" />
                    }
                </MudItem>

                <!-- 或手动输入URL -->
                <MudItem xs="12">
                    <MudDivider Class="my-2">
                        <MudText Typo="Typo.caption">或手动输入</MudText>
                    </MudDivider>
                </MudItem>

                <MudItem xs="12">
                    <MudTextField @bind-Value="version.DownloadUrl"
                                 Label="下载URL"
                                 Placeholder="https://example.com/app.apk"
                                 Disabled="@(!string.IsNullOrEmpty(uploadedFileName))" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField @bind-Value="version.FileSize"
                                    Label="文件大小 (字节)"
                                    Min="0"
                                    Disabled="@(!string.IsNullOrEmpty(uploadedFileName))" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudTextField @bind-Value="version.FileMd5"
                                 Label="文件MD5"
                                 Placeholder="32位MD5哈希值"
                                 Disabled="@(!string.IsNullOrEmpty(uploadedFileName))" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch @bind-Value="version.IsForceUpdate" 
                              Label="强制更新" 
                              Color="Color.Warning" />
                </MudItem>
            </MudGrid>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                  OnClick="Submit" 
                  Disabled="@(!IsValid())">
            创建
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    private AppVersion version = new AppVersion
    {
        Platform = "Android",
        UpdateType = "Minor",
        TargetAudience = "All",
        Status = "Draft"
    };

    private IBrowserFile? selectedFile;
    private bool isUploading = false;
    private double uploadProgress = 0;
    private string? uploadedFileName;

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        try
        {
            version.CreatedBy = 1; // 临时设置，实际应该从当前用户获取
            var (isSuccess, errorMessage, versionId) = await UpdateService.CreateVersionAsync(version);
            
            if (isSuccess)
            {
                Snackbar.Add("版本创建成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(versionId));
            }
            else
            {
                Snackbar.Add($"创建失败: {errorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"创建失败: {ex.Message}", Severity.Error);
        }
    }

    private void OnFileSelected(IBrowserFile? file)
    {
        selectedFile = file;
        uploadProgress = 0;
        StateHasChanged();
    }

    private async Task UploadFile()
    {
        if (selectedFile == null) return;

        try
        {
            isUploading = true;
            uploadProgress = 0;
            StateHasChanged();

            // 使用服务层进行文件上传
            var result = await UpdateService.UploadFileAsync(selectedFile, version.Platform, version.VersionNumber);

            if (result.Success)
            {
                version.DownloadUrl = result.DownloadUrl;
                version.FileSize = result.FileSize;
                version.FileMd5 = result.FileMd5;
                uploadedFileName = result.FileName;

                Snackbar.Add(result.Message, Severity.Success);
                uploadProgress = 100;
            }
            else
            {
                Snackbar.Add(result.Message, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"上传失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isUploading = false;
            StateHasChanged();
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(version.VersionNumber) &&
               version.VersionCode > 0 &&
               !string.IsNullOrWhiteSpace(version.Platform) &&
               !string.IsNullOrWhiteSpace(version.Title) &&
               (!string.IsNullOrWhiteSpace(version.DownloadUrl) || !string.IsNullOrEmpty(uploadedFileName)) &&
               version.FileSize > 0;
    }


}
