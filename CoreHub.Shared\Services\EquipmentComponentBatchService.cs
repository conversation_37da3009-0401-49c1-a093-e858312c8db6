using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件批量管理服务实现
    /// </summary>
    public partial class EquipmentComponentBatchService : IEquipmentComponentBatchService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentComponentBatchService> _logger;

        public EquipmentComponentBatchService(
            DatabaseContext dbContext,
            ILogger<EquipmentComponentBatchService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<BatchOperationResultDto> ApplyTemplateToEquipmentsAsync(ApplyTemplateToEquipmentsRequestDto request)
        {
            var result = new BatchOperationResultDto();
            
            try
            {
                _logger.LogInformation("开始应用模板到设备: 模板{modelId}, 设备数量{count}", 
                    request.EquipmentModelId, request.EquipmentIds.Count);

                // 验证请求
                var validation = await ValidateApplyTemplateRequestAsync(request);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                    return result;
                }

                // 获取模板
                var templates = await GetEquipmentModelTemplatesAsync(request.EquipmentModelId, request.ComponentIds, request.OnlyRequiredComponents);
                if (!templates.Any())
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "没有找到可应用的模板";
                    return result;
                }

                // 获取目标设备
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds, request.EquipmentModelId);

                result.TotalCount = equipments.Count;

                // 为每个设备应用模板
                foreach (var equipment in equipments)
                {
                    var itemResult = await ApplyTemplatesToSingleEquipmentAsync(equipment, templates, request.OverrideExisting);
                    result.Details.Add(itemResult);
                    
                    if (itemResult.IsSuccess)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                    }
                }

                result.IsSuccess = result.SuccessCount > 0;
                
                _logger.LogInformation("应用模板完成: 成功{success}, 失败{failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用模板到设备失败");
                result.IsSuccess = false;
                result.ErrorMessage = $"操作失败: {ex.Message}";
                return result;
            }
        }

        public async Task<BatchOperationResultDto> CopyComponentsFromEquipmentAsync(CopyComponentsFromEquipmentRequestDto request)
        {
            var result = new BatchOperationResultDto();
            
            try
            {
                _logger.LogInformation("开始从设备复制部件配置: 源设备{sourceId}, 目标设备数量{count}", 
                    request.SourceEquipmentId, request.TargetEquipmentIds.Count);

                // 验证请求
                var validation = await ValidateCopyComponentsRequestAsync(request);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                    return result;
                }

                // 获取源设备的部件配置
                var sourceConfigurations = await GetEquipmentComponentConfigurationsAsync(request.SourceEquipmentId, request.ComponentIds);
                if (!sourceConfigurations.Any())
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "源设备没有可复制的部件配置";
                    return result;
                }

                // 获取目标设备
                var targetEquipments = await GetValidEquipmentsAsync(request.TargetEquipmentIds);

                result.TotalCount = targetEquipments.Count;

                // 为每个目标设备复制配置
                foreach (var equipment in targetEquipments)
                {
                    var itemResult = await CopyConfigurationsToSingleEquipmentAsync(equipment, sourceConfigurations, request.OverrideExisting);
                    result.Details.Add(itemResult);
                    
                    if (itemResult.IsSuccess)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                    }
                }

                result.IsSuccess = result.SuccessCount > 0;
                
                _logger.LogInformation("复制部件配置完成: 成功{success}, 失败{failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制部件配置失败");
                result.IsSuccess = false;
                result.ErrorMessage = $"操作失败: {ex.Message}";
                return result;
            }
        }

        public async Task<BatchOperationResultDto> BatchAddComponentsToEquipmentsAsync(BatchAddComponentsRequestDto request)
        {
            var result = new BatchOperationResultDto();
            
            try
            {
                _logger.LogInformation("开始批量添加部件: 设备数量{equipmentCount}, 部件数量{componentCount}", 
                    request.EquipmentIds.Count, request.ComponentConfigurations.Count);

                // 验证请求
                var validation = await ValidateBatchAddComponentsRequestAsync(request);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                    return result;
                }

                // 获取目标设备
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                result.TotalCount = equipments.Count;

                // 为每个设备添加部件
                foreach (var equipment in equipments)
                {
                    var itemResult = await AddComponentsToSingleEquipmentAsync(equipment, request.ComponentConfigurations, request.OverrideExisting);
                    result.Details.Add(itemResult);
                    
                    if (itemResult.IsSuccess)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                    }
                }

                result.IsSuccess = result.SuccessCount > 0;
                
                _logger.LogInformation("批量添加部件完成: 成功{success}, 失败{failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量添加部件失败");
                result.IsSuccess = false;
                result.ErrorMessage = $"操作失败: {ex.Message}";
                return result;
            }
        }

        public async Task<BatchOperationResultDto> BatchRemoveComponentsFromEquipmentsAsync(BatchRemoveComponentsRequestDto request)
        {
            var result = new BatchOperationResultDto();
            
            try
            {
                _logger.LogInformation("开始批量移除部件: 设备数量{equipmentCount}, 部件数量{componentCount}", 
                    request.EquipmentIds.Count, request.ComponentIds.Count);

                // 验证请求
                var validation = await ValidateBatchRemoveComponentsRequestAsync(request);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                    return result;
                }

                // 获取目标设备
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                result.TotalCount = equipments.Count;

                // 为每个设备移除部件
                foreach (var equipment in equipments)
                {
                    var itemResult = await RemoveComponentsFromSingleEquipmentAsync(equipment, request.ComponentIds);
                    result.Details.Add(itemResult);
                    
                    if (itemResult.IsSuccess)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                    }
                }

                result.IsSuccess = result.SuccessCount > 0;
                
                _logger.LogInformation("批量移除部件完成: 成功{success}, 失败{failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量移除部件失败");
                result.IsSuccess = false;
                result.ErrorMessage = $"操作失败: {ex.Message}";
                return result;
            }
        }

        public async Task<BatchOperationResultDto> BatchUpdateEquipmentComponentsAsync(BatchUpdateComponentsRequestDto request)
        {
            var result = new BatchOperationResultDto();
            
            try
            {
                _logger.LogInformation("开始批量更新部件配置: 设备数量{equipmentCount}, 部件数量{componentCount}", 
                    request.EquipmentIds.Count, request.ComponentConfigurations.Count);

                // 验证请求
                var validation = await ValidateBatchUpdateComponentsRequestAsync(request);
                if (!validation.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = string.Join("; ", validation.Errors.Select(e => e.ErrorMessage));
                    return result;
                }

                // 获取目标设备
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                result.TotalCount = equipments.Count;

                // 为每个设备更新部件配置
                foreach (var equipment in equipments)
                {
                    var itemResult = await UpdateComponentsForSingleEquipmentAsync(equipment, request.ComponentConfigurations);
                    result.Details.Add(itemResult);
                    
                    if (itemResult.IsSuccess)
                    {
                        result.SuccessCount++;
                    }
                    else
                    {
                        result.FailureCount++;
                    }
                }

                result.IsSuccess = result.SuccessCount > 0;
                
                _logger.LogInformation("批量更新部件配置完成: 成功{success}, 失败{failure}", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新部件配置失败");
                result.IsSuccess = false;
                result.ErrorMessage = $"操作失败: {ex.Message}";
                return result;
            }
        }

        public async Task<List<EquipmentComponentPreviewDto>> GetEquipmentComponentsPreviewAsync(List<int> equipmentIds)
        {
            try
            {
                var previews = new List<EquipmentComponentPreviewDto>();

                foreach (var equipmentId in equipmentIds)
                {
                    var equipment = await _dbContext.Db.Queryable<Equipment>()
                        .LeftJoin<EquipmentModel>((e, em) => e.ModelId == em.Id)
                        .Where(e => e.Id == equipmentId)
                        .Select((e, em) => new { Equipment = e, Model = em })
                        .FirstAsync();

                    if (equipment?.Equipment == null) continue;

                    var preview = new EquipmentComponentPreviewDto
                    {
                        EquipmentId = equipment.Equipment.Id,
                        EquipmentName = equipment.Equipment.Name,
                        EquipmentCode = equipment.Equipment.Code,
                        EquipmentModelName = equipment.Model?.Name ?? ""
                    };

                    // 获取设备的部件配置（从模板获取）
                    if (equipment.Equipment.ModelId > 0)
                    {
                        var components = await GetEquipmentComponentConfigurationsAsync(equipmentId);
                        preview.Components = components.Select(c => new ComponentConfigurationPreviewDto
                        {
                            ComponentId = c.ComponentId,
                            ComponentName = $"部件 {c.ComponentId}",
                            ComponentCode = $"COMP{c.ComponentId:D4}",
                            ComponentCategoryName = "未分类",
                            StandardQuantity = c.StandardQuantity,
                            IsRequired = c.IsRequired,
                            ReplacementCycleDays = c.ReplacementCycleDays,
                            MaintenanceNotes = c.MaintenanceNotes,
                            StockStatus = "正常"
                        }).ToList();
                    }

                    previews.Add(preview);
                }

                return previews;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备部件预览失败");
                throw;
            }
        }

        public async Task<List<TemplateOptionDto>> GetAvailableTemplatesAsync(List<int> equipmentIds)
        {
            try
            {
                // 获取设备的型号信息
                var equipments = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => equipmentIds.Contains(e.Id))
                    .ToListAsync();

                var equipmentModels = equipments
                    .GroupBy(e => e.ModelId)
                    .Select(g => new { ModelId = g.Key, Count = g.Count() })
                    .ToList();

                var templates = new List<TemplateOptionDto>();

                foreach (var modelGroup in equipmentModels.Where(m => m.ModelId > 0))
                {
                    var modelId = modelGroup.ModelId;

                    var model = await _dbContext.Db.Queryable<EquipmentModel>()
                        .Where(em => em.Id == modelId)
                        .FirstAsync();

                    if (model == null) continue;

                    var templateComponents = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                        .Where(t => t.EquipmentModelId == modelId && t.IsEnabled)
                        .ToListAsync();

                    var template = new TemplateOptionDto
                    {
                        EquipmentModelId = modelId,
                        EquipmentModelName = model.Name,
                        ComponentCount = templateComponents.Count,
                        RequiredComponentCount = templateComponents.Count(t => t.IsRequired),
                        OptionalComponentCount = templateComponents.Count(t => !t.IsRequired),
                        ApplicableEquipmentCount = modelGroup.Count,
                        Description = $"{model.Name} 标准配置模板"
                    };

                    templates.Add(template);
                }

                return templates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用模板失败");
                throw;
            }
        }

        public async Task<List<TemplateOptionDto>> GetAllAvailableTemplatesAsync()
        {
            try
            {
                // 获取所有有模板的设备型号
                var modelTemplates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .LeftJoin<EquipmentModel>((t, em) => t.EquipmentModelId == em.Id)
                    .Where((t, em) => t.IsEnabled && em.IsEnabled)
                    .GroupBy((t, em) => new { t.EquipmentModelId, em.Name })
                    .Select((t, em) => new
                    {
                        EquipmentModelId = t.EquipmentModelId,
                        EquipmentModelName = em.Name,
                        ComponentCount = SqlFunc.AggregateCount(t.Id)
                    })
                    .ToListAsync();

                var templates = new List<TemplateOptionDto>();

                foreach (var mt in modelTemplates)
                {
                    // 获取该型号的详细模板信息
                    var templateDetails = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                        .Where(t => t.EquipmentModelId == mt.EquipmentModelId && t.IsEnabled)
                        .ToListAsync();

                    var template = new TemplateOptionDto
                    {
                        EquipmentModelId = mt.EquipmentModelId,
                        EquipmentModelName = mt.EquipmentModelName,
                        ComponentCount = templateDetails.Count,
                        RequiredComponentCount = templateDetails.Count(t => t.IsRequired),
                        OptionalComponentCount = templateDetails.Count(t => !t.IsRequired),
                        ApplicableEquipmentCount = 0, // 不限制适用设备数量
                        Description = $"{mt.EquipmentModelName} 标准配置模板"
                    };

                    templates.Add(template);
                }

                return templates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有可用模板失败");
                throw;
            }
        }

        public async Task<List<EquipmentOptionDto>> GetCopyableEquipmentsAsync(List<int> targetEquipmentIds)
        {
            try
            {
                // 获取目标设备的型号信息
                var targetModels = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => targetEquipmentIds.Contains(e.Id))
                    .Select(e => e.ModelId)
                    .ToListAsync();

                // 获取相同型号的其他设备作为可复制选项
                var copyableEquipments = await _dbContext.Db.Queryable<Equipment>()
                    .LeftJoin<EquipmentModel>((e, em) => e.ModelId == em.Id)
                    .LeftJoin<Department>((e, em, d) => e.DepartmentId == d.Id)
                    .LeftJoin<Location>((e, em, d, l) => e.LocationId == l.Id)
                    .Where(e => targetModels.Contains(e.ModelId) && !targetEquipmentIds.Contains(e.Id) && e.IsEnabled)
                    .Select((e, em, d, l) => new EquipmentOptionDto
                    {
                        EquipmentId = e.Id,
                        EquipmentName = e.Name,
                        EquipmentCode = e.Code,
                        EquipmentModelName = em.Name,
                        DepartmentName = d.Name,
                        LocationName = l.Name,
                        Status = e.StatusName
                    })
                    .ToListAsync();

                // 获取每个设备的部件配置数量
                foreach (var equipment in copyableEquipments)
                {
                    var componentCount = await GetEquipmentComponentCountAsync(equipment.EquipmentId);
                    equipment.ComponentCount = componentCount;
                }

                return copyableEquipments.Where(e => e.ComponentCount > 0).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可复制设备失败");
                throw;
            }
        }

        public async Task<BatchValidationResultDto> ValidateBatchOperationAsync(object request)
        {
            try
            {
                return request switch
                {
                    ApplyTemplateToEquipmentsRequestDto applyRequest => await ValidateApplyTemplateRequestAsync(applyRequest),
                    CopyComponentsFromEquipmentRequestDto copyRequest => await ValidateCopyComponentsRequestAsync(copyRequest),
                    BatchAddComponentsRequestDto addRequest => await ValidateBatchAddComponentsRequestAsync(addRequest),
                    BatchRemoveComponentsRequestDto removeRequest => await ValidateBatchRemoveComponentsRequestAsync(removeRequest),
                    BatchUpdateComponentsRequestDto updateRequest => await ValidateBatchUpdateComponentsRequestAsync(updateRequest),
                    _ => new BatchValidationResultDto { IsValid = false, Errors = { new ValidationErrorDto { ErrorMessage = "不支持的请求类型" } } }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证批量操作失败");
                return new BatchValidationResultDto
                {
                    IsValid = false,
                    Errors = { new ValidationErrorDto { ErrorMessage = $"验证失败: {ex.Message}" } }
                };
            }
        }

        public async Task<BatchImpactPreviewDto> GetBatchOperationImpactAsync(object request)
        {
            try
            {
                return request switch
                {
                    ApplyTemplateToEquipmentsRequestDto applyRequest => await GetApplyTemplateImpactAsync(applyRequest),
                    CopyComponentsFromEquipmentRequestDto copyRequest => await GetCopyComponentsImpactAsync(copyRequest),
                    BatchAddComponentsRequestDto addRequest => await GetBatchAddImpactAsync(addRequest),
                    BatchRemoveComponentsRequestDto removeRequest => await GetBatchRemoveImpactAsync(removeRequest),
                    BatchUpdateComponentsRequestDto updateRequest => await GetBatchUpdateImpactAsync(updateRequest),
                    _ => new BatchImpactPreviewDto { OperationType = "未知操作" }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取批量操作影响预览失败");
                return new BatchImpactPreviewDto
                {
                    OperationType = "错误",
                    Summary = $"获取影响预览失败: {ex.Message}"
                };
            }
        }
    }
}
