using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 权限验证服务接口
    /// </summary>
    public interface IPermissionValidationService
    {
        /// <summary>
        /// 验证用户是否可以报修指定设备
        /// </summary>
        Task<PermissionValidationResult> ValidateEquipmentReportPermissionAsync(int userId, int equipmentId);

        /// <summary>
        /// 验证用户是否可以接收指定部门的报修
        /// </summary>
        Task<PermissionValidationResult> ValidateRepairReceivePermissionAsync(int userId, int departmentId);

        /// <summary>
        /// 验证用户是否可以维修指定设备
        /// </summary>
        Task<PermissionValidationResult> ValidateEquipmentMaintenancePermissionAsync(int userId, int equipmentId);

        /// <summary>
        /// 验证维修人员是否具备维修指定设备的技能
        /// </summary>
        Task<PermissionValidationResult> ValidateMaintenanceSkillsAsync(int personnelId, int equipmentModelId);

        /// <summary>
        /// 验证用户是否可以查看指定报修单
        /// </summary>
        Task<PermissionValidationResult> ValidateRepairOrderViewPermissionAsync(int userId, int repairOrderId);

        /// <summary>
        /// 验证用户是否可以操作指定报修单
        /// </summary>
        Task<PermissionValidationResult> ValidateRepairOrderOperationPermissionAsync(int userId, int repairOrderId, RepairOrderOperation operation);

        /// <summary>
        /// 获取用户的完整权限摘要
        /// </summary>
        Task<UserPermissionSummary> GetUserPermissionSummaryAsync(int userId);
    }

    /// <summary>
    /// 权限验证结果
    /// </summary>
    public class PermissionValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public string? WarningMessage { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();

        public static PermissionValidationResult Success() => new() { IsValid = true };
        public static PermissionValidationResult Failure(string errorMessage) => new() { IsValid = false, ErrorMessage = errorMessage };
        public static PermissionValidationResult Warning(string warningMessage) => new() { IsValid = true, WarningMessage = warningMessage };
    }

    /// <summary>
    /// 用户权限摘要
    /// </summary>
    public class UserPermissionSummary
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public int? DepartmentId { get; set; }
        public string? DepartmentName { get; set; }
        public List<string> Roles { get; set; } = new();
        public List<Department> ReportableDepartments { get; set; } = new();
        public List<Department> ReceivableDepartments { get; set; } = new();
        public List<Department> MaintainableDepartments { get; set; } = new();
        public bool IsMaintenancePersonnel { get; set; }
        public bool HasAnyReportPermission => ReportableDepartments.Any();
        public bool HasAnyReceivePermission => ReceivableDepartments.Any();
        public bool HasAnyMaintenancePermission => MaintainableDepartments.Any();
    }

    /// <summary>
    /// 报修单操作类型
    /// </summary>
    public enum RepairOrderOperation
    {
        /// <summary>
        /// 查看
        /// </summary>
        View,
        /// <summary>
        /// 编辑
        /// </summary>
        Edit,
        /// <summary>
        /// 分配
        /// </summary>
        Assign,
        /// <summary>
        /// 开始维修
        /// </summary>
        StartRepair,
        /// <summary>
        /// 完成维修
        /// </summary>
        CompleteRepair,
        /// <summary>
        /// 取消/作废
        /// </summary>
        Cancel,
        /// <summary>
        /// 评价
        /// </summary>
        Rate
    }
}
