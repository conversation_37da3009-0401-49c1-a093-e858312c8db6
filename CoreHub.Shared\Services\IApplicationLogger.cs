namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 应用程序日志服务接口
    /// </summary>
    public interface IApplicationLogger
    {
        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        void LogDebug(string message, params object[] args);

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        void LogInformation(string message, params object[] args);

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        void LogWarning(string message, params object[] args);

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        void LogError(Exception exception, string message, params object[] args);

        /// <summary>
        /// 记录严重错误
        /// </summary>
        /// <param name="exception">异常</param>
        /// <param name="message">消息</param>
        /// <param name="args">参数</param>
        void LogCritical(Exception exception, string message, params object[] args);

        /// <summary>
        /// 记录用户操作
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="action">操作</param>
        /// <param name="details">详细信息</param>
        void LogUserAction(string userId, string action, object? details = null);

        /// <summary>
        /// 记录性能指标
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="duration">持续时间（毫秒）</param>
        /// <param name="additionalData">附加数据</param>
        void LogPerformance(string operation, long duration, object? additionalData = null);

        /// <summary>
        /// 记录安全事件
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="userId">用户ID</param>
        /// <param name="details">详细信息</param>
        void LogSecurityEvent(string eventType, string? userId, object? details = null);
    }
}
