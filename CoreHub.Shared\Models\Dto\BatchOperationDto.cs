using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 批量操作结果DTO
    /// </summary>
    public class BatchOperationResultDto
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 成功处理的数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 详细结果列表
        /// </summary>
        public List<BatchOperationItemResultDto> Details { get; set; } = new List<BatchOperationItemResultDto>();

        /// <summary>
        /// 操作摘要
        /// </summary>
        public string Summary => $"总计 {TotalCount} 项，成功 {SuccessCount} 项，失败 {FailureCount} 项";
    }

    /// <summary>
    /// 批量操作单项结果DTO
    /// </summary>
    public class BatchOperationItemResultDto
    {
        /// <summary>
        /// 项目标识（如设备ID、部件ID等）
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 从设备型号模板应用到设备的请求DTO
    /// </summary>
    public class ApplyTemplateToEquipmentsRequestDto
    {
        /// <summary>
        /// 设备型号ID
        /// </summary>
        [Required(ErrorMessage = "设备型号不能为空")]
        public int EquipmentModelId { get; set; }

        /// <summary>
        /// 目标设备ID列表
        /// </summary>
        [Required(ErrorMessage = "目标设备不能为空")]
        public List<int> EquipmentIds { get; set; } = new List<int>();

        /// <summary>
        /// 是否覆盖已存在的部件配置
        /// </summary>
        public bool OverrideExisting { get; set; } = false;

        /// <summary>
        /// 只应用必需部件
        /// </summary>
        public bool OnlyRequiredComponents { get; set; } = false;

        /// <summary>
        /// 指定要应用的部件ID列表（为空则应用所有）
        /// </summary>
        public List<int> ComponentIds { get; set; } = new List<int>();

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 从源设备复制部件配置的请求DTO
    /// </summary>
    public class CopyComponentsFromEquipmentRequestDto
    {
        /// <summary>
        /// 源设备ID
        /// </summary>
        [Required(ErrorMessage = "源设备不能为空")]
        public int SourceEquipmentId { get; set; }

        /// <summary>
        /// 目标设备ID列表
        /// </summary>
        [Required(ErrorMessage = "目标设备不能为空")]
        public List<int> TargetEquipmentIds { get; set; } = new List<int>();

        /// <summary>
        /// 是否覆盖已存在的部件配置
        /// </summary>
        public bool OverrideExisting { get; set; } = false;

        /// <summary>
        /// 指定要复制的部件ID列表（为空则复制所有）
        /// </summary>
        public List<int> ComponentIds { get; set; } = new List<int>();

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 批量添加部件到设备的请求DTO
    /// </summary>
    public class BatchAddComponentsRequestDto
    {
        /// <summary>
        /// 目标设备ID列表
        /// </summary>
        [Required(ErrorMessage = "目标设备不能为空")]
        public List<int> EquipmentIds { get; set; } = new List<int>();

        /// <summary>
        /// 要添加的部件配置列表
        /// </summary>
        [Required(ErrorMessage = "部件配置不能为空")]
        public List<ComponentConfigurationDto> ComponentConfigurations { get; set; } = new List<ComponentConfigurationDto>();

        /// <summary>
        /// 是否覆盖已存在的部件配置
        /// </summary>
        public bool OverrideExisting { get; set; } = false;

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 批量移除部件的请求DTO
    /// </summary>
    public class BatchRemoveComponentsRequestDto
    {
        /// <summary>
        /// 目标设备ID列表
        /// </summary>
        [Required(ErrorMessage = "目标设备不能为空")]
        public List<int> EquipmentIds { get; set; } = new List<int>();

        /// <summary>
        /// 要移除的部件ID列表
        /// </summary>
        [Required(ErrorMessage = "部件列表不能为空")]
        public List<int> ComponentIds { get; set; } = new List<int>();

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 批量更新部件配置的请求DTO
    /// </summary>
    public class BatchUpdateComponentsRequestDto
    {
        /// <summary>
        /// 目标设备ID列表
        /// </summary>
        [Required(ErrorMessage = "目标设备不能为空")]
        public List<int> EquipmentIds { get; set; } = new List<int>();

        /// <summary>
        /// 要更新的部件配置列表
        /// </summary>
        [Required(ErrorMessage = "部件配置不能为空")]
        public List<ComponentConfigurationDto> ComponentConfigurations { get; set; } = new List<ComponentConfigurationDto>();

        /// <summary>
        /// 操作备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 部件配置DTO
    /// </summary>
    public class ComponentConfigurationDto
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        [Required(ErrorMessage = "部件ID不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 标准数量
        /// </summary>
        [Required(ErrorMessage = "标准数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "标准数量必须大于0")]
        public int StandardQuantity { get; set; } = 1;

        /// <summary>
        /// 是否必需部件
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 维护说明
        /// </summary>
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;
    }

    /// <summary>
    /// 设备部件配置预览DTO
    /// </summary>
    public class EquipmentComponentPreviewDto
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string EquipmentModelName { get; set; } = string.Empty;

        /// <summary>
        /// 当前部件配置列表
        /// </summary>
        public List<ComponentConfigurationPreviewDto> Components { get; set; } = new List<ComponentConfigurationPreviewDto>();

        /// <summary>
        /// 部件总数
        /// </summary>
        public int ComponentCount => Components.Count;

        /// <summary>
        /// 必需部件数量
        /// </summary>
        public int RequiredComponentCount => Components.Count(c => c.IsRequired);
    }

    /// <summary>
    /// 部件配置预览DTO
    /// </summary>
    public class ComponentConfigurationPreviewDto
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int ComponentId { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 部件编码
        /// </summary>
        public string ComponentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类名称
        /// </summary>
        public string ComponentCategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 标准数量
        /// </summary>
        public int StandardQuantity { get; set; }

        /// <summary>
        /// 是否必需部件
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 维护说明
        /// </summary>
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string StockStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// 模板选项DTO
    /// </summary>
    public class TemplateOptionDto
    {
        /// <summary>
        /// 设备型号ID
        /// </summary>
        public int EquipmentModelId { get; set; }

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string EquipmentModelName { get; set; } = string.Empty;

        /// <summary>
        /// 模板部件数量
        /// </summary>
        public int ComponentCount { get; set; }

        /// <summary>
        /// 必需部件数量
        /// </summary>
        public int RequiredComponentCount { get; set; }

        /// <summary>
        /// 可选部件数量
        /// </summary>
        public int OptionalComponentCount { get; set; }

        /// <summary>
        /// 适用设备数量
        /// </summary>
        public int ApplicableEquipmentCount { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 设备选项DTO
    /// </summary>
    public class EquipmentOptionDto
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; } = string.Empty;

        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 设备型号名称
        /// </summary>
        public string EquipmentModelName { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 位置名称
        /// </summary>
        public string LocationName { get; set; } = string.Empty;

        /// <summary>
        /// 部件配置数量
        /// </summary>
        public int ComponentCount { get; set; }

        /// <summary>
        /// 设备状态
        /// </summary>
        public string Status { get; set; } = string.Empty;
    }
}
