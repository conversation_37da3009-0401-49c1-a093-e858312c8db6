using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Utils;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 维修仪表板服务实现
    /// </summary>
    public class MaintenanceDashboardService : IMaintenanceDashboardService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<MaintenanceDashboardService> _logger;
        private readonly IRepairOrderService _repairOrderService;
        private readonly IRoleDepartmentAssignmentServiceV2 _roleDepartmentAssignmentService;
        private readonly IJobTypeService _jobTypeService;
        public MaintenanceDashboardService(
            DatabaseContext dbContext,
            ILogger<MaintenanceDashboardService> logger,
            IRepairOrderService repairOrderService,
            IRoleDepartmentAssignmentServiceV2 roleDepartmentAssignmentService,
            IJobTypeService jobTypeService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _repairOrderService = repairOrderService;
            _roleDepartmentAssignmentService = roleDepartmentAssignmentService;
            _jobTypeService = jobTypeService;
        }

        public async Task<MaintenanceDashboardData> GetDashboardDataAsync(int userId)
        {
            try
            {
                var dashboardData = new MaintenanceDashboardData();

                // 获取用户可见的报修单
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);

                // 统计各状态数量
                dashboardData.TotalPending = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Pending);
                dashboardData.TotalInProgress = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.InProgress);
                dashboardData.TotalCompleted = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Completed);
                dashboardData.MyAssignedCount = visibleRepairOrders.Count(ro => ro.AssignedTo == userId);
                dashboardData.UrgentCount = visibleRepairOrders.Count(ro => ro.UrgencyLevel == 1);

                // 计算超时报修单
                var overdueOrders = await GetOverdueRepairOrdersAsync(userId);
                dashboardData.TotalOverdue = overdueOrders.Count;

                // 获取最近的报修单（最多10条）
                dashboardData.RecentRepairOrders = visibleRepairOrders
                    .OrderByDescending(ro => ro.ReportedAt)
                    .Take(10)
                    .ToList();

                // 获取技术员工作负载
                var maintainableDepartments = await _roleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);
                if (maintainableDepartments.Any())
                {
                    var departmentId = maintainableDepartments.First().Id;
                    dashboardData.TechnicianWorkloads = await GetTechnicianWorkloadAsync(departmentId);
                }

                return dashboardData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取仪表板数据失败: {userId}", userId);
                return new MaintenanceDashboardData();
            }
        }

        public async Task<List<TechnicianWorkloadDto>> GetTechnicianWorkloadAsync(int departmentId)
        {
            try
            {
                // 直接从维修部门下面具有维修工种的用户中获取
                var maintenanceUsers = await _jobTypeService.GetMaintenanceUsersByDepartmentAsync(departmentId);
                var workloads = new List<TechnicianWorkloadDto>();

                foreach (var user in maintenanceUsers)
                {
                    var assignedOrders = await _repairOrderService.GetRepairOrdersByAssignedTechnicianAsync(user.Id);

                    var workload = new TechnicianWorkloadDto
                    {
                        UserId = user.Id,
                        TechnicianName = user.DisplayName,
                        AssignedCount = assignedOrders.Count(ro => ro.Status != RepairOrderStatusHelper.Completed &&
                                                                   ro.Status != RepairOrderStatusHelper.Cancelled &&
                                                                   ro.Status != RepairOrderStatusHelper.Closed),
                        InProgressCount = assignedOrders.Count(ro => ro.Status == RepairOrderStatusHelper.InProgress),
                        CompletedThisWeek = assignedOrders.Count(ro =>
                            ro.Status == RepairOrderStatusHelper.Completed &&
                            ro.CompletedAt.HasValue &&
                            ro.CompletedAt.Value >= DateTime.Now.AddDays(-7)),
                        IsAvailable = true, // 默认可用
                        Level = "维修人员",
                        Specialties = "维修"
                    };

                    workloads.Add(workload);
                }

                return workloads;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取技术员工作负载失败: {departmentId}", departmentId);
                return new List<TechnicianWorkloadDto>();
            }
        }

        public async Task<RepairOrderStatusStatistics> GetRepairOrderStatusStatisticsAsync(int userId)
        {
            try
            {
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);

                return new RepairOrderStatusStatistics
                {
                    PendingCount = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Pending),
                    InProgressCount = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.InProgress),
                    CompletedCount = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Completed),
                    CancelledCount = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Cancelled),
                    ClosedCount = visibleRepairOrders.Count(ro => ro.Status == RepairOrderStatusHelper.Closed)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取状态统计失败: {userId}", userId);
                return new RepairOrderStatusStatistics();
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetUrgentRepairOrdersAsync(int userId)
        {
            try
            {
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);
                return visibleRepairOrders
                    .Where(ro => ro.UrgencyLevel == 1 &&
                                ro.Status != RepairOrderStatusHelper.Completed &&
                                ro.Status != RepairOrderStatusHelper.Cancelled &&
                                ro.Status != RepairOrderStatusHelper.Closed)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取紧急报修单失败: {userId}", userId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> FilterRepairOrdersAsync(int userId, RepairOrderFilterDto filter)
        {
            try
            {
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);
                var filteredOrders = visibleRepairOrders.AsQueryable();

                if (filter.Status.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.Status == filter.Status.Value);

                if (filter.UrgencyLevel.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.UrgencyLevel == filter.UrgencyLevel.Value);

                if (filter.AssignedTo.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.AssignedTo == filter.AssignedTo.Value);

                if (filter.MaintenanceDepartmentId.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.MaintenanceDepartmentId == filter.MaintenanceDepartmentId.Value);

                if (filter.StartDate.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.ReportedAt >= filter.StartDate.Value);

                if (filter.EndDate.HasValue)
                    filteredOrders = filteredOrders.Where(ro => ro.ReportedAt <= filter.EndDate.Value);

                if (filter.IsUnassigned == true)
                    filteredOrders = filteredOrders.Where(ro => ro.AssignedTo == null);

                if (filter.IsOverdue == true)
                {
                    var overdueThreshold = DateTime.Now.AddHours(-24); // 24小时未处理视为超时
                    filteredOrders = filteredOrders.Where(ro =>
                        ro.Status == RepairOrderStatusHelper.Pending && ro.ReportedAt < overdueThreshold);
                }

                return filteredOrders.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "过滤报修单失败: {userId}", userId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> SearchRepairOrdersAsync(int userId, string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);

                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);
                var searchTermLower = searchTerm.ToLower();

                return visibleRepairOrders.Where(ro =>
                    ro.OrderNumber.ToLower().Contains(searchTermLower) ||
                    ro.EquipmentName.ToLower().Contains(searchTermLower) ||
                    ro.EquipmentCode.ToLower().Contains(searchTermLower) ||
                    ro.FaultDescription.ToLower().Contains(searchTermLower) ||
                    (ro.ReporterName?.ToLower().Contains(searchTermLower) ?? false) ||
                    (ro.AssignedToName?.ToLower().Contains(searchTermLower) ?? false)
                ).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索报修单失败: {userId}, {searchTerm}", userId, searchTerm);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> BatchAssignRepairOrdersAsync(List<int> repairOrderIds, int technicianId, int assignedByUserId)
        {
            try
            {
                var successCount = 0;
                var errors = new List<string>();

                foreach (var repairOrderId in repairOrderIds)
                {
                    // 检查分配权限
                    if (!await _repairOrderService.CanUserAssignRepairOrderAsync(assignedByUserId, repairOrderId))
                    {
                        errors.Add($"报修单 {repairOrderId}: 无分配权限");
                        continue;
                    }

                    var result = await _repairOrderService.AssignRepairOrderAsync(repairOrderId, technicianId);
                    if (result.IsSuccess)
                    {
                        successCount++;
                    }
                    else
                    {
                        errors.Add($"报修单 {repairOrderId}: {result.ErrorMessage}");
                    }
                }

                if (successCount == repairOrderIds.Count)
                {
                    return (true, $"成功分配 {successCount} 个报修单");
                }
                else if (successCount > 0)
                {
                    return (true, $"成功分配 {successCount} 个报修单，{errors.Count} 个失败");
                }
                else
                {
                    return (false, string.Join("; ", errors));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量分配报修单失败");
                return (false, $"批量分配失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ReassignRepairOrderAsync(int repairOrderId, int newTechnicianId, int assignedByUserId)
        {
            try
            {
                // 检查分配权限
                if (!await _repairOrderService.CanUserAssignRepairOrderAsync(assignedByUserId, repairOrderId))
                {
                    return (false, "无重新分配权限");
                }

                return await _repairOrderService.AssignRepairOrderAsync(repairOrderId, newTechnicianId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新分配报修单失败: {repairOrderId}", repairOrderId);
                return (false, $"重新分配失败: {ex.Message}");
            }
        }

        public async Task<List<User>> GetRecommendedTechniciansAsync(int repairOrderId)
        {
            try
            {
                // 获取可用技术员
                var availableTechnicians = await _repairOrderService.GetAvailableTechniciansForRepairOrderAsync(repairOrderId);

                // 获取技术员工作负载
                var workloads = new Dictionary<int, int>();
                foreach (var technician in availableTechnicians)
                {
                    var assignedOrders = await _repairOrderService.GetRepairOrdersByAssignedTechnicianAsync(technician.Id);
                    workloads[technician.Id] = assignedOrders.Count(ro => ro.Status != RepairOrderStatusHelper.Completed &&
                                                                          ro.Status != RepairOrderStatusHelper.Cancelled &&
                                                                          ro.Status != RepairOrderStatusHelper.Closed);
                }

                // 按工作负载排序推荐
                return availableTechnicians
                    .OrderBy(t => workloads.GetValueOrDefault(t.Id, 0))
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取推荐技术员失败: {repairOrderId}", repairOrderId);
                return new List<User>();
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateRepairOrderPriorityAsync(int repairOrderId, int urgencyLevel, int updatedByUserId)
        {
            try
            {
                // 检查管理权限
                if (!await _repairOrderService.CanUserManageRepairOrderAsync(updatedByUserId, repairOrderId))
                {
                    return (false, "无更新优先级权限");
                }

                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                repairOrder.UrgencyLevel = urgencyLevel;
                repairOrder.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(repairOrder)
                    .UpdateColumns(ro => new { ro.UrgencyLevel, ro.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    return (true, "优先级更新成功");
                }
                else
                {
                    return (false, "更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新报修单优先级失败: {repairOrderId}", repairOrderId);
                return (false, $"更新失败: {ex.Message}");
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetAttentionRequiredRepairOrdersAsync(int userId)
        {
            try
            {
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);
                var attentionRequired = new List<RepairOrderDetailDto>();

                // 紧急且未分配的报修单
                attentionRequired.AddRange(visibleRepairOrders.Where(ro =>
                    ro.UrgencyLevel == 1 && ro.AssignedTo == null && ro.Status == RepairOrderStatusHelper.Pending));

                // 超过24小时未处理的报修单
                var overdueThreshold = DateTime.Now.AddHours(-24);
                attentionRequired.AddRange(visibleRepairOrders.Where(ro =>
                    ro.Status == RepairOrderStatusHelper.Pending && ro.ReportedAt < overdueThreshold));

                // 处理中超过48小时的报修单
                var longProcessingThreshold = DateTime.Now.AddHours(-48);
                attentionRequired.AddRange(visibleRepairOrders.Where(ro =>
                    ro.Status == RepairOrderStatusHelper.InProgress && ro.StartedAt.HasValue && ro.StartedAt.Value < longProcessingThreshold));

                return attentionRequired.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取需要关注的报修单失败: {userId}", userId);
                return new List<RepairOrderDetailDto>();
            }
        }

        public async Task<List<RepairOrderDetailDto>> GetOverdueRepairOrdersAsync(int userId)
        {
            try
            {
                var visibleRepairOrders = await _repairOrderService.GetMaintenanceVisibleRepairOrdersAsync(userId);
                var overdueThreshold = DateTime.Now.AddHours(-24);

                return visibleRepairOrders.Where(ro =>
                    (ro.Status == RepairOrderStatusHelper.Pending || ro.Status == RepairOrderStatusHelper.InProgress) &&
                    ro.ReportedAt < overdueThreshold).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取超时报修单失败: {userId}", userId);
                return new List<RepairOrderDetailDto>();
            }
        }
    }
}
