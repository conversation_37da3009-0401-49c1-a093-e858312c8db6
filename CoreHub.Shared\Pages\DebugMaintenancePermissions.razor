@page "/debug-maintenance-permissions"
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject ISnackbar Snackbar

<div>
    <MudText Typo="Typo.h4" Class="mb-4">调试维修部门权限</MudText>

    <MudGrid>
        <MudItem xs="12" md="6">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">所有部门</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (allDepartments.Any())
                    {
                        @foreach (var dept in allDepartments)
                        {
                            <MudText Typo="Typo.body2">
                                ID: @dept.Id, 代码: @dept.Code, 名称: @dept.Name, 
                                类型ID: @dept.DepartmentTypeId, 类型: @dept.DepartmentType?.Name
                            </MudText>
                        }
                    }
                    else
                    {
                        <MudText>没有部门数据</MudText>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">维修部门权限</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (permissions.Any())
                    {
                        @foreach (var perm in permissions)
                        {
                            <MudText Typo="Typo.body2">
                                维修部门: @perm.MaintenanceDepartment?.Name (@perm.MaintenanceDepartmentId) 
                                -> 目标部门: @perm.TargetDepartment?.Name (@perm.TargetDepartmentId)
                                启用: @perm.IsEnabled
                            </MudText>
                        }
                    }
                    else
                    {
                        <MudText Color="Color.Warning">没有维修部门权限数据</MudText>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">测试查询</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudSelect T="int?" @bind-Value="selectedDepartmentId" Label="选择部门">
                        @foreach (var dept in allDepartments)
                        {
                            <MudSelectItem T="int?" Value="dept.Id">@dept.Name (@dept.Code)</MudSelectItem>
                        }
                    </MudSelect>
                    
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" 
                               OnClick="TestQuery" Class="mt-2">
                        测试查询可用维修部门
                    </MudButton>

                    @if (testResult.Any())
                    {
                        <MudText Typo="Typo.h6" Class="mt-4">查询结果:</MudText>
                        @foreach (var dept in testResult)
                        {
                            <MudText Typo="Typo.body2">@dept.Name (@dept.Code)</MudText>
                        }
                    }
                    else if (hasTestedQuery)
                    {
                        <MudText Color="Color.Warning" Class="mt-4">没有找到可用的维修部门</MudText>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</div>

@code {
    private List<Department> allDepartments = new();
    private List<MaintenanceDepartmentPermission> permissions = new();
    private List<Department> testResult = new();
    private int? selectedDepartmentId;
    private bool hasTestedQuery = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            // 加载所有部门
            allDepartments = await DepartmentService.GetAllDepartmentsAsync();
            
            // 加载部门类型信息
            foreach (var dept in allDepartments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            // 加载维修部门权限
            permissions = await MaintenanceDepartmentPermissionService.GetAllMaintenanceDepartmentPermissionsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task TestQuery()
    {
        if (!selectedDepartmentId.HasValue) return;

        try
        {
            hasTestedQuery = true;
            testResult = await MaintenanceDepartmentPermissionService.GetAvailableMaintenanceDepartmentsAsync(selectedDepartmentId.Value);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"查询失败: {ex.Message}", Severity.Error);
        }
    }
}
