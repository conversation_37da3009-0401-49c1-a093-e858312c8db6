using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备型号标准部件模板服务接口
    /// </summary>
    public interface IEquipmentModelComponentTemplateService
    {
        /// <summary>
        /// 获取所有模板
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> GetAllTemplatesAsync();

        /// <summary>
        /// 根据ID获取模板
        /// </summary>
        Task<EquipmentModelComponentTemplate?> GetTemplateByIdAsync(int id);

        /// <summary>
        /// 根据设备型号获取模板
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> GetTemplatesByEquipmentModelAsync(int equipmentModelId);

        /// <summary>
        /// 根据部件获取模板
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> GetTemplatesByComponentAsync(int componentId);

        /// <summary>
        /// 创建模板
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateTemplateAsync(EquipmentModelComponentTemplate template);

        /// <summary>
        /// 更新模板
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateTemplateAsync(EquipmentModelComponentTemplate template);

        /// <summary>
        /// 删除模板
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteTemplateAsync(int id);

        /// <summary>
        /// 批量删除模板
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteTemplatesAsync(List<int> ids);

        /// <summary>
        /// 切换模板状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 复制模板到其他设备型号
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CopyTemplatesToEquipmentModelAsync(int sourceModelId, int targetModelId);

        /// <summary>
        /// 批量应用模板到设备
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ApplyTemplatesToEquipmentsAsync(int equipmentModelId, List<int> equipmentIds);

        /// <summary>
        /// 检查模板是否已存在
        /// </summary>
        Task<bool> IsTemplateExistsAsync(int equipmentModelId, int componentId, int? excludeId = null);

        /// <summary>
        /// 获取设备型号的必需部件列表
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> GetRequiredComponentsAsync(int equipmentModelId);

        /// <summary>
        /// 获取设备型号的可选部件列表
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> GetOptionalComponentsAsync(int equipmentModelId);

        /// <summary>
        /// 导入模板数据
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage, int ImportedCount)> ImportTemplatesAsync(List<EquipmentModelComponentTemplate> templates);

        /// <summary>
        /// 导出模板数据
        /// </summary>
        Task<List<EquipmentModelComponentTemplateDto>> ExportTemplatesAsync(int? equipmentModelId = null);
    }
}
