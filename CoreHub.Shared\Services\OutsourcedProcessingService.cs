using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 委外加工服务
    /// </summary>
    public class OutsourcedProcessingService
    {
        private readonly DatabaseContext _dbContext;

        public OutsourcedProcessingService(DatabaseContext dbContext)
        {
            _dbContext = dbContext;
        }

        #region 委外加工记录管理

        /// <summary>
        /// 创建委外加工记录
        /// </summary>
        /// <param name="partRequestId">零件申请ID</param>
        /// <param name="dto">委外加工信息</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>创建结果</returns>
        public async Task<(bool Success, string Message, int Id)> CreateOutsourcedProcessingAsync(
            int partRequestId, OutsourcedProcessingDto dto, int currentUserId)
        {
            try
            {
                // 检查零件申请是否存在
                var partRequest = await _dbContext.Db.Queryable<RepairOrderPartRequest>()
                    .FirstAsync(x => x.Id == partRequestId);

                if (partRequest == null)
                {
                    return (false, "零件申请记录不存在", 0);
                }

                // 检查是否已存在委外加工记录
                var existingRecord = await _dbContext.Db.Queryable<OutsourcedProcessingRecord>()
                    .FirstAsync(x => x.PartRequestId == partRequestId);

                if (existingRecord != null)
                {
                    return (false, "该零件申请已存在委外加工记录", 0);
                }

                // 开始事务
                await _dbContext.Db.Ado.BeginTranAsync();

                try
                {
                    // 创建委外加工记录
                    var record = new OutsourcedProcessingRecord
                    {
                        PartRequestId = partRequestId,
                        SupplierName = dto.SupplierName,
                        SupplierContact = dto.SupplierContact,
                        SupplierPhone = dto.SupplierPhone,
                        ProcessingRequirements = dto.ProcessingRequirements,
                        EstimatedCompletionDate = dto.EstimatedCompletionDate,
                        ProcessingCost = dto.ProcessingCost,
                        ProcessingStatus = 1, // 已下单
                        CreatedAt = DateTime.Now,
                        CreatedBy = currentUserId,
                        Remark = dto.Remark
                    };

                    var recordId = await _dbContext.Db.Insertable(record).ExecuteReturnIdentityAsync();

                    await _dbContext.Db.Ado.CommitTranAsync();

                    return (true, "委外加工记录创建成功", recordId);
                }
                catch
                {
                    await _dbContext.Db.Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return (false, $"创建委外加工记录失败：{ex.Message}", 0);
            }
        }

        /// <summary>
        /// 获取委外加工记录详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>委外加工记录</returns>
        public async Task<OutsourcedProcessingDto?> GetOutsourcedProcessingAsync(int id)
        {
            try
            {
                var record = await _dbContext.Db.Queryable<OutsourcedProcessingRecord>()
                    .LeftJoin<RepairOrderPartRequest>((opr, rpr) => opr.PartRequestId == rpr.Id)
                    .LeftJoin<RepairOrder>((opr, rpr, ro) => rpr.RepairOrderId == ro.Id)
                    .LeftJoin<User>((opr, rpr, ro, u1) => opr.CreatedBy == u1.Id)
                    .LeftJoin<User>((opr, rpr, ro, u1, u2) => opr.UpdatedBy == u2.Id)
                    .Where((opr, rpr, ro, u1, u2) => opr.Id == id)
                    .Select((opr, rpr, ro, u1, u2) => new OutsourcedProcessingDto
                    {
                        Id = opr.Id,
                        PartRequestId = opr.PartRequestId,
                        PartName = rpr.PartName,
                        Specification = rpr.Specification,
                        RequestedQuantity = rpr.RequestedQuantity,
                        Unit = rpr.Unit,
                        RepairOrderNo = ro.OrderNumber,
                        SupplierName = opr.SupplierName,
                        SupplierContact = opr.SupplierContact,
                        SupplierPhone = opr.SupplierPhone,
                        ProcessingRequirements = opr.ProcessingRequirements,
                        EstimatedCompletionDate = opr.EstimatedCompletionDate,
                        ActualCompletionDate = opr.ActualCompletionDate,
                        ProcessingCost = opr.ProcessingCost,
                        ProcessingStatus = opr.ProcessingStatus,
                        CreatedAt = opr.CreatedAt,
                        CreatedBy = opr.CreatedBy,
                        CreatedByName = u1.DisplayName,
                        UpdatedAt = opr.UpdatedAt,
                        UpdatedBy = opr.UpdatedBy,
                        UpdatedByName = u2.DisplayName,
                        Remark = opr.Remark
                    })
                    .FirstAsync();

                return record;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 获取零件申请的委外加工记录
        /// </summary>
        /// <param name="partRequestId">零件申请ID</param>
        /// <returns>委外加工记录</returns>
        public async Task<OutsourcedProcessingDto?> GetOutsourcedProcessingByPartRequestAsync(int partRequestId)
        {
            try
            {
                var record = await _dbContext.Db.Queryable<OutsourcedProcessingRecord>()
                    .Where(x => x.PartRequestId == partRequestId)
                    .FirstAsync();

                if (record != null)
                {
                    return await GetOutsourcedProcessingAsync(record.Id);
                }

                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 更新委外加工状态
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <param name="status">新状态</param>
        /// <param name="remark">更新备注</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>更新结果</returns>
        public async Task<(bool Success, string Message)> UpdateProcessingStatusAsync(
            int id, int status, string? remark, int currentUserId)
        {
            try
            {
                var record = await _dbContext.Db.Queryable<OutsourcedProcessingRecord>()
                    .FirstAsync(x => x.Id == id);

                if (record == null)
                {
                    return (false, "委外加工记录不存在");
                }

                // 开始事务
                await _dbContext.Db.Ado.BeginTranAsync();

                try
                {
                    var oldStatus = record.ProcessingStatus;

                    // 更新委外加工记录状态
                    if (status == 4) // 已完成状态
                    {
                        // 当状态更新为"已完成"时，使用数据库当前时间设置实际完成时间
                        await _dbContext.Db.Updateable<OutsourcedProcessingRecord>()
                            .SetColumns(x => new OutsourcedProcessingRecord
                            {
                                ProcessingStatus = status,
                                ActualCompletionDate = DateTime.Now, // 使用当前时间
                                UpdatedAt = DateTime.Now,
                                UpdatedBy = currentUserId
                            })
                            .Where(x => x.Id == id)
                            .ExecuteCommandAsync();
                    }
                    else
                    {
                        // 其他状态更新，清空实际完成时间
                        await _dbContext.Db.Updateable<OutsourcedProcessingRecord>()
                            .SetColumns(x => new OutsourcedProcessingRecord
                            {
                                ProcessingStatus = status,
                                ActualCompletionDate = null,
                                UpdatedAt = DateTime.Now,
                                UpdatedBy = currentUserId
                            })
                            .Where(x => x.Id == id)
                            .ExecuteCommandAsync();
                    }

                    // 记录状态变更日志
                    var statusLog = new OutsourcedProcessingStatusLog
                    {
                        ProcessingRecordId = id,
                        FromStatus = oldStatus,
                        ToStatus = status,
                        UpdatedBy = currentUserId,
                        UpdatedAt = DateTime.Now,
                        Remark = remark
                    };

                    await _dbContext.Db.Insertable(statusLog).ExecuteCommandAsync();

                    await _dbContext.Db.Ado.CommitTranAsync();

                    return (true, "状态更新成功");
                }
                catch
                {
                    await _dbContext.Db.Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                return (false, $"状态更新失败：{ex.Message}");
            }
        }

        #endregion
    }
}