using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备型号标准部件模板服务实现
    /// </summary>
    public class EquipmentModelComponentTemplateService : IEquipmentModelComponentTemplateService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentModelComponentTemplateService> _logger;

        public EquipmentModelComponentTemplateService(
            DatabaseContext dbContext,
            ILogger<EquipmentModelComponentTemplateService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> GetAllTemplatesAsync()
        {
            try
            {
                var templates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .LeftJoin<EquipmentModel>((t, em) => t.EquipmentModelId == em.Id)
                    .LeftJoin<Component>((t, em, c) => t.ComponentId == c.Id)
                    .LeftJoin<ComponentCategory>((t, em, c, cc) => c.CategoryId == cc.Id)
                    .Select((t, em, c, cc) => new EquipmentModelComponentTemplateDto
                    {
                        Id = t.Id,
                        EquipmentModelId = t.EquipmentModelId,
                        EquipmentModelName = em.Name,
                        ComponentId = t.ComponentId,
                        ComponentCode = c.Code,
                        ComponentName = c.Name,
                        ComponentCategoryName = cc.Name,
                        StandardQuantity = t.StandardQuantity,
                        IsRequired = t.IsRequired,
                        ReplacementCycleDays = t.ReplacementCycleDays,
                        MaintenanceNotes = t.MaintenanceNotes,
                        SortOrder = t.SortOrder,
                        IsEnabled = t.IsEnabled,
                        ComponentUnit = c.Unit,
                        ComponentStockQuantity = c.StockQuantity,
                        ComponentStockStatus = c.StockQuantity <= 0 ? "缺货" : 
                                             c.StockQuantity <= c.MinStockQuantity ? "库存不足" : "库存充足",
                        CreatedAt = t.CreatedAt,
                        Remark = t.Remark
                    })
                    .ToListAsync();

                // 设置计算属性
                foreach (var template in templates)
                {
                    template.ComponentTypeName = template.IsRequired ? "必需部件" : "可选部件";
                    template.ReplacementCycleDescription = GetReplacementCycleDescription(template.ReplacementCycleDays);
                }

                return templates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有模板失败");
                throw;
            }
        }

        public async Task<EquipmentModelComponentTemplate?> GetTemplateByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .Where(t => t.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取模板失败: {id}", id);
                throw;
            }
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> GetTemplatesByEquipmentModelAsync(int equipmentModelId)
        {
            try
            {
                var templates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .LeftJoin<EquipmentModel>((t, em) => t.EquipmentModelId == em.Id)
                    .LeftJoin<Component>((t, em, c) => t.ComponentId == c.Id)
                    .LeftJoin<ComponentCategory>((t, em, c, cc) => c.CategoryId == cc.Id)
                    .Where(t => t.EquipmentModelId == equipmentModelId && t.IsEnabled)
                    .Select((t, em, c, cc) => new EquipmentModelComponentTemplateDto
                    {
                        Id = t.Id,
                        EquipmentModelId = t.EquipmentModelId,
                        EquipmentModelName = em.Name,
                        ComponentId = t.ComponentId,
                        ComponentCode = c.Code,
                        ComponentName = c.Name,
                        ComponentCategoryName = cc.Name,
                        StandardQuantity = t.StandardQuantity,
                        IsRequired = t.IsRequired,
                        ReplacementCycleDays = t.ReplacementCycleDays,
                        MaintenanceNotes = t.MaintenanceNotes,
                        SortOrder = t.SortOrder,
                        IsEnabled = t.IsEnabled,
                        ComponentUnit = c.Unit,
                        ComponentStockQuantity = c.StockQuantity,
                        ComponentStockStatus = c.StockQuantity <= 0 ? "缺货" : 
                                             c.StockQuantity <= c.MinStockQuantity ? "库存不足" : "库存充足",
                        CreatedAt = t.CreatedAt,
                        Remark = t.Remark
                    })
                    .ToListAsync();

                // 设置计算属性
                foreach (var template in templates)
                {
                    template.ComponentTypeName = template.IsRequired ? "必需部件" : "可选部件";
                    template.ReplacementCycleDescription = GetReplacementCycleDescription(template.ReplacementCycleDays);
                }

                return templates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据设备型号获取模板失败: {equipmentModelId}", equipmentModelId);
                throw;
            }
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> GetTemplatesByComponentAsync(int componentId)
        {
            try
            {
                var templates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .LeftJoin<EquipmentModel>((t, em) => t.EquipmentModelId == em.Id)
                    .LeftJoin<Component>((t, em, c) => t.ComponentId == c.Id)
                    .LeftJoin<ComponentCategory>((t, em, c, cc) => c.CategoryId == cc.Id)
                    .Where(t => t.ComponentId == componentId && t.IsEnabled)
                    .Select((t, em, c, cc) => new EquipmentModelComponentTemplateDto
                    {
                        Id = t.Id,
                        EquipmentModelId = t.EquipmentModelId,
                        EquipmentModelName = em.Name,
                        ComponentId = t.ComponentId,
                        ComponentCode = c.Code,
                        ComponentName = c.Name,
                        ComponentCategoryName = cc.Name,
                        StandardQuantity = t.StandardQuantity,
                        IsRequired = t.IsRequired,
                        ReplacementCycleDays = t.ReplacementCycleDays,
                        MaintenanceNotes = t.MaintenanceNotes,
                        SortOrder = t.SortOrder,
                        IsEnabled = t.IsEnabled,
                        ComponentUnit = c.Unit,
                        ComponentStockQuantity = c.StockQuantity,
                        ComponentStockStatus = c.StockQuantity <= 0 ? "缺货" : 
                                             c.StockQuantity <= c.MinStockQuantity ? "库存不足" : "库存充足",
                        CreatedAt = t.CreatedAt,
                        Remark = t.Remark
                    })
                    .ToListAsync();

                // 设置计算属性
                foreach (var template in templates)
                {
                    template.ComponentTypeName = template.IsRequired ? "必需部件" : "可选部件";
                    template.ReplacementCycleDescription = GetReplacementCycleDescription(template.ReplacementCycleDays);
                }

                return templates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据部件获取模板失败: {componentId}", componentId);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateTemplateAsync(EquipmentModelComponentTemplate template)
        {
            try
            {
                // 检查模板是否已存在
                if (await IsTemplateExistsAsync(template.EquipmentModelId, template.ComponentId))
                {
                    return (false, "该设备型号已存在相同的部件模板");
                }

                // 验证设备型号是否存在
                var equipmentModel = await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Id == template.EquipmentModelId && em.IsEnabled)
                    .FirstAsync();
                if (equipmentModel == null)
                {
                    return (false, "设备型号不存在或已禁用");
                }

                // 验证部件是否存在
                var component = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == template.ComponentId && c.IsEnabled)
                    .FirstAsync();
                if (component == null)
                {
                    return (false, "部件不存在或已禁用");
                }

                template.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(template).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建模板成功: 设备型号{modelId} - 部件{componentId}", template.EquipmentModelId, template.ComponentId);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建模板失败: 设备型号{modelId} - 部件{componentId}", template.EquipmentModelId, template.ComponentId);
                return (false, $"创建模板失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateTemplateAsync(EquipmentModelComponentTemplate template)
        {
            try
            {
                // 检查模板是否已存在（排除自己）
                if (await IsTemplateExistsAsync(template.EquipmentModelId, template.ComponentId, template.Id))
                {
                    return (false, "该设备型号已存在相同的部件模板");
                }

                // 验证设备型号是否存在
                var equipmentModel = await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Id == template.EquipmentModelId && em.IsEnabled)
                    .FirstAsync();
                if (equipmentModel == null)
                {
                    return (false, "设备型号不存在或已禁用");
                }

                // 验证部件是否存在
                var component = await _dbContext.Db.Queryable<Component>()
                    .Where(c => c.Id == template.ComponentId && c.IsEnabled)
                    .FirstAsync();
                if (component == null)
                {
                    return (false, "部件不存在或已禁用");
                }

                template.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(template).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新模板成功: {id}", template.Id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "模板不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新模板失败: {id}", template.Id);
                return (false, $"更新模板失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteTemplateAsync(int id)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<EquipmentModelComponentTemplate>()
                    .Where(t => t.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除模板成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "模板不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除模板失败: {id}", id);
                return (false, $"删除模板失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> BatchDeleteTemplatesAsync(List<int> ids)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<EquipmentModelComponentTemplate>()
                    .Where(t => ids.Contains(t.Id))
                    .ExecuteCommandAsync();

                _logger.LogInformation("批量删除模板成功: {count}个", result);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量删除模板失败");
                return (false, $"批量删除模板失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var template = await GetTemplateByIdAsync(id);
                if (template == null)
                {
                    return (false, "模板不存在");
                }

                template.IsEnabled = !template.IsEnabled;
                template.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(template)
                    .UpdateColumns(t => new { t.IsEnabled, t.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换模板状态成功: {id} -> {status}", id, template.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换模板状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CopyTemplatesToEquipmentModelAsync(int sourceModelId, int targetModelId)
        {
            try
            {
                // 验证目标设备型号是否存在
                var targetModel = await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(em => em.Id == targetModelId && em.IsEnabled)
                    .FirstAsync();
                if (targetModel == null)
                {
                    return (false, "目标设备型号不存在或已禁用");
                }

                // 获取源设备型号的模板
                var sourceTemplates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .Where(t => t.EquipmentModelId == sourceModelId && t.IsEnabled)
                    .ToListAsync();

                if (!sourceTemplates.Any())
                {
                    return (false, "源设备型号没有可复制的模板");
                }

                var copiedCount = 0;
                foreach (var sourceTemplate in sourceTemplates)
                {
                    // 检查目标设备型号是否已存在相同的部件模板
                    if (!await IsTemplateExistsAsync(targetModelId, sourceTemplate.ComponentId))
                    {
                        var newTemplate = new EquipmentModelComponentTemplate
                        {
                            EquipmentModelId = targetModelId,
                            ComponentId = sourceTemplate.ComponentId,
                            StandardQuantity = sourceTemplate.StandardQuantity,
                            IsRequired = sourceTemplate.IsRequired,
                            ReplacementCycleDays = sourceTemplate.ReplacementCycleDays,
                            MaintenanceNotes = sourceTemplate.MaintenanceNotes,
                            SortOrder = sourceTemplate.SortOrder,
                            IsEnabled = sourceTemplate.IsEnabled,
                            CreatedAt = DateTime.Now,
                            Remark = sourceTemplate.Remark
                        };

                        await _dbContext.Db.Insertable(newTemplate).ExecuteReturnIdentityAsync();
                        copiedCount++;
                    }
                }

                _logger.LogInformation("复制模板成功: {sourceId} -> {targetId}, 复制{count}个", sourceModelId, targetModelId, copiedCount);
                return (true, $"成功复制 {copiedCount} 个模板");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制模板失败: {sourceId} -> {targetId}", sourceModelId, targetModelId);
                return (false, $"复制模板失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ApplyTemplatesToEquipmentsAsync(int equipmentModelId, List<int> equipmentIds)
        {
            try
            {
                // 获取设备型号的模板
                var templates = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .Where(t => t.EquipmentModelId == equipmentModelId && t.IsEnabled)
                    .ToListAsync();

                if (!templates.Any())
                {
                    return (false, "该设备型号没有可应用的模板");
                }

                // 验证设备是否都属于指定的设备型号
                var equipments = await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => equipmentIds.Contains(e.Id) && e.ModelId == equipmentModelId)
                    .ToListAsync();

                if (equipments.Count != equipmentIds.Count)
                {
                    return (false, "部分设备不属于指定的设备型号");
                }

                // 这里可以实现将模板应用到设备的逻辑
                // 例如：为每个设备创建对应的部件记录或配置

                _logger.LogInformation("应用模板到设备成功: 型号{modelId}, 设备{count}台", equipmentModelId, equipments.Count);
                return (true, $"成功应用模板到 {equipments.Count} 台设备");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用模板到设备失败: 型号{modelId}", equipmentModelId);
                return (false, $"应用模板失败: {ex.Message}");
            }
        }

        public async Task<bool> IsTemplateExistsAsync(int equipmentModelId, int componentId, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                    .Where(t => t.EquipmentModelId == equipmentModelId && t.ComponentId == componentId);

                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查模板是否存在失败: 型号{modelId} - 部件{componentId}", equipmentModelId, componentId);
                throw;
            }
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> GetRequiredComponentsAsync(int equipmentModelId)
        {
            try
            {
                var templates = await GetTemplatesByEquipmentModelAsync(equipmentModelId);
                return templates.Where(t => t.IsRequired).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取必需部件失败: {equipmentModelId}", equipmentModelId);
                throw;
            }
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> GetOptionalComponentsAsync(int equipmentModelId)
        {
            try
            {
                var templates = await GetTemplatesByEquipmentModelAsync(equipmentModelId);
                return templates.Where(t => !t.IsRequired).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可选部件失败: {equipmentModelId}", equipmentModelId);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int ImportedCount)> ImportTemplatesAsync(List<EquipmentModelComponentTemplate> templates)
        {
            try
            {
                var importedCount = 0;
                var errors = new List<string>();

                foreach (var template in templates)
                {
                    // 检查模板是否已存在
                    if (await IsTemplateExistsAsync(template.EquipmentModelId, template.ComponentId))
                    {
                        errors.Add($"设备型号{template.EquipmentModelId}的部件{template.ComponentId}模板已存在");
                        continue;
                    }

                    // 验证设备型号是否存在
                    var equipmentModel = await _dbContext.Db.Queryable<EquipmentModel>()
                        .Where(em => em.Id == template.EquipmentModelId && em.IsEnabled)
                        .FirstAsync();
                    if (equipmentModel == null)
                    {
                        errors.Add($"设备型号{template.EquipmentModelId}不存在或已禁用");
                        continue;
                    }

                    // 验证部件是否存在
                    var component = await _dbContext.Db.Queryable<Component>()
                        .Where(c => c.Id == template.ComponentId && c.IsEnabled)
                        .FirstAsync();
                    if (component == null)
                    {
                        errors.Add($"部件{template.ComponentId}不存在或已禁用");
                        continue;
                    }

                    template.CreatedAt = DateTime.Now;
                    await _dbContext.Db.Insertable(template).ExecuteReturnIdentityAsync();
                    importedCount++;
                }

                var errorMessage = errors.Any() ? string.Join("; ", errors) : string.Empty;
                _logger.LogInformation("导入模板完成: 成功{imported}个, 失败{failed}个", importedCount, errors.Count);

                return (true, errorMessage, importedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入模板失败");
                return (false, $"导入模板失败: {ex.Message}", 0);
            }
        }

        public async Task<List<EquipmentModelComponentTemplateDto>> ExportTemplatesAsync(int? equipmentModelId = null)
        {
            try
            {
                if (equipmentModelId.HasValue)
                {
                    return await GetTemplatesByEquipmentModelAsync(equipmentModelId.Value);
                }
                else
                {
                    return await GetAllTemplatesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出模板数据失败");
                throw;
            }
        }

        #region 私有方法

        private string GetReplacementCycleDescription(int? replacementCycleDays)
        {
            if (!replacementCycleDays.HasValue)
                return "无固定周期";

            if (replacementCycleDays.Value < 30)
                return $"{replacementCycleDays.Value}天";
            else if (replacementCycleDays.Value < 365)
                return $"{replacementCycleDays.Value / 30}个月";
            else
                return $"{replacementCycleDays.Value / 365}年";
        }

        #endregion
    }
}
