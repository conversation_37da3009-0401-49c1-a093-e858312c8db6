@page "/login"
@using Microsoft.AspNetCore.Components.Authorization
@using CoreHub.Shared.Services
@inject IUserAuthenticationService UserAuthService
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<PageTitle>登录</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="d-flex align-center justify-center" Style="min-height: 100vh;">
    <MudPaper Class="pa-6" Style="width: 100%; max-width: 400px;">
        <div class="text-center mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Login" Size="Size.Large" Color="Color.Primary" />
            <MudText Typo="Typo.h4" Class="mt-2">登录系统</MudText>
            <MudText Typo="Typo.body2" Color="Color.Secondary">请登录您的账户</MudText>
            </div>

            <!-- 登录表单 -->
        <EditForm Model="@this" OnValidSubmit="HandleLogin">
            <MudTextField @bind-Value="username" 
                         Label="用户名"
                                    Placeholder="请输入用户名" 
                         Variant="Variant.Outlined"
                         Margin="Margin.Normal"
                         FullWidth="true"
                         Required="true"
                         RequiredError="请输入用户名" />

            <MudTextField @bind-Value="password" 
                         Label="密码"
                                            Placeholder="请输入密码" 
                         InputType="InputType.Password"
                         Variant="Variant.Outlined"
                         Margin="Margin.Normal"
                         FullWidth="true"
                         Required="true"
                         RequiredError="请输入密码" />

            <MudButton ButtonType="ButtonType.Submit" 
                      Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      FullWidth="true"
                      Size="Size.Large"
                      Class="mt-3"
                      Disabled="isLoading">
                @if (isLoading)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                    <MudText Class="ms-2">登录中...</MudText>
                }
                else
                {
                    <MudText>登录</MudText>
                }
            </MudButton>
        </EditForm>

            <!-- 错误消息 -->
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                         <MudAlert Severity="Severity.Error" 
                      Class="mt-3"
                      CloseIcon="true"
                      CloseIconClicked="() => errorMessage = string.Empty">
                @errorMessage
            </MudAlert>
            }

            <!-- 测试账号提示 -->
        <MudAlert Severity="Severity.Success" Class="mt-4">
            <MudText Typo="Typo.subtitle2" Class="mb-2"><strong>测试账号:</strong></MudText>
                        
            <div class="mb-2">
                <MudText Typo="Typo.body2">
                    <strong>管理员:</strong> admin / admin123 (全部权限)
                </MudText>
                        </div>
            <div class="mb-2">
                <MudText Typo="Typo.body2">
                    <strong>操作员:</strong> operator / op123 (设备相关)
                </MudText>
                        </div>
            <div class="mb-3">
                <MudText Typo="Typo.body2">
                    <strong>访客:</strong> viewer / view123 (仅查看)
                </MudText>
                        </div>

            <div class="d-flex flex-wrap gap-2">
                <MudButton Variant="Variant.Text" 
                          Color="Color.Primary" 
                          Size="Size.Small" 
                                             OnClick="UseAdminAccount">
                                使用管理员
                </MudButton>
                <MudButton Variant="Variant.Text" 
                          Color="Color.Primary" 
                          Size="Size.Small" 
                                             OnClick="UseOperatorAccount">
                                使用操作员
                </MudButton>
                <MudButton Variant="Variant.Text" 
                          Color="Color.Primary" 
                          Size="Size.Small" 
                                             OnClick="UseViewerAccount">
                                使用访客
                </MudButton>
</div>
        </MudAlert>
    </MudPaper>
</MudContainer>

@code {
    private string username = "";
    private string password = "";
    private string errorMessage = "";
    private bool isLoading = false;
    private string? returnUrl;

    protected override void OnInitialized()
    {
        // 获取返回URL参数
        var uri = Navigation.ToAbsoluteUri(Navigation.Uri);
        var query = uri.Query;
        if (!string.IsNullOrEmpty(query) && query.Contains("returnUrl="))
        {
            var startIndex = query.IndexOf("returnUrl=") + "returnUrl=".Length;
            var endIndex = query.IndexOf("&", startIndex);
            if (endIndex == -1) endIndex = query.Length;
            returnUrl = query.Substring(startIndex, endIndex - startIndex);
        }
    }

    private void UseAdminAccount()
    {
        username = "admin";
        password = "admin123";
        errorMessage = "";
        StateHasChanged();
    }

    private void UseOperatorAccount()
    {
        username = "operator";
        password = "op123";
        errorMessage = "";
        StateHasChanged();
    }

    private void UseViewerAccount()
    {
        username = "viewer";
        password = "view123";
        errorMessage = "";
        StateHasChanged();
    }

    private async Task HandleLogin()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                errorMessage = "请输入用户名";
                return;
            }

            if (string.IsNullOrWhiteSpace(password))
            {
                errorMessage = "请输入密码";
                return;
            }

            isLoading = true;
            errorMessage = "";
            StateHasChanged();

            // 验证用户
            var result = await UserAuthService.ValidateUserAsync(username.Trim(), password);

            if (result.IsSuccess && result.User != null)
            {
                // 创建 ClaimsPrincipal
                var user = result.User.ToClaimsPrincipal();
                
                // 使用 .NET 原生的 AuthenticationStateProvider 设置认证状态
                if (AuthStateProvider is PersistentAuthenticationStateProvider persistentProvider)
                {
                    await persistentProvider.MarkUserAsAuthenticatedAsync(user);
                }

                // 登录成功，跳转到返回URL或首页
                NavigateToReturnUrl();
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "用户名或密码错误，请重试";
                password = ""; // 清空密码
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"登录失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"登录异常: {ex}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void NavigateToReturnUrl()
    {
        var targetUrl = "/";
        
        if (!string.IsNullOrEmpty(returnUrl))
        {
            try
            {
                targetUrl = Uri.UnescapeDataString(returnUrl);
                // 确保返回URL是相对路径，防止开放重定向攻击
                if (targetUrl.StartsWith("http") || targetUrl.StartsWith("//"))
                {
                    targetUrl = "/";
                }
            }
            catch
            {
                targetUrl = "/";
            }
        }

        Navigation.NavigateTo(targetUrl, forceLoad: true);
    }
}