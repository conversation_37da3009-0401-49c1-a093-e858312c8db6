using CoreHub.Shared.Models.AppUpdate;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 应用更新服务接口
    /// </summary>
    public interface IAppUpdateService
    {
        /// <summary>
        /// 检查更新
        /// </summary>
        /// <param name="request">更新检查请求</param>
        /// <returns>更新检查响应</returns>
        Task<UpdateCheckResponse> CheckForUpdateAsync(UpdateCheckRequest request);

        /// <summary>
        /// 获取所有版本列表
        /// </summary>
        /// <param name="platform">平台类型</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>版本列表</returns>
        Task<(List<AppVersion> Versions, int TotalCount)> GetVersionsAsync(string? platform = null, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 根据ID获取版本信息
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <returns>版本信息</returns>
        Task<AppVersion?> GetVersionByIdAsync(int id);

        /// <summary>
        /// 创建新版本
        /// </summary>
        /// <param name="version">版本信息</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? VersionId)> CreateVersionAsync(AppVersion version);

        /// <summary>
        /// 更新版本信息
        /// </summary>
        /// <param name="version">版本信息</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateVersionAsync(AppVersion version);

        /// <summary>
        /// 删除版本
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteVersionAsync(int id);

        /// <summary>
        /// 发布版本
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <param name="operatorId">操作者ID</param>
        /// <returns>发布结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> PublishVersionAsync(int id, int operatorId);

        /// <summary>
        /// 撤回版本
        /// </summary>
        /// <param name="id">版本ID</param>
        /// <param name="operatorId">操作者ID</param>
        /// <returns>撤回结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> WithdrawVersionAsync(int id, int operatorId);

        /// <summary>
        /// 获取最新版本
        /// </summary>
        /// <param name="platform">平台类型</param>
        /// <param name="targetAudience">目标用户群体</param>
        /// <param name="departmentId">部门ID</param>
        /// <returns>最新版本</returns>
        Task<AppVersion?> GetLatestVersionAsync(string platform, string targetAudience = "All", int? departmentId = null);

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedMd5">期望的MD5值</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateFileIntegrityAsync(string filePath, string expectedMd5);

        /// <summary>
        /// 上传文件到服务器
        /// </summary>
        /// <param name="file">上传的文件</param>
        /// <param name="platform">平台类型</param>
        /// <param name="version">版本号</param>
        /// <returns>上传结果</returns>
        Task<(bool Success, string Message, string FileName, long FileSize, string FileMd5, string DownloadUrl)> UploadFileAsync(
            Microsoft.AspNetCore.Components.Forms.IBrowserFile file, string platform, string version);

        /// <summary>
        /// 计算文件MD5
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5值</returns>
        Task<string> CalculateFileMd5Async(string filePath);

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小</returns>
        Task<long> GetFileSizeAsync(string filePath);
    }
}
