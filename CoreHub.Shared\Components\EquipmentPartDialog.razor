@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IEquipmentPartService EquipmentPartService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@isFormValid" @bind-Errors="@errors">
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="equipmentPart.Code" 
                                  Label="部位编码" 
                                  Required="true" 
                                  RequiredError="部位编码不能为空"
                                  MaxLength="50"
                                  Variant="Variant.Outlined" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="equipmentPart.Name" 
                                  Label="部位名称" 
                                  Required="true" 
                                  RequiredError="部位名称不能为空"
                                  MaxLength="100"
                                  Variant="Variant.Outlined" />
                </MudItem>

                @if (parentParts.Any())
                {
                    <MudItem xs="12">
                        <MudSelect T="int?" @bind-Value="equipmentPart.ParentId" 
                                   Label="父级部位" 
                                   Placeholder="选择父级部位（可选）"
                                   Variant="Variant.Outlined"
                                   Clearable="true">
                            @foreach (var parent in parentParts)
                            {
                                <MudSelectItem Value="@((int?)parent.Id)">@parent.IndentedName</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                }

                <MudItem xs="12">
                    <MudNumericField @bind-Value="equipmentPart.SortOrder" 
                                     Label="排序顺序" 
                                     Min="0"
                                     Variant="Variant.Outlined" />
                </MudItem>

                <MudItem xs="12">
                    <MudTextField @bind-Value="equipmentPart.Description" 
                                  Label="部位描述" 
                                  Lines="3"
                                  MaxLength="500"
                                  Variant="Variant.Outlined" />
                </MudItem>

                <MudItem xs="12">
                    <MudSwitch T="bool" @bind-Checked="equipmentPart.IsEnabled"
                               Label="启用状态"
                               Color="Color.Success" />
                </MudItem>

                <MudItem xs="12">
                    <MudTextField @bind-Value="equipmentPart.Remark" 
                                  Label="备注" 
                                  Lines="2"
                                  MaxLength="1000"
                                  Variant="Variant.Outlined" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" OnClick="Submit" Disabled="@(!isFormValid || isSubmitting)">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>保存</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    
    [Parameter] public int EquipmentId { get; set; }
    [Parameter] public int? ParentId { get; set; }
    [Parameter] public EquipmentPart? EquipmentPart { get; set; }

    private MudForm form = null!;
    private bool isFormValid;
    private string[] errors = { };
    private bool isSubmitting = false;
    private bool isEditMode = false;

    private EquipmentPart equipmentPart = new();
    private List<EquipmentPartDto> parentParts = new();

    protected override async Task OnInitializedAsync()
    {
        isEditMode = EquipmentPart != null;
        
        if (isEditMode)
        {
            // 编辑模式：复制现有数据
            equipmentPart = new EquipmentPart
            {
                Id = EquipmentPart!.Id,
                Code = EquipmentPart.Code,
                Name = EquipmentPart.Name,
                EquipmentId = EquipmentPart.EquipmentId,
                ParentId = EquipmentPart.ParentId,
                Level = EquipmentPart.Level,
                SortOrder = EquipmentPart.SortOrder,
                Description = EquipmentPart.Description,
                IsEnabled = EquipmentPart.IsEnabled,
                Remark = EquipmentPart.Remark
            };
            EquipmentId = EquipmentPart.EquipmentId;
        }
        else
        {
            // 新建模式：初始化默认值
            equipmentPart = new EquipmentPart
            {
                EquipmentId = EquipmentId,
                ParentId = ParentId,
                IsEnabled = true,
                SortOrder = 0
            };
        }

        await LoadParentParts();
    }

    private async Task LoadParentParts()
    {
        try
        {
            var allParts = await EquipmentPartService.GetEquipmentPartsFlatListAsync(EquipmentId);
            
            // 过滤掉自己和自己的子部位（编辑模式下）
            if (isEditMode)
            {
                var excludeIds = new List<int> { equipmentPart.Id };
                // 这里可以添加获取所有子部位ID的逻辑
                parentParts = allParts.Where(p => !excludeIds.Contains(p.Id)).ToList();
            }
            else
            {
                parentParts = allParts;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载父级部位失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task Submit()
    {
        if (!isFormValid) return;

        try
        {
            isSubmitting = true;

            // 验证编码唯一性
            var isCodeUnique = await EquipmentPartService.IsPartCodeUniqueAsync(
                equipmentPart.Code, 
                EquipmentId, 
                isEditMode ? equipmentPart.Id : null);

            if (!isCodeUnique)
            {
                Snackbar.Add("部位编码已存在，请使用其他编码", Severity.Error);
                return;
            }

            if (isEditMode)
            {
                var result = await EquipmentPartService.UpdateEquipmentPartAsync(equipmentPart);
                if (result.IsSuccess)
                {
                    MudDialog.Close(DialogResult.Ok(equipmentPart));
                }
                else
                {
                    Snackbar.Add($"更新失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            else
            {
                var result = await EquipmentPartService.CreateEquipmentPartAsync(equipmentPart);
                if (result.IsSuccess)
                {
                    MudDialog.Close(DialogResult.Ok(equipmentPart));
                }
                else
                {
                    Snackbar.Add($"创建失败: {result.ErrorMessage}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
