@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@inject IComponentReplacementRecordService ComponentReplacementRecordService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">
                    标准部件清单
                </MudText>
            </MudItem>
            <MudItem xs="12">
                @if (StandardComponents.Any())
                {
                    <MudDataGrid T="EquipmentModelComponentTemplate" 
                               Items="@StandardComponents" 
                               Dense="true"
                               Hover="true"
                               Striped="true"
                               SelectOnRowClick="false">
                        <Columns>
                            <TemplateColumn Title="选择" Sortable="false">
                                <CellTemplate>
                                    <MudCheckBox @bind-Value="selectedComponents[context.Item.Id]" />
                                </CellTemplate>
                            </TemplateColumn>
                            <PropertyColumn Property="x => x.Component!.Name" Title="部件名称" />
                            <PropertyColumn Property="x => x.Component!.Code" Title="部件编码" />
                            <PropertyColumn Property="x => x.StandardQuantity" Title="标准数量" />
                            <PropertyColumn Property="x => x.Component!.Unit" Title="单位" />
                            <TemplateColumn Title="类型" Sortable="false">
                                <CellTemplate>
                                    <MudChip Color="@(context.Item.IsRequired ? Color.Error : Color.Info)" 
                                           Size="Size.Small">
                                        @(context.Item.IsRequired ? "必需" : "可选")
                                    </MudChip>
                                </CellTemplate>
                            </TemplateColumn>
                            <TemplateColumn Title="库存状态" Sortable="false">
                                <CellTemplate>
                                    @if (context.Item.Component != null)
                                    {
                                        <MudChip Color="@GetStockStatusColor(context.Item.Component)" 
                                               Size="Size.Small">
                                            @context.Item.Component.StockQuantity @context.Item.Component.Unit
                                        </MudChip>
                                    }
                                </CellTemplate>
                            </TemplateColumn>
                            <PropertyColumn Property="x => x.MaintenanceNotes" Title="维护说明" />
                        </Columns>
                    </MudDataGrid>
                }
                else
                {
                    <MudAlert Severity="Severity.Info">
                        该设备型号暂无标准部件配置
                    </MudAlert>
                }
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
        @if (StandardComponents.Any())
        {
            <MudButton Color="Color.Primary" 
                     Variant="Variant.Filled" 
                     OnClick="AddSelectedComponents"
                     Disabled="@(!HasSelectedComponents() || isSubmitting)">
                @if (isSubmitting)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                    <MudText Class="ms-2">添加中...</MudText>
                }
                else
                {
                    <MudText>添加选中部件</MudText>
                }
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public List<EquipmentModelComponentTemplate> StandardComponents { get; set; } = new();
    [Parameter] public int RepairOrderId { get; set; }
    [Parameter] public int EquipmentId { get; set; }

    private Dictionary<int, bool> selectedComponents = new();
    private bool isSubmitting = false;

    protected override void OnInitialized()
    {
        // 初始化选择状态
        foreach (var component in StandardComponents)
        {
            selectedComponents[component.Id] = false;
        }
    }

    private Color GetStockStatusColor(Component component)
    {
        if (component.StockQuantity <= 0)
            return Color.Error;
        else if (component.StockQuantity <= component.MinStockQuantity)
            return Color.Warning;
        else
            return Color.Success;
    }

    private bool HasSelectedComponents()
    {
        return selectedComponents.Values.Any(selected => selected);
    }

    private async Task AddSelectedComponents()
    {
        try
        {
            isSubmitting = true;
            StateHasChanged();

            var recordsToCreate = new List<ComponentReplacementRecord>();

            foreach (var kvp in selectedComponents.Where(x => x.Value))
            {
                var template = StandardComponents.FirstOrDefault(t => t.Id == kvp.Key);
                if (template?.Component != null)
                {
                    var record = new ComponentReplacementRecord
                    {
                        RepairOrderId = RepairOrderId,
                        EquipmentId = EquipmentId,
                        ComponentId = template.ComponentId,
                        Quantity = template.StandardQuantity,
                        Reason = "按标准部件清单更换",
                        ReplacedAt = DateTime.Now,
                        UnitPrice = template.Component.UnitPrice,
                        TotalPrice = template.Component.UnitPrice.HasValue 
                            ? template.Component.UnitPrice.Value * template.StandardQuantity 
                            : null
                    };
                    recordsToCreate.Add(record);
                }
            }

            if (recordsToCreate.Any())
            {
                var result = await ComponentReplacementRecordService.BatchCreateRecordsAsync(recordsToCreate);
                if (result.IsSuccess)
                {
                    var message = $"成功添加 {result.CreatedCount} 个部件更换记录";
                    if (!string.IsNullOrEmpty(result.ErrorMessage))
                    {
                        message += $"，部分失败：{result.ErrorMessage}";
                    }
                    Snackbar.Add(message, result.CreatedCount > 0 ? Severity.Success : Severity.Warning);
                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    Snackbar.Add(result.ErrorMessage, Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"添加部件更换记录失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
