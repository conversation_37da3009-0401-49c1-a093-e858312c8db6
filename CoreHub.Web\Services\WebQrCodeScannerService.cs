using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    public class WebQrCodeScannerService : IQrCodeScannerService
    {
        public Task<string?> ScanQrCodeAsync()
        {
            // Web端不支持摄像头扫描，返回空字符串或特殊值
            return Task.FromResult<string?>("CAMERA_UNAVAILABLE");
        }

        public Task<bool> RequestCameraPermissionAsync()
        {
            // Web端不需要摄像头权限，返回true
            return Task.FromResult(true);
        }

        public bool IsCameraAvailable()
        {
            // Web端不支持摄像头，返回false
            return false;
        }
    }
} 