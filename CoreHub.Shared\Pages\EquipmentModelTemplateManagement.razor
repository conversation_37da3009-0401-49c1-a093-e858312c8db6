@page "/equipment-model-template-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using MudBlazor
@inject IEquipmentModelComponentTemplateService TemplateService
@inject IEquipmentModelService EquipmentModelService
@inject IComponentService ComponentService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>设备型号模板管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <!-- 页面标题 -->
    <MudBreadcrumbs Items="breadcrumbItems" Class="mb-4"></MudBreadcrumbs>
    
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h4" Class="mb-4">
            <MudIcon Icon="@Icons.Material.Filled.Memory" Class="mr-2" />
            设备型号模板管理
        </MudText>
        <MudText Typo="Typo.body1" Color="Color.Secondary">
            为每个设备型号定义标准的部件配置模板，用于批量应用到同型号设备
        </MudText>
    </MudPaper>

    <!-- 工具栏 -->
    <MudPaper Class="pa-4 mb-4">
        <MudGrid AlignItems="AlignItems.Center">
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="searchText" 
                            Placeholder="搜索设备型号或部件..." 
                            Adornment="Adornment.Start" 
                            AdornmentIcon="@Icons.Material.Filled.Search"
                            Immediate="true"
                            OnKeyUp="OnSearchKeyUp" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect T="int?" @bind-Value="selectedEquipmentModelId"
                         Label="筛选设备型号"
                         Clearable="true"
                         OnSelectionChanged="OnEquipmentModelChanged">
                    @if (equipmentModels != null)
                    {
                        @foreach (var model in equipmentModels)
                        {
                            <MudSelectItem T="int?" Value="@((int?)model.Id)">@model.Name</MudSelectItem>
                        }
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect T="bool?" @bind-Value="selectedIsRequired"
                         Label="部件类型"
                         Clearable="true"
                         OnSelectionChanged="OnRequiredFilterChanged">
                    <MudSelectItem T="bool?" Value="@((bool?)true)">必需部件</MudSelectItem>
                    <MudSelectItem T="bool?" Value="@((bool?)false)">可选部件</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="4">
                <MudStack Row Spacing="2">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增模板
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             StartIcon="@Icons.Material.Filled.FileCopy"
                             OnClick="OpenCopyDialog"
                             Disabled="@(!templates.Any())">
                        复制模板
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             StartIcon="@Icons.Material.Filled.Refresh"
                             OnClick="LoadData">
                        刷新
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 统计信息 -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                    <MudIcon Icon="@Icons.Material.Filled.Memory" Color="Color.Primary" Size="Size.Large" />
                    <div>
                        <MudText Typo="Typo.h6">@statistics.TotalTemplates</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">模板总数</MudText>
                    </div>
                </MudStack>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                    <MudIcon Icon="@Icons.Material.Filled.PrecisionManufacturing" Color="Color.Secondary" Size="Size.Large" />
                    <div>
                        <MudText Typo="Typo.h6">@statistics.EquipmentModelCount</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">设备型号数</MudText>
                    </div>
                </MudStack>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                    <MudIcon Icon="@Icons.Material.Filled.Star" Color="Color.Warning" Size="Size.Large" />
                    <div>
                        <MudText Typo="Typo.h6">@statistics.RequiredComponentCount</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">必需部件</MudText>
                    </div>
                </MudStack>
            </MudPaper>
        </MudItem>
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                    <MudIcon Icon="@Icons.Material.Filled.Extension" Color="Color.Info" Size="Size.Large" />
                    <div>
                        <MudText Typo="Typo.h6">@statistics.OptionalComponentCount</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">可选部件</MudText>
                    </div>
                </MudStack>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- 数据表格 -->
    <MudPaper Class="pa-4">
        <MudDataGrid T="EquipmentModelComponentTemplateDto" 
                   Items="@filteredTemplates" 
                   Loading="@isLoading"
                   Hover="true" 
                   Striped="true"
                   Dense="true"
                   FixedHeader="true"
                   Height="600px"
                   SortMode="SortMode.Multiple"
                   Groupable="true"
                   GroupExpanded="true">
            <Columns>
                <PropertyColumn Property="x => x.EquipmentModelName" Title="设备型号" Grouping />
                <PropertyColumn Property="x => x.ComponentCode" Title="部件编码" />
                <PropertyColumn Property="x => x.ComponentName" Title="部件名称" />
                <PropertyColumn Property="x => x.ComponentCategoryName" Title="部件分类" />
                <PropertyColumn Property="x => x.StandardQuantity" Title="标准数量" />
                <PropertyColumn Property="x => x.ComponentUnit" Title="单位" />
                <TemplateColumn Title="部件类型" Sortable="true" SortBy="@(x => x.IsRequired)">
                    <CellTemplate>
                        <MudChip Color="@(context.Item.IsRequired ? Color.Warning : Color.Info)" 
                               Size="Size.Small">
                            @(context.Item.IsRequired ? "必需" : "可选")
                        </MudChip>
                    </CellTemplate>
                </TemplateColumn>
                <PropertyColumn Property="x => x.ReplacementCycleDescription" Title="更换周期" />
                <TemplateColumn Title="库存状态" Sortable="false">
                    <CellTemplate>
                        <MudChip Color="@GetStockStatusColor(context.Item.ComponentStockStatus)" 
                               Size="Size.Small">
                            @context.Item.ComponentStockStatus
                        </MudChip>
                    </CellTemplate>
                </TemplateColumn>
                <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                <TemplateColumn Title="状态" Sortable="true" SortBy="@(x => x.IsEnabled)">
                    <CellTemplate>
                        <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                               Size="Size.Small">
                            @(context.Item.IsEnabled ? "启用" : "禁用")
                        </MudChip>
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                    <CellTemplate>
                        <MudStack Row Spacing="1">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                         Color="Color.Primary" 
                                         Size="Size.Small"
                                         OnClick="() => OpenEditDialog(context.Item)"
                                         Title="编辑" />
                            <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                         Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                         Size="Size.Small"
                                         OnClick="() => ToggleStatus(context.Item)"
                                         Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                         Color="Color.Error" 
                                         Size="Size.Small"
                                         OnClick="() => DeleteTemplate(context.Item)"
                                         Title="删除" />
                        </MudStack>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>
    </MudPaper>
</MudContainer>

@code {
    // 面包屑导航
    private List<BreadcrumbItem> breadcrumbItems = new()
    {
        new BreadcrumbItem("首页", href: "/", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("设备管理", href: null, disabled: true),
        new BreadcrumbItem("设备型号模板管理", href: "/equipment-model-template-management", disabled: true)
    };

    // 页面状态
    private bool isLoading = false;
    private string searchText = string.Empty;
    private int? selectedEquipmentModelId;
    private bool? selectedIsRequired;

    // 数据集合
    private List<EquipmentModelComponentTemplateDto> templates = new();
    private List<EquipmentModelComponentTemplateDto> filteredTemplates = new();
    private List<EquipmentModel> equipmentModels = new();

    // 统计信息
    private TemplateStatistics statistics = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // 加载模板数据
            templates = await TemplateService.GetAllTemplatesAsync();

            // 加载设备型号
            var equipmentModelList = await EquipmentModelService.GetAllEquipmentModelsAsync();
            equipmentModels = equipmentModelList.Where(em => em.IsEnabled).ToList();

            // 计算统计信息
            CalculateStatistics();

            // 应用筛选
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void CalculateStatistics()
    {
        statistics = new TemplateStatistics
        {
            TotalTemplates = templates.Count,
            EquipmentModelCount = templates.Select(t => t.EquipmentModelId).Distinct().Count(),
            RequiredComponentCount = templates.Count(t => t.IsRequired),
            OptionalComponentCount = templates.Count(t => !t.IsRequired)
        };
    }

    private void ApplyFilters()
    {
        var query = templates.AsEnumerable();

        // 搜索筛选
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            var search = searchText.ToLower();
            query = query.Where(t =>
                t.EquipmentModelName.ToLower().Contains(search) ||
                t.ComponentName.ToLower().Contains(search) ||
                t.ComponentCode.ToLower().Contains(search) ||
                t.ComponentCategoryName.ToLower().Contains(search));
        }

        // 设备型号筛选
        if (selectedEquipmentModelId.HasValue)
        {
            query = query.Where(t => t.EquipmentModelId == selectedEquipmentModelId.Value);
        }

        // 部件类型筛选
        if (selectedIsRequired.HasValue)
        {
            query = query.Where(t => t.IsRequired == selectedIsRequired.Value);
        }

        filteredTemplates = query.ToList();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        ApplyFilters();
    }

    private void OnEquipmentModelChanged(int? modelId)
    {
        selectedEquipmentModelId = modelId;
        ApplyFilters();
    }

    private void OnRequiredFilterChanged(bool? isRequired)
    {
        selectedIsRequired = isRequired;
        ApplyFilters();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<EquipmentModelTemplateEditDialog>
        {
            { x => x.Template, new EquipmentModelComponentTemplate() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<EquipmentModelTemplateEditDialog>("新增设备型号模板", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadData();
        }
    }

    private async Task OpenEditDialog(EquipmentModelComponentTemplateDto templateDto)
    {
        // 获取完整的模板实体
        var template = await TemplateService.GetTemplateByIdAsync(templateDto.Id);
        if (template == null)
        {
            Snackbar.Add("模板不存在", Severity.Error);
            return;
        }

        var parameters = new DialogParameters<EquipmentModelTemplateEditDialog>
        {
            { x => x.Template, template },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<EquipmentModelTemplateEditDialog>("编辑设备型号模板", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadData();
        }
    }

    private async Task OpenCopyDialog()
    {
        var parameters = new DialogParameters<EquipmentModelTemplateCopyDialog>
        {
            { x => x.EquipmentModels, equipmentModels }
        };

        var dialog = await DialogService.ShowAsync<EquipmentModelTemplateCopyDialog>("复制设备型号模板", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadData();
        }
    }

    private async Task ToggleStatus(EquipmentModelComponentTemplateDto templateDto)
    {
        try
        {
            var template = await TemplateService.GetTemplateByIdAsync(templateDto.Id);
            if (template == null)
            {
                Snackbar.Add("模板不存在", Severity.Error);
                return;
            }

            template.IsEnabled = !template.IsEnabled;
            var result = await TemplateService.UpdateTemplateAsync(template);

            if (result.IsSuccess)
            {
                Snackbar.Add($"模板已{(template.IsEnabled ? "启用" : "禁用")}", Severity.Success);
                await LoadData();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteTemplate(EquipmentModelComponentTemplateDto templateDto)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除设备型号 '{templateDto.EquipmentModelName}' 的部件 '{templateDto.ComponentName}' 模板吗？",
            yesText: "删除", cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await TemplateService.DeleteTemplateAsync(templateDto.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("模板删除成功", Severity.Success);
                    await LoadData();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private Color GetStockStatusColor(string status)
    {
        return status switch
        {
            "库存充足" => Color.Success,
            "库存不足" => Color.Warning,
            "缺货" => Color.Error,
            _ => Color.Default
        };
    }

    // 统计信息类
    private class TemplateStatistics
    {
        public int TotalTemplates { get; set; }
        public int EquipmentModelCount { get; set; }
        public int RequiredComponentCount { get; set; }
        public int OptionalComponentCount { get; set; }
    }
}
