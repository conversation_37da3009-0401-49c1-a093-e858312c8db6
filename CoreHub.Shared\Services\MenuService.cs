using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 菜单服务实现
    /// </summary>
    public class MenuService : IMenuService
    {
        private readonly DatabaseContext _dbContext;
        private readonly IUserManagementService _userManagementService;
        private readonly ILogger<MenuService> _logger;

        public MenuService(
            DatabaseContext dbContext,
            IUserManagementService userManagementService,
            ILogger<MenuService> logger)
        {
            _dbContext = dbContext;
            _userManagementService = userManagementService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户可访问的菜单列表
        /// </summary>
        public async Task<List<MenuItem>> GetUserMenusAsync(int? userId = null)
        {
            try
            {
                // 使用同步方法避免MultipleActiveResultSets问题
                var allMenus = _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.IsEnabled)
                    .OrderBy(m => m.SortOrder)
                    .ToList();

                var userMenus = new List<MenuItem>();

                foreach (var menu in allMenus)
                {
                    // 公开菜单（如首页）对所有用户可见
                    if (menu.IsPublic)
                    {
                        userMenus.Add(menu);
                        continue;
                    }

                    // 未登录用户只能看到公开菜单
                    if (userId == null)
                    {
                        continue;
                    }

                    // 检查用户是否有权限访问此菜单
                    if (!string.IsNullOrEmpty(menu.PermissionCode))
                    {
                        // 检查是否为系统管理员角色
                        var hasAdminRole = await _userManagementService.CheckUserRoleAsync(userId.Value, RoleCodes.Administrator);
                        if (hasAdminRole)
                        {
                            // 系统管理员可以访问所有菜单
                            userMenus.Add(menu);
                        }
                        else
                        {
                            var hasPermission = await _userManagementService.CheckUserPermissionAsync(userId.Value, menu.PermissionCode);
                            if (hasPermission)
                            {
                                userMenus.Add(menu);
                            }
                        }
                    }
                    else
                    {
                        // 没有权限要求的菜单，登录用户都可以看到
                        userMenus.Add(menu);
                    }
                }

                // 构建菜单树结构
                return BuildMenuTree(userMenus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户菜单失败：{userId}", userId);
                return new List<MenuItem>();
            }
        }

        /// <summary>
        /// 获取所有菜单（管理用）
        /// </summary>
        public Task<List<MenuItem>> GetAllMenusAsync()
        {
            try
            {
                // 使用同步方法避免MultipleActiveResultSets问题
                var allMenus = _dbContext.Db.Queryable<MenuItem>()
                    .OrderBy(m => m.SortOrder)
                    .ToList();

                return Task.FromResult(BuildMenuTree(allMenus));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有菜单失败");
                return Task.FromResult(new List<MenuItem>());
            }
        }

        /// <summary>
        /// 创建菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> CreateMenuAsync(MenuItem menuItem)
        {
            try
            {
                // 检查菜单编码是否已存在
                var existingMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Code == menuItem.Code)
                    .FirstAsync();

                if (existingMenu != null)
                    return (false, "菜单编码已存在");

                menuItem.CreatedAt = DateTime.Now;

                await _dbContext.Db.Insertable(menuItem).ExecuteCommandAsync();

                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建菜单失败：{menuName}", menuItem.Name);
                return (false, $"创建菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 更新菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateMenuAsync(MenuItem menuItem)
        {
            try
            {
                // 检查菜单是否存在
                var existingMenu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Id == menuItem.Id)
                    .FirstAsync();

                if (existingMenu == null)
                    return (false, "菜单不存在");

                // 检查菜单编码是否已被其他菜单使用
                if (existingMenu.Code != menuItem.Code)
                {
                    var duplicateMenu = await _dbContext.Db.Queryable<MenuItem>()
                        .Where(m => m.Code == menuItem.Code && m.Id != menuItem.Id)
                        .FirstAsync();

                    if (duplicateMenu != null)
                        return (false, "菜单编码已被其他菜单使用");
                }

                menuItem.UpdatedAt = DateTime.Now;

                await _dbContext.Db.Updateable(menuItem)
                    .IgnoreColumns(m => new { m.CreatedAt, m.CreatedBy })
                    .ExecuteCommandAsync();

                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新菜单失败：{menuId}", menuItem.Id);
                return (false, $"更新菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 删除菜单
        /// </summary>
        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteMenuAsync(int menuId)
        {
            try
            {
                var menu = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.Id == menuId)
                    .FirstAsync();

                if (menu == null)
                    return (false, "菜单不存在");

                if (menu.IsSystem)
                    return (false, "系统菜单不能删除");

                // 检查是否有子菜单
                var childCount = await _dbContext.Db.Queryable<MenuItem>()
                    .Where(m => m.ParentId == menuId)
                    .CountAsync();

                if (childCount > 0)
                    return (false, "该菜单下还有子菜单，无法删除");

                await _dbContext.Db.Deleteable<MenuItem>()
                    .Where(m => m.Id == menuId)
                    .ExecuteCommandAsync();

                return (true, "");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除菜单失败：{menuId}", menuId);
                return (false, $"删除菜单失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化默认菜单数据（已移除固定菜单项生成，菜单完全动态化）
        /// </summary>
        public Task<(bool IsSuccess, string ErrorMessage)> InitializeDefaultMenusAsync()
        {
            // 菜单现在完全动态生成，不再创建固定菜单项
            return Task.FromResult((true, "菜单系统已动态化"));
        }

        /// <summary>
        /// 构建菜单树结构
        /// </summary>
        private static List<MenuItem> BuildMenuTree(List<MenuItem> allMenus)
        {
            var menuDict = allMenus.ToDictionary(m => m.Id, m => m);
            var rootMenus = new List<MenuItem>();

            foreach (var menu in allMenus)
            {
                if (menu.ParentId == null)
                {
                    rootMenus.Add(menu);
                }
                else if (menuDict.TryGetValue(menu.ParentId.Value, out var parent))
                {
                    parent.Children.Add(menu);
                    menu.Parent = parent;
                }
            }

            return [.. rootMenus.OrderBy(m => m.SortOrder)];
        }
    }
} 