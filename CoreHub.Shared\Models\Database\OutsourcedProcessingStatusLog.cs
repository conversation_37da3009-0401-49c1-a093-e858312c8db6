using SqlSugar;

namespace CoreHub.Shared.Models.Database;

/// <summary>
/// 委外加工状态更新日志实体
/// </summary>
[SugarTable("OutsourcedProcessingStatusLogs")]
public class OutsourcedProcessingStatusLog
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 关联的委外加工记录ID
    /// </summary>
    public int ProcessingRecordId { get; set; }

    /// <summary>
    /// 原状态
    /// </summary>
    public int FromStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public int ToStatus { get; set; }

    /// <summary>
    /// 更新人ID
    /// </summary>
    public int UpdatedBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新备注
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    public string? Remark { get; set; }

    // 导航属性
    /// <summary>
    /// 关联的委外加工记录
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public OutsourcedProcessingRecord? ProcessingRecord { get; set; }

    /// <summary>
    /// 更新人信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Updater { get; set; }
}