@using System.Security.Claims
@using CoreHub.Shared

<AuthorizeView>
    <Authorized>
        @if (HasRequiredPermissions(context.User))
        {
            @ChildContent
        }
        else if (NotAuthorized != null)
        {
            @NotAuthorized
        }
    </Authorized>
    <NotAuthorized>
        @if (NotAuthorized != null)
        {
            @NotAuthorized
        }
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? NotAuthorized { get; set; }
    [Parameter] public string? RequiredPermission { get; set; }
    [Parameter] public List<string>? RequiredPermissions { get; set; }
    [Parameter] public bool RequireAllPermissions { get; set; } = false;
    [Parameter] public string? RequiredRole { get; set; }
    [Parameter] public List<string>? RequiredRoles { get; set; }
    [Parameter] public bool RequireAllRoles { get; set; } = false;

    private bool HasRequiredPermissions(ClaimsPrincipal user)
    {
        // 获取用户角色
        var userRoles = user.Claims
            .Where(c => c.Type == "Role")
            .SelectMany(c => c.Value.Split(',', StringSplitOptions.RemoveEmptyEntries))
            .Select(r => r.Trim())
            .ToList();

        // 检查角色要求
        if (!string.IsNullOrEmpty(RequiredRole) || (RequiredRoles != null && RequiredRoles.Any()))
        {
            // 检查单个角色
            if (!string.IsNullOrEmpty(RequiredRole))
            {
                if (!userRoles.Any(role => role.Equals(RequiredRole, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }
            }

            // 检查多个角色
            if (RequiredRoles != null && RequiredRoles.Any())
            {
                if (RequireAllRoles)
                {
                    // 需要所有角色
                    if (!RequiredRoles.All(r => userRoles.Any(ur => ur.Equals(r, StringComparison.OrdinalIgnoreCase))))
                    {
                        return false;
                    }
                }
                else
                {
                    // 需要任一角色
                    if (!RequiredRoles.Any(r => userRoles.Any(ur => ur.Equals(r, StringComparison.OrdinalIgnoreCase))))
                    {
                        return false;
                    }
                }
            }
        }

        // 如果没有指定权限要求，且角色检查通过，则通过
        if (string.IsNullOrEmpty(RequiredPermission) &&
            (RequiredPermissions == null || !RequiredPermissions.Any()))
        {
            return true;
        }

        // 检查是否为管理员角色，管理员拥有所有权限
        if (userRoles.Any(role =>
            role.Equals(RoleCodes.Administrator, StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        // 获取用户权限
        var userPermissions = user.Claims
            .Where(c => c.Type == "Permission")
            .Select(c => c.Value)
            .ToList();

        // 检查单个权限
        if (!string.IsNullOrEmpty(RequiredPermission))
        {
            return userPermissions.Contains(RequiredPermission);
        }

        // 检查多个权限
        if (RequiredPermissions != null && RequiredPermissions.Any())
        {
            if (RequireAllPermissions)
            {
                // 需要所有权限
                return RequiredPermissions.All(p => userPermissions.Contains(p));
            }
            else
            {
                // 需要任一权限
                return RequiredPermissions.Any(p => userPermissions.Contains(p));
            }
        }

        return false;
    }
}