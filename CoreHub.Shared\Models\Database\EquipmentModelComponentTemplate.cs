using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备型号标准部件模板实体
    /// </summary>
    [SugarTable("EquipmentModelComponentTemplates")]
    public class EquipmentModelComponentTemplate
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 设备型号ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备型号不能为空")]
        public int EquipmentModelId { get; set; }

        /// <summary>
        /// 部件ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "部件不能为空")]
        public int ComponentId { get; set; }

        /// <summary>
        /// 标准数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "标准数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "标准数量必须大于0")]
        public int StandardQuantity { get; set; } = 1;

        /// <summary>
        /// 是否必需部件
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// 更换周期（天）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ReplacementCycleDays { get; set; }

        /// <summary>
        /// 维护说明
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "维护说明长度不能超过500个字符")]
        public string? MaintenanceNotes { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 设备型号
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public EquipmentModel? EquipmentModel { get; set; }

        /// <summary>
        /// 部件信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Component? Component { get; set; }

        /// <summary>
        /// 部件类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ComponentTypeName => IsRequired ? "必需部件" : "可选部件";

        /// <summary>
        /// 更换周期描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ReplacementCycleDescription
        {
            get
            {
                if (!ReplacementCycleDays.HasValue)
                    return "无固定周期";
                
                if (ReplacementCycleDays.Value < 30)
                    return $"{ReplacementCycleDays.Value}天";
                else if (ReplacementCycleDays.Value < 365)
                    return $"{ReplacementCycleDays.Value / 30}个月";
                else
                    return $"{ReplacementCycleDays.Value / 365}年";
            }
        }
    }
}
