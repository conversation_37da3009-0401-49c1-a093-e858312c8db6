using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 部件信息DTO
    /// </summary>
    public class ComponentDto
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 部件编码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部件名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 型号规格
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 规格参数
        /// </summary>
        public string? Specifications { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? Supplier { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 库存数量
        /// </summary>
        public int StockQuantity { get; set; }

        /// <summary>
        /// 最小库存量
        /// </summary>
        public int MinStockQuantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 部件描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 库存状态名称
        /// </summary>
        public string StockStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 库存状态颜色
        /// </summary>
        public string StockStatusColor { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 从实体转换为DTO
        /// </summary>
        public static ComponentDto FromEntity(Component entity, ComponentCategory? category = null)
        {
            return new ComponentDto
            {
                Id = entity.Id,
                Code = entity.Code,
                Name = entity.Name,
                CategoryId = entity.CategoryId,
                CategoryName = category?.Name ?? entity.Category?.Name ?? "",
                Model = entity.Model,
                Specifications = entity.Specifications,
                Supplier = entity.Supplier,
                Brand = entity.Brand,
                Unit = entity.Unit,
                StockQuantity = entity.StockQuantity,
                MinStockQuantity = entity.MinStockQuantity,
                UnitPrice = entity.UnitPrice,
                Description = entity.Description,
                IsEnabled = entity.IsEnabled,
                StockStatusName = entity.StockStatusName,
                StockStatusColor = entity.StockStatusColor,
                CreatedAt = entity.CreatedAt,
                Remark = entity.Remark
            };
        }

        /// <summary>
        /// 转换为实体
        /// </summary>
        public Component ToEntity()
        {
            return new Component
            {
                Id = Id,
                Code = Code,
                Name = Name,
                CategoryId = CategoryId,
                Model = Model,
                Specifications = Specifications,
                Supplier = Supplier,
                Brand = Brand,
                Unit = Unit,
                StockQuantity = StockQuantity,
                MinStockQuantity = MinStockQuantity,
                UnitPrice = UnitPrice,
                Description = Description,
                IsEnabled = IsEnabled,
                Remark = Remark
            };
        }
    }
}
