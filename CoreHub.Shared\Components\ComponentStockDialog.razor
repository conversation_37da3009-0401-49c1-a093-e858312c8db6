@using CoreHub.Shared.Services
@inject IComponentService ComponentService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">
                    部件库存管理 - @ComponentName
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6">
                <MudTextField Label="当前库存"
                            Value="@CurrentStock.ToString()"
                            ReadOnly="true"
                            Adornment="Adornment.End"
                            AdornmentText="@Unit" />
            </MudItem>
            <MudItem xs="12" md="6">
                <MudNumericField @bind-Value="newStock"
                               Label="新库存数量"
                               Min="0"
                               Required="true"
                               Adornment="Adornment.End"
                               AdornmentText="@Unit" />
            </MudItem>
            <MudItem xs="12">
                <MudTextField @bind-Value="reason"
                            Label="调整原因"
                            Placeholder="请输入库存调整原因"
                            Required="true"
                            Lines="3" />
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@isSubmitting">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">更新中...</MudText>
            }
            else
            {
                <MudText>更新库存</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public int ComponentId { get; set; }
    [Parameter] public string ComponentName { get; set; } = string.Empty;
    [Parameter] public int CurrentStock { get; set; }
    [Parameter] public string Unit { get; set; } = string.Empty;

    private int newStock;
    private string reason = string.Empty;
    private bool isSubmitting = false;

    protected override void OnInitialized()
    {
        newStock = CurrentStock;
    }

    private async Task Submit()
    {
        if (string.IsNullOrWhiteSpace(reason))
        {
            Snackbar.Add("请输入调整原因", Severity.Error);
            return;
        }

        try
        {
            isSubmitting = true;
            StateHasChanged();

            var result = await ComponentService.UpdateStockQuantityAsync(ComponentId, newStock, reason);
            if (result.IsSuccess)
            {
                Snackbar.Add("库存更新成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add(result.ErrorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"更新失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
