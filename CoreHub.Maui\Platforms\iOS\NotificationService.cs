using CoreHub.Shared.Services;

namespace CoreHub.Platforms.iOS
{
    public class NotificationService : INotificationService
    {
        public async Task<bool> RequestPermissionAsync()
        {
            // iOS通知权限请求的简单实现
            return await Task.FromResult(true);
        }

        public async Task SendNotificationAsync(string title, string message)
        {
            // iOS通知发送的简单实现
            System.Diagnostics.Debug.WriteLine($"iOS通知: {title} - {message}");
            await Task.CompletedTask;
        }
    }
} 