using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 部件分类实体
    /// </summary>
    [SugarTable("ComponentCategories")]
    public class ComponentCategory
    {
        /// <summary>
        /// 分类ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 分类编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "分类编码不能为空")]
        [StringLength(50, ErrorMessage = "分类编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 分类名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 父级分类ID（支持层级结构）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 层级级别（1=一级分类，2=二级分类，以此类推）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序顺序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 分类描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 父级分类
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(ParentId))]
        public ComponentCategory? Parent { get; set; }

        /// <summary>
        /// 子级分类列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(ComponentCategory.ParentId))]
        public List<ComponentCategory> Children { get; set; } = new List<ComponentCategory>();

        /// <summary>
        /// 该分类下的部件列表
        /// </summary>
        [Navigate(NavigateType.OneToMany, nameof(Component.CategoryId))]
        public List<Component> Components { get; set; } = new List<Component>();

        /// <summary>
        /// 完整路径名称（包含父级分类）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FullName
        {
            get
            {
                if (Parent != null)
                {
                    return $"{Parent.Name} - {Name}";
                }
                return Name;
            }
        }
    }
}
