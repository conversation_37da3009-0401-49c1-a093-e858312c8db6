using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;
using System.Linq;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 设备部件批量管理服务辅助方法
    /// </summary>
    public partial class EquipmentComponentBatchService
    {
        #region 验证方法

        private async Task<BatchValidationResultDto> ValidateApplyTemplateRequestAsync(ApplyTemplateToEquipmentsRequestDto request)
        {
            var result = new BatchValidationResultDto { IsValid = true };

            // 验证设备型号是否存在
            var model = await _dbContext.Db.Queryable<EquipmentModel>()
                .Where(em => em.Id == request.EquipmentModelId && em.IsEnabled)
                .FirstAsync();

            if (model == null)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "MODEL_NOT_FOUND",
                    ErrorMessage = "设备型号不存在或已禁用",
                    RelatedId = request.EquipmentModelId.ToString()
                });
            }

            // 验证设备是否存在且属于指定型号
            var equipments = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => request.EquipmentIds.Contains(e.Id) && e.IsEnabled)
                .ToListAsync();

            var invalidEquipments = equipments.Where(e => e.ModelId != request.EquipmentModelId).ToList();
            foreach (var equipment in invalidEquipments)
            {
                result.Warnings.Add(new ValidationWarningDto
                {
                    WarningCode = "MODEL_MISMATCH",
                    WarningMessage = $"设备 {equipment.Name} 的型号与模板不匹配",
                    RelatedId = equipment.Id.ToString(),
                    RelatedName = equipment.Name,
                    SuggestedAction = "请选择相同型号的设备或更换模板"
                });
            }

            // 验证模板是否存在
            var templateCount = await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                .Where(t => t.EquipmentModelId == request.EquipmentModelId && t.IsEnabled)
                .CountAsync();

            if (templateCount == 0)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "NO_TEMPLATE",
                    ErrorMessage = "该设备型号没有可用的部件模板",
                    RelatedId = request.EquipmentModelId.ToString()
                });
            }

            result.ValidOperationCount = equipments.Count - invalidEquipments.Count;
            result.InvalidOperationCount = invalidEquipments.Count;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }

        private async Task<BatchValidationResultDto> ValidateCopyComponentsRequestAsync(CopyComponentsFromEquipmentRequestDto request)
        {
            var result = new BatchValidationResultDto { IsValid = true };

            // 验证源设备是否存在
            var sourceEquipment = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => e.Id == request.SourceEquipmentId && e.IsEnabled)
                .FirstAsync();

            if (sourceEquipment == null)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "SOURCE_NOT_FOUND",
                    ErrorMessage = "源设备不存在或已禁用",
                    RelatedId = request.SourceEquipmentId.ToString()
                });
            }

            // 验证目标设备是否存在
            var targetEquipments = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => request.TargetEquipmentIds.Contains(e.Id) && e.IsEnabled)
                .ToListAsync();

            var missingEquipments = request.TargetEquipmentIds.Except(targetEquipments.Select(e => e.Id)).ToList();
            foreach (var missingId in missingEquipments)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "TARGET_NOT_FOUND",
                    ErrorMessage = $"目标设备 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证型号兼容性
            if (sourceEquipment != null)
            {
                var incompatibleEquipments = targetEquipments.Where(e => e.ModelId != sourceEquipment.ModelId).ToList();
                foreach (var equipment in incompatibleEquipments)
                {
                    result.Warnings.Add(new ValidationWarningDto
                    {
                        WarningCode = "MODEL_MISMATCH",
                        WarningMessage = $"目标设备 {equipment.Name} 与源设备型号不匹配",
                        RelatedId = equipment.Id.ToString(),
                        RelatedName = equipment.Name,
                        SuggestedAction = "建议选择相同型号的设备进行复制"
                    });
                }
            }

            result.ValidOperationCount = targetEquipments.Count;
            result.InvalidOperationCount = missingEquipments.Count;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }

        private async Task<BatchValidationResultDto> ValidateBatchAddComponentsRequestAsync(BatchAddComponentsRequestDto request)
        {
            var result = new BatchValidationResultDto { IsValid = true };

            // 验证设备是否存在
            var equipments = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => request.EquipmentIds.Contains(e.Id) && e.IsEnabled)
                .ToListAsync();

            var missingEquipments = request.EquipmentIds.Except(equipments.Select(e => e.Id)).ToList();
            foreach (var missingId in missingEquipments)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "EQUIPMENT_NOT_FOUND",
                    ErrorMessage = $"设备 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证部件是否存在
            var componentIds = request.ComponentConfigurations.Select(c => c.ComponentId).ToList();
            var components = await _dbContext.Db.Queryable<Component>()
                .Where(c => componentIds.Contains(c.Id) && c.IsEnabled)
                .ToListAsync();

            var missingComponents = componentIds.Except(components.Select(c => c.Id)).ToList();
            foreach (var missingId in missingComponents)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "COMPONENT_NOT_FOUND",
                    ErrorMessage = $"部件 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证配置参数
            foreach (var config in request.ComponentConfigurations)
            {
                if (config.StandardQuantity <= 0)
                {
                    result.Errors.Add(new ValidationErrorDto
                    {
                        ErrorCode = "INVALID_QUANTITY",
                        ErrorMessage = $"部件 {config.ComponentId} 的标准数量必须大于0",
                        RelatedId = config.ComponentId.ToString()
                    });
                }
            }

            result.ValidOperationCount = equipments.Count;
            result.InvalidOperationCount = missingEquipments.Count;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }

        private async Task<BatchValidationResultDto> ValidateBatchRemoveComponentsRequestAsync(BatchRemoveComponentsRequestDto request)
        {
            var result = new BatchValidationResultDto { IsValid = true };

            // 验证设备是否存在
            var equipments = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => request.EquipmentIds.Contains(e.Id) && e.IsEnabled)
                .ToListAsync();

            var missingEquipments = request.EquipmentIds.Except(equipments.Select(e => e.Id)).ToList();
            foreach (var missingId in missingEquipments)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "EQUIPMENT_NOT_FOUND",
                    ErrorMessage = $"设备 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证部件是否存在
            var components = await _dbContext.Db.Queryable<Component>()
                .Where(c => request.ComponentIds.Contains(c.Id) && c.IsEnabled)
                .ToListAsync();

            var missingComponents = request.ComponentIds.Except(components.Select(c => c.Id)).ToList();
            foreach (var missingId in missingComponents)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "COMPONENT_NOT_FOUND",
                    ErrorMessage = $"部件 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            result.ValidOperationCount = equipments.Count;
            result.InvalidOperationCount = missingEquipments.Count;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }

        private async Task<BatchValidationResultDto> ValidateBatchUpdateComponentsRequestAsync(BatchUpdateComponentsRequestDto request)
        {
            var result = new BatchValidationResultDto { IsValid = true };

            // 验证设备是否存在
            var equipments = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => request.EquipmentIds.Contains(e.Id) && e.IsEnabled)
                .ToListAsync();

            var missingEquipments = request.EquipmentIds.Except(equipments.Select(e => e.Id)).ToList();
            foreach (var missingId in missingEquipments)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "EQUIPMENT_NOT_FOUND",
                    ErrorMessage = $"设备 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证部件是否存在
            var componentIds = request.ComponentConfigurations.Select(c => c.ComponentId).ToList();
            var components = await _dbContext.Db.Queryable<Component>()
                .Where(c => componentIds.Contains(c.Id) && c.IsEnabled)
                .ToListAsync();

            var missingComponents = componentIds.Except(components.Select(c => c.Id)).ToList();
            foreach (var missingId in missingComponents)
            {
                result.Errors.Add(new ValidationErrorDto
                {
                    ErrorCode = "COMPONENT_NOT_FOUND",
                    ErrorMessage = $"部件 {missingId} 不存在或已禁用",
                    RelatedId = missingId.ToString()
                });
            }

            // 验证配置参数
            foreach (var config in request.ComponentConfigurations)
            {
                if (config.StandardQuantity <= 0)
                {
                    result.Errors.Add(new ValidationErrorDto
                    {
                        ErrorCode = "INVALID_QUANTITY",
                        ErrorMessage = $"部件 {config.ComponentId} 的标准数量必须大于0",
                        RelatedId = config.ComponentId.ToString()
                    });
                }
            }

            result.ValidOperationCount = equipments.Count;
            result.InvalidOperationCount = missingEquipments.Count;
            result.IsValid = result.Errors.Count == 0;

            return result;
        }

        #endregion

        #region 数据获取方法

        private async Task<List<EquipmentModelComponentTemplate>> GetEquipmentModelTemplatesAsync(int equipmentModelId, List<int> componentIds, bool onlyRequired)
        {
            var query = _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                .Where(t => t.EquipmentModelId == equipmentModelId && t.IsEnabled);

            if (componentIds.Any())
            {
                query = query.Where(t => componentIds.Contains(t.ComponentId));
            }

            if (onlyRequired)
            {
                query = query.Where(t => t.IsRequired);
            }

            return await query.ToListAsync();
        }

        private async Task<List<Equipment>> GetValidEquipmentsAsync(List<int> equipmentIds, int? requiredModelId = null)
        {
            var query = _dbContext.Db.Queryable<Equipment>()
                .Where(e => equipmentIds.Contains(e.Id) && e.IsEnabled);

            if (requiredModelId.HasValue)
            {
                query = query.Where(e => e.ModelId == requiredModelId.Value);
            }

            return await query.ToListAsync();
        }

        private async Task<List<ComponentConfigurationDto>> GetEquipmentComponentConfigurationsAsync(int equipmentId, List<int>? componentIds = null)
        {
            // 从设备型号模板获取部件配置
            var equipment = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => e.Id == equipmentId)
                .FirstAsync();

            if (equipment == null || equipment.ModelId == 0)
            {
                return new List<ComponentConfigurationDto>();
            }

            var query = _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                .Where(t => t.EquipmentModelId == equipment.ModelId && t.IsEnabled);

            if (componentIds?.Any() == true)
            {
                query = query.Where(t => componentIds.Contains(t.ComponentId));
            }

            var templates = await query.ToListAsync();

            return templates.Select(t => new ComponentConfigurationDto
            {
                ComponentId = t.ComponentId,
                StandardQuantity = t.StandardQuantity,
                IsRequired = t.IsRequired,
                ReplacementCycleDays = t.ReplacementCycleDays,
                MaintenanceNotes = t.MaintenanceNotes,
                SortOrder = t.SortOrder
            }).ToList();
        }

        private async Task<int> GetEquipmentComponentCountAsync(int equipmentId)
        {
            var equipment = await _dbContext.Db.Queryable<Equipment>()
                .Where(e => e.Id == equipmentId)
                .FirstAsync();

            if (equipment == null || equipment.ModelId == 0)
            {
                return 0;
            }

            return await _dbContext.Db.Queryable<EquipmentModelComponentTemplate>()
                .Where(t => t.EquipmentModelId == equipment.ModelId && t.IsEnabled)
                .CountAsync();
        }

        #endregion

        #region 操作执行方法

        private async Task<BatchOperationItemResultDto> ApplyTemplatesToSingleEquipmentAsync(Equipment equipment, List<EquipmentModelComponentTemplate> templates, bool overrideExisting)
        {
            var result = new BatchOperationItemResultDto
            {
                ItemId = equipment.Id.ToString(),
                ItemName = equipment.Name,
                OperationType = "应用模板"
            };

            try
            {
                var addedCount = 0;
                var updatedCount = 0;
                var skippedCount = 0;

                foreach (var template in templates)
                {
                    // 检查是否已存在该设备-部件关联
                    var existingComponent = await _dbContext.Db.Queryable<EquipmentComponent>()
                        .Where(ec => ec.EquipmentId == equipment.Id && ec.ComponentId == template.ComponentId)
                        .FirstAsync();

                    if (existingComponent != null)
                    {
                        if (overrideExisting)
                        {
                            // 更新现有关联
                            existingComponent.Quantity = template.StandardQuantity;
                            existingComponent.IsRequired = template.IsRequired;
                            existingComponent.ReplacementCycleDays = template.ReplacementCycleDays;
                            existingComponent.MaintenanceNotes = template.MaintenanceNotes;
                            existingComponent.SortOrder = template.SortOrder;
                            existingComponent.UpdatedAt = DateTime.Now;
                            existingComponent.Remark = $"从模板更新: {DateTime.Now:yyyy-MM-dd HH:mm}";

                            await _dbContext.Db.Updateable(existingComponent).ExecuteCommandAsync();
                            updatedCount++;
                        }
                        else
                        {
                            skippedCount++;
                        }
                    }
                    else
                    {
                        // 创建新的设备-部件关联
                        var equipmentComponent = new EquipmentComponent
                        {
                            EquipmentId = equipment.Id,
                            ComponentId = template.ComponentId,
                            Quantity = template.StandardQuantity,
                            IsRequired = template.IsRequired,
                            InstallDate = DateTime.Now,
                            ReplacementCycleDays = template.ReplacementCycleDays,
                            MaintenanceNotes = template.MaintenanceNotes,
                            Status = 1, // 正常状态
                            SortOrder = template.SortOrder,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now,
                            Remark = $"从模板应用: {DateTime.Now:yyyy-MM-dd HH:mm}"
                        };

                        // 计算下次更换日期
                        if (template.ReplacementCycleDays.HasValue && template.ReplacementCycleDays.Value > 0)
                        {
                            equipmentComponent.NextReplacementDate = DateTime.Now.AddDays(template.ReplacementCycleDays.Value);
                        }

                        await _dbContext.Db.Insertable(equipmentComponent).ExecuteReturnIdentityAsync();
                        addedCount++;
                    }
                }

                result.IsSuccess = true;
                result.ErrorMessage = $"新增: {addedCount}, 更新: {updatedCount}, 跳过: {skippedCount}";

                _logger.LogInformation("成功为设备 {equipmentName} 应用模板: 新增{added}, 更新{updated}, 跳过{skipped}",
                    equipment.Name, addedCount, updatedCount, skippedCount);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "为设备 {equipmentName} 应用模板失败", equipment.Name);
            }

            return result;
        }

        private async Task<BatchOperationItemResultDto> CopyConfigurationsToSingleEquipmentAsync(Equipment equipment, List<ComponentConfigurationDto> configurations, bool overrideExisting)
        {
            var result = new BatchOperationItemResultDto
            {
                ItemId = equipment.Id.ToString(),
                ItemName = equipment.Name,
                OperationType = "复制配置"
            };

            try
            {
                // 这里可以实现具体的配置复制逻辑

                // 模拟操作成功
                await Task.CompletedTask;
                result.IsSuccess = true;
                _logger.LogInformation("成功为设备 {equipmentName} 复制了 {count} 个部件配置", equipment.Name, configurations.Count);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "为设备 {equipmentName} 复制配置失败", equipment.Name);
            }

            return result;
        }

        private async Task<BatchOperationItemResultDto> AddComponentsToSingleEquipmentAsync(Equipment equipment, List<ComponentConfigurationDto> configurations, bool overrideExisting)
        {
            var result = new BatchOperationItemResultDto
            {
                ItemId = equipment.Id.ToString(),
                ItemName = equipment.Name,
                OperationType = "添加部件"
            };

            try
            {
                // 这里可以实现具体的部件添加逻辑

                // 模拟操作成功
                await Task.CompletedTask;
                result.IsSuccess = true;
                _logger.LogInformation("成功为设备 {equipmentName} 添加了 {count} 个部件", equipment.Name, configurations.Count);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "为设备 {equipmentName} 添加部件失败", equipment.Name);
            }

            return result;
        }

        private async Task<BatchOperationItemResultDto> RemoveComponentsFromSingleEquipmentAsync(Equipment equipment, List<int> componentIds)
        {
            var result = new BatchOperationItemResultDto
            {
                ItemId = equipment.Id.ToString(),
                ItemName = equipment.Name,
                OperationType = "移除部件"
            };

            try
            {
                // 这里可以实现具体的部件移除逻辑

                // 模拟操作成功
                await Task.CompletedTask;
                result.IsSuccess = true;
                _logger.LogInformation("成功从设备 {equipmentName} 移除了 {count} 个部件", equipment.Name, componentIds.Count);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "从设备 {equipmentName} 移除部件失败", equipment.Name);
            }

            return result;
        }

        private async Task<BatchOperationItemResultDto> UpdateComponentsForSingleEquipmentAsync(Equipment equipment, List<ComponentConfigurationDto> configurations)
        {
            var result = new BatchOperationItemResultDto
            {
                ItemId = equipment.Id.ToString(),
                ItemName = equipment.Name,
                OperationType = "更新配置"
            };

            try
            {
                // 这里可以实现具体的配置更新逻辑

                // 模拟操作成功
                await Task.CompletedTask;
                result.IsSuccess = true;
                _logger.LogInformation("成功更新设备 {equipmentName} 的 {count} 个部件配置", equipment.Name, configurations.Count);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, "更新设备 {equipmentName} 的部件配置失败", equipment.Name);
            }

            return result;
        }

        #endregion

        #region 影响预览方法

        private async Task<BatchImpactPreviewDto> GetApplyTemplateImpactAsync(ApplyTemplateToEquipmentsRequestDto request)
        {
            var impact = new BatchImpactPreviewDto
            {
                OperationType = "应用模板"
            };

            try
            {
                var templates = await GetEquipmentModelTemplatesAsync(request.EquipmentModelId, request.ComponentIds, request.OnlyRequiredComponents);
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds, request.EquipmentModelId);

                impact.AffectedEquipmentCount = equipments.Count;
                impact.AffectedComponentCount = templates.Count;
                impact.NewConfigurationCount = equipments.Count * templates.Count;
                impact.EstimatedExecutionTimeSeconds = Math.Max(5, equipments.Count * templates.Count / 10);
                impact.Summary = $"将为 {equipments.Count} 台设备应用 {templates.Count} 个部件模板，预计新增 {impact.NewConfigurationCount} 个配置";

                return impact;
            }
            catch (Exception ex)
            {
                impact.Summary = $"获取影响预览失败: {ex.Message}";
                return impact;
            }
        }

        private async Task<BatchImpactPreviewDto> GetCopyComponentsImpactAsync(CopyComponentsFromEquipmentRequestDto request)
        {
            var impact = new BatchImpactPreviewDto
            {
                OperationType = "复制配置"
            };

            try
            {
                var sourceConfigurations = await GetEquipmentComponentConfigurationsAsync(request.SourceEquipmentId, request.ComponentIds);
                var targetEquipments = await GetValidEquipmentsAsync(request.TargetEquipmentIds);

                impact.AffectedEquipmentCount = targetEquipments.Count;
                impact.AffectedComponentCount = sourceConfigurations.Count;
                impact.NewConfigurationCount = targetEquipments.Count * sourceConfigurations.Count;
                impact.EstimatedExecutionTimeSeconds = Math.Max(3, targetEquipments.Count * sourceConfigurations.Count / 15);
                impact.Summary = $"将从源设备复制 {sourceConfigurations.Count} 个部件配置到 {targetEquipments.Count} 台目标设备";

                return impact;
            }
            catch (Exception ex)
            {
                impact.Summary = $"获取影响预览失败: {ex.Message}";
                return impact;
            }
        }

        private async Task<BatchImpactPreviewDto> GetBatchAddImpactAsync(BatchAddComponentsRequestDto request)
        {
            var impact = new BatchImpactPreviewDto
            {
                OperationType = "批量添加"
            };

            try
            {
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                impact.AffectedEquipmentCount = equipments.Count;
                impact.AffectedComponentCount = request.ComponentConfigurations.Count;
                impact.NewConfigurationCount = equipments.Count * request.ComponentConfigurations.Count;
                impact.EstimatedExecutionTimeSeconds = Math.Max(3, impact.NewConfigurationCount / 20);
                impact.Summary = $"将为 {equipments.Count} 台设备添加 {request.ComponentConfigurations.Count} 种部件配置";

                return impact;
            }
            catch (Exception ex)
            {
                impact.Summary = $"获取影响预览失败: {ex.Message}";
                return impact;
            }
        }

        private async Task<BatchImpactPreviewDto> GetBatchRemoveImpactAsync(BatchRemoveComponentsRequestDto request)
        {
            var impact = new BatchImpactPreviewDto
            {
                OperationType = "批量移除"
            };

            try
            {
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                impact.AffectedEquipmentCount = equipments.Count;
                impact.AffectedComponentCount = request.ComponentIds.Count;
                impact.DeletedConfigurationCount = equipments.Count * request.ComponentIds.Count;
                impact.EstimatedExecutionTimeSeconds = Math.Max(2, impact.DeletedConfigurationCount / 30);
                impact.Summary = $"将从 {equipments.Count} 台设备移除 {request.ComponentIds.Count} 种部件配置";

                return impact;
            }
            catch (Exception ex)
            {
                impact.Summary = $"获取影响预览失败: {ex.Message}";
                return impact;
            }
        }

        private async Task<BatchImpactPreviewDto> GetBatchUpdateImpactAsync(BatchUpdateComponentsRequestDto request)
        {
            var impact = new BatchImpactPreviewDto
            {
                OperationType = "批量更新"
            };

            try
            {
                var equipments = await GetValidEquipmentsAsync(request.EquipmentIds);

                impact.AffectedEquipmentCount = equipments.Count;
                impact.AffectedComponentCount = request.ComponentConfigurations.Count;
                impact.UpdatedConfigurationCount = equipments.Count * request.ComponentConfigurations.Count;
                impact.EstimatedExecutionTimeSeconds = Math.Max(3, impact.UpdatedConfigurationCount / 25);
                impact.Summary = $"将更新 {equipments.Count} 台设备的 {request.ComponentConfigurations.Count} 种部件配置";

                return impact;
            }
            catch (Exception ex)
            {
                impact.Summary = $"获取影响预览失败: {ex.Message}";
                return impact;
            }
        }

        #endregion
    }
}
