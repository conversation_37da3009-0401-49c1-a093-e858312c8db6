@echo off
echo ========================================
echo    CoreHub - Clear Environment Variables
echo ========================================
echo.

echo WARNING: This will clear all CoreHub-specific environment variables!
echo The application will use default configuration from appsettings.json
echo Other projects will NOT be affected.
echo.
set /p confirm=Are you sure? (y/N):
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Clearing CoreHub-specific environment variables...

setx COREHUB_ENVIRONMENT "" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENVIRONMENT cleared
) else (
    echo [ERROR] Failed to clear COREHUB_ENVIRONMENT
)

setx COREHUB_API_BASE_URL "" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_API_BASE_URL cleared
) else (
    echo [ERROR] Failed to clear COREHUB_API_BASE_URL
)

setx COREHUB_DB_CONNECTION_STRING "" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_DB_CONNECTION_STRING cleared
) else (
    echo [ERROR] Failed to clear COREHUB_DB_CONNECTION_STRING
)

setx COREHUB_USE_HTTPS_REDIRECTION "" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_USE_HTTPS_REDIRECTION cleared
) else (
    echo [ERROR] Failed to clear COREHUB_USE_HTTPS_REDIRECTION
)

setx COREHUB_ENABLE_VERBOSE_LOGGING "" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] COREHUB_ENABLE_VERBOSE_LOGGING cleared
) else (
    echo [ERROR] Failed to clear COREHUB_ENABLE_VERBOSE_LOGGING
)

echo.
echo ========================================
echo CoreHub environment variables cleared!
echo ========================================
echo.
echo The CoreHub application will now use default configuration
echo from appsettings.json and appsettings.{Environment}.json
echo.
echo ADVANTAGE: Other projects are completely unaffected!
echo.
echo IMPORTANT: Please restart your application
echo for the changes to take effect:
echo - Visual Studio: Restart Visual Studio
echo - Command Line: Open new command window
echo - IIS: Restart application pool
echo.
pause
