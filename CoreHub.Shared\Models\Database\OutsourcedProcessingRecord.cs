using SqlSugar;

namespace CoreHub.Shared.Models.Database;

/// <summary>
/// 委外加工记录实体
/// </summary>
[SugarTable("OutsourcedProcessingRecords")]
public class OutsourcedProcessingRecord
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 关联的零件申请ID
    /// </summary>
    public int PartRequestId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    [SugarColumn(Length = 100)]
    public string SupplierName { get; set; } = string.Empty;

    /// <summary>
    /// 供应商联系人
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true)]
    public string? SupplierContact { get; set; }

    /// <summary>
    /// 供应商电话
    /// </summary>
    [SugarColumn(Length = 20, IsNullable = true)]
    public string? SupplierPhone { get; set; }

    /// <summary>
    /// 加工要求
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true)]
    public string? ProcessingRequirements { get; set; }

    /// <summary>
    /// 加工费用
    /// </summary>
    [SugarColumn(DecimalDigits = 2, IsNullable = true)]
    public decimal? ProcessingCost { get; set; }

    /// <summary>
    /// 预计完成时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? EstimatedCompletionDate { get; set; }

    /// <summary>
    /// 实际完成时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? ActualCompletionDate { get; set; }

    /// <summary>
    /// 加工状态：1=已下单,2=加工中,3=待验收,4=已完成,5=已取消
    /// </summary>
    public int ProcessingStatus { get; set; } = 1;

    /// <summary>
    /// 创建人ID
    /// </summary>
    public int CreatedBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新人ID
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? UpdatedBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true)]
    public string? Remark { get; set; }

    // 导航属性
    /// <summary>
    /// 关联的零件申请记录
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public RepairOrderPartRequest? PartRequest { get; set; }

    /// <summary>
    /// 创建人信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Creator { get; set; }

    /// <summary>
    /// 更新人信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public User? Updater { get; set; }
}