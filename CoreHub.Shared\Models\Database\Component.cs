using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 设备部件实体
    /// </summary>
    [SugarTable("Components")]
    public class Component
    {
        /// <summary>
        /// 部件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 部件编码（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "部件编码不能为空")]
        [StringLength(50, ErrorMessage = "部件编码长度不能超过50个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部件名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "部件名称不能为空")]
        [StringLength(100, ErrorMessage = "部件名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部件分类ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "部件分类不能为空")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 型号规格
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "型号规格长度不能超过200个字符")]
        public string? Model { get; set; }

        /// <summary>
        /// 规格参数
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "规格参数长度不能超过500个字符")]
        public string? Specifications { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "供应商长度不能超过100个字符")]
        public string? Supplier { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "品牌长度不能超过100个字符")]
        public string? Brand { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "单位不能为空")]
        [StringLength(20, ErrorMessage = "单位长度不能超过20个字符")]
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 库存数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int StockQuantity { get; set; } = 0;

        /// <summary>
        /// 最小库存量（预警阈值）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MinStockQuantity { get; set; } = 0;

        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDataType = "decimal(18,2)", IsNullable = true)]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 部件描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "部件描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 所属分类
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(CategoryId))]
        public ComponentCategory? Category { get; set; }

        /// <summary>
        /// 标准部件模板列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<EquipmentModelComponentTemplate> ModelTemplates { get; set; } = new List<EquipmentModelComponentTemplate>();

        /// <summary>
        /// 库存状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StockStatusName
        {
            get
            {
                if (StockQuantity <= 0)
                    return "缺货";
                else if (StockQuantity <= MinStockQuantity)
                    return "库存不足";
                else
                    return "库存充足";
            }
        }

        /// <summary>
        /// 库存状态颜色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StockStatusColor
        {
            get
            {
                if (StockQuantity <= 0)
                    return "error";
                else if (StockQuantity <= MinStockQuantity)
                    return "warning";
                else
                    return "success";
            }
        }
    }
}
